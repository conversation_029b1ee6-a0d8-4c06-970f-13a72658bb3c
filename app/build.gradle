plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-kapt'
}

android {
    namespace 'com.example.castapp'
    compileSdk 35

    defaultConfig {
        applicationId "com.example.castapp"
        minSdk 24
        targetSdk 35
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
        freeCompilerArgs += [
                "-opt-in=kotlin.RequiresOptIn",
                "-opt-in=kotlin.ExperimentalStdlibApi"
        ]
    }
}

dependencies {
    // 更新后的核心依赖
    implementation 'androidx.core:core-ktx:1.13.1'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.8.7'
    implementation 'androidx.appcompat:appcompat:1.7.0'

    // Material Design 组件
    implementation 'com.google.android.material:material:1.12.0'

    // 添加RecyclerView依赖
    implementation 'androidx.recyclerview:recyclerview:1.3.2'

    // ViewModel 和 LiveData 的依赖
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:2.8.7"

    // Activity KTX 扩展
    implementation "androidx.activity:activity-ktx:1.9.3"

    // Fragment KTX 扩展
    implementation "androidx.fragment:fragment-ktx:1.8.5"

    // 网络和WebSocket依赖（您原有的重要依赖）
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okio:okio:3.9.1'
    implementation 'org.java-websocket:Java-WebSocket:1.5.7'
    implementation 'com.google.code.gson:gson:2.11.0'

    // 协程依赖
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0'

    // Room数据库依赖
    implementation "androidx.room:room-runtime:2.6.1"
    implementation "androidx.room:room-ktx:2.6.1"
    kapt "androidx.room:room-compiler:2.6.1"

    // 🎨 ColorPickerView - HSV色轮颜色选择器
    implementation 'com.github.skydoves:colorpickerview:2.3.0'

    // 测试依赖 - 使用稳定版本避免缓存问题
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
