package com.example.castapp

import com.example.castapp.model.CastWindowInfo
import org.junit.Test
import org.junit.Assert.*

/**
 * 精准控制功能测试
 */
class PrecisionControlTest {

    @Test
    fun testCastWindowInfoWithControlEnabled() {
        // 测试CastWindowInfo的isControlEnabled字段
        val windowInfo = CastWindowInfo(
            connectionId = "test123",
            ipAddress = "*************",
            port = 8080,
            isControlEnabled = true
        )

        assertTrue("调控功能应该被启用", windowInfo.isControlEnabled)
        assertEquals("连接ID应该正确", "test123", windowInfo.connectionId)
    }

    @Test
    fun testCastWindowInfoDefaultControlState() {
        // 测试默认的调控状态
        val windowInfo = CastWindowInfo(
            connectionId = "test456",
            ipAddress = "*************",
            port = 8080
        )

        assertFalse("调控功能默认应该被禁用", windowInfo.isControlEnabled)
    }

    @Test
    fun testTransformInfo() {
        // 测试变换信息的格式化
        val windowInfo = CastWindowInfo(
            connectionId = "test789",
            ipAddress = "*************",
            port = 8080,
            positionX = 100.5f,
            positionY = 200.3f,
            scaleFactor = 1.5f,
            rotationAngle = 45.7f
        )

        val transformInfo = windowInfo.getTransformInfo()
        assertTrue("变换信息应该包含位置", transformInfo.contains("位置（100,200）"))
        assertTrue("变换信息应该包含缩放", transformInfo.contains("缩放：1.5"))
        assertTrue("变换信息应该包含旋转", transformInfo.contains("旋转：45°"))
    }
}
