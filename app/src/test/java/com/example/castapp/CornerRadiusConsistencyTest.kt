package com.example.castapp

import android.util.TypedValue
import org.junit.Test
import org.junit.Assert.*

/**
 * 🎯 圆角半径一致性测试
 * 
 * 验证遥控端和接收端圆角半径效果的一致性修复
 */
class CornerRadiusConsistencyTest {

    /**
     * 模拟Android的TypedValue.applyDimension方法
     */
    private fun mockDpToPx(dp: Float, density: Float = 3.0f): Float {
        return dp * density
    }

    @Test
    fun testCornerRadiusCalculationConsistency() {
        // 🎯 测试圆角半径计算的一致性（包括Canvas绘制）
        
        val testCornerRadius = 16.5f // 测试非整数圆角半径
        val mockDensity = 3.0f // 模拟设备密度
        
        println("=== 圆角半径计算一致性测试 ===")
        println("测试圆角半径: ${testCornerRadius}dp")
        println("模拟设备密度: ${mockDensity}")
        
        // 🎯 遥控端计算方式（正确）
        val remoteRadiusPx = mockDpToPx(testCornerRadius, mockDensity)
        
        println("\n遥控端计算:")
        println("  dpToPx($testCornerRadius) = $remoteRadiusPx px")
        
        // 🎯 接收端修复前计算方式（有精度损失）
        val receiverRadiusPxBefore = mockDpToPx(testCornerRadius.toInt().toFloat(), mockDensity)
        
        println("\n接收端修复前:")
        println("  cornerRadius.toInt() = ${testCornerRadius.toInt()}")
        println("  dpToPx(${testCornerRadius.toInt()}.toFloat()) = $receiverRadiusPxBefore px")
        println("  精度损失: ${remoteRadiusPx - receiverRadiusPxBefore} px")
        
        // 🎯 接收端修复后计算方式（与遥控端一致）
        val receiverRadiusPxAfter = mockDpToPx(testCornerRadius, mockDensity)
        
        println("\n接收端修复后:")
        println("  dpToPxFloat($testCornerRadius) = $receiverRadiusPxAfter px")
        println("  与遥控端差异: ${Math.abs(remoteRadiusPx - receiverRadiusPxAfter)} px")
        
        // 验证修复效果
        assertNotEquals("修复前应该有精度损失", remoteRadiusPx, receiverRadiusPxBefore, 0.01f)
        assertEquals("修复后应该完全一致", remoteRadiusPx, receiverRadiusPxAfter, 0.01f)
        
        println("\n✅ 圆角半径计算一致性修复验证通过！")
    }

    @Test
    fun testCornerRadiusImplementationConsistency() {
        // 🎯 测试圆角实现方式的一致性修复

        val testCornerRadius = 16.5f
        val mockDensity = 3.0f

        println("=== 圆角实现方式一致性测试 ===")
        println("测试圆角半径: ${testCornerRadius}dp")

        // 🎯 修复前：遥控端双重圆角效果
        val remoteCanvasRadius = mockDpToPx(testCornerRadius, mockDensity)
        val remoteOutlineRadius = mockDpToPx(testCornerRadius, mockDensity)
        val remoteEffectiveBefore = "Canvas圆角 + Outline圆角 (双重效果)"

        // 🎯 修复后：遥控端单一圆角效果
        val remoteEffectiveAfter = "仅Outline圆角 (单一效果)"

        // 🎯 接收端：始终是单一圆角效果
        val receiverOutlineRadius = mockDpToPx(testCornerRadius, mockDensity)
        val receiverEffective = "仅Outline圆角 (单一效果)"

        println("\n遥控端圆角实现:")
        println("  修复前: $remoteEffectiveBefore")
        println("    Canvas: canvas.drawRoundRect(..., $remoteCanvasRadius, $remoteCanvasRadius, paint)")
        println("    Outline: outline.setRoundRect(..., $remoteOutlineRadius) + clipToOutline=true")
        println("  修复后: $remoteEffectiveAfter")
        println("    Canvas: canvas.drawRect(..., paint) (无圆角)")
        println("    Outline: outline.setRoundRect(..., $remoteOutlineRadius) + clipToOutline=true")

        println("\n接收端圆角实现:")
        println("  始终: $receiverEffective")
        println("    Outline: outline.setRoundRect(..., $receiverOutlineRadius) + clipToOutline=true")

        println("\n一致性对比:")
        println("  修复前: 遥控端双重圆角 vs 接收端单一圆角 (不一致)")
        println("  修复后: 遥控端单一圆角 vs 接收端单一圆角 (完全一致)")

        // 验证修复效果
        assertEquals("修复后Outline圆角半径应该完全一致", remoteOutlineRadius, receiverOutlineRadius, 0.01f)
        assertEquals("修复后圆角实现方式应该完全一致", remoteEffectiveAfter, receiverEffective)

        println("\n✅ 圆角实现方式一致性修复验证通过！")
    }

    @Test
    fun testScaleFactorImpactOnCornerRadius() {
        // 🎯 测试缩放因子对圆角半径的影响修复

        val baseCornerRadius = 16f
        val testScaleFactors = arrayOf(0.5f, 1.0f, 1.5f, 2.0f)
        val mockDensity = 3.0f

        println("=== 缩放因子对圆角半径影响测试 ===")
        println("基础圆角半径: ${baseCornerRadius}dp")
        println("测试缩放因子: ${testScaleFactors.contentToString()}")

        for (scaleFactor in testScaleFactors) {
            // 🎯 接收端：圆角半径会被容器缩放变换影响
            val receiverEffectiveRadius = baseCornerRadius * scaleFactor
            val receiverRadiusPx = mockDpToPx(receiverEffectiveRadius, mockDensity)

            // 🎯 遥控端修复前：圆角半径固定，不考虑缩放
            val remoteRadiusBeforePx = mockDpToPx(baseCornerRadius, mockDensity)

            // 🎯 遥控端修复后：圆角半径考虑缩放因子
            val remoteEffectiveRadius = baseCornerRadius * scaleFactor
            val remoteRadiusAfterPx = mockDpToPx(remoteEffectiveRadius, mockDensity)

            println("\n缩放因子: ${scaleFactor}x")
            println("  接收端有效圆角: ${baseCornerRadius}dp × ${scaleFactor} = ${receiverEffectiveRadius}dp (${receiverRadiusPx}px)")
            println("  遥控端修复前: ${baseCornerRadius}dp (${remoteRadiusBeforePx}px)")
            println("  遥控端修复后: ${baseCornerRadius}dp × ${scaleFactor} = ${remoteEffectiveRadius}dp (${remoteRadiusAfterPx}px)")
            println("  修复前差异: ${Math.abs(receiverRadiusPx - remoteRadiusBeforePx)}px")
            println("  修复后差异: ${Math.abs(receiverRadiusPx - remoteRadiusAfterPx)}px")

            // 验证修复效果
            if (scaleFactor != 1.0f) {
                assertNotEquals("缩放${scaleFactor}x时修复前应该有差异", receiverRadiusPx, remoteRadiusBeforePx, 0.01f)
            }
            assertEquals("缩放${scaleFactor}x时修复后应该完全一致", receiverRadiusPx, remoteRadiusAfterPx, 0.01f)
        }

        println("\n✅ 缩放因子对圆角半径影响修复验证通过！")
    }
    
    @Test
    fun testMultipleCornerRadiusValues() {
        // 🎯 测试多个圆角半径值的一致性
        
        val testValues = arrayOf(8.0f, 12.5f, 16.0f, 20.3f, 24.7f)
        val mockDensity = 2.5f
        
        println("=== 多个圆角半径值一致性测试 ===")
        println("测试值: ${testValues.contentToString()}")
        println("模拟设备密度: ${mockDensity}")
        
        for (cornerRadius in testValues) {
            // 遥控端计算
            val remoteRadiusPx = mockDpToPx(cornerRadius, mockDensity)
            
            // 接收端修复前计算
            val receiverRadiusPxBefore = mockDpToPx(cornerRadius.toInt().toFloat(), mockDensity)
            
            // 接收端修复后计算
            val receiverRadiusPxAfter = mockDpToPx(cornerRadius, mockDensity)
            
            println("\n圆角半径: ${cornerRadius}dp")
            println("  遥控端: $remoteRadiusPx px")
            println("  接收端修复前: $receiverRadiusPxBefore px (损失: ${remoteRadiusPx - receiverRadiusPxBefore} px)")
            println("  接收端修复后: $receiverRadiusPxAfter px (差异: ${Math.abs(remoteRadiusPx - receiverRadiusPxAfter)} px)")
            
            // 验证修复效果
            assertEquals("${cornerRadius}dp 修复后应该完全一致", remoteRadiusPx, receiverRadiusPxAfter, 0.01f)
        }
        
        println("\n✅ 多个圆角半径值一致性验证通过！")
    }
    
    @Test
    fun testEdgeCases() {
        // 🎯 测试边界情况
        
        val edgeCases = arrayOf(
            0.0f,    // 无圆角
            0.5f,    // 小数圆角
            1.0f,    // 整数圆角
            15.9f,   // 接近整数的小数
            16.0f,   // 整数圆角
            16.1f,   // 略大于整数的小数
            32.0f    // 大圆角
        )
        val mockDensity = 3.0f
        
        println("=== 边界情况测试 ===")
        println("测试边界值: ${edgeCases.contentToString()}")
        
        for (cornerRadius in edgeCases) {
            // 遥控端计算
            val remoteRadiusPx = mockDpToPx(cornerRadius, mockDensity)
            
            // 接收端修复后计算
            val receiverRadiusPxAfter = mockDpToPx(cornerRadius, mockDensity)
            
            println("\n边界值: ${cornerRadius}dp")
            println("  遥控端: $remoteRadiusPx px")
            println("  接收端修复后: $receiverRadiusPxAfter px")
            
            // 验证一致性
            assertEquals("边界值 ${cornerRadius}dp 应该完全一致", remoteRadiusPx, receiverRadiusPxAfter, 0.01f)
        }
        
        println("\n✅ 边界情况验证通过！")
    }
    
    @Test
    fun testPrecisionLossScenarios() {
        // 🎯 测试精度损失场景
        
        println("=== 精度损失场景分析 ===")
        
        // 找出会造成精度损失的圆角半径值
        val precisionLossValues = mutableListOf<Float>()
        
        for (i in 0..500) {
            val cornerRadius = i / 10.0f // 0.0, 0.1, 0.2, ..., 50.0
            val original = cornerRadius
            val afterIntConversion = cornerRadius.toInt().toFloat()
            
            if (original != afterIntConversion) {
                precisionLossValues.add(cornerRadius)
            }
        }
        
        println("发现 ${precisionLossValues.size} 个会造成精度损失的值")
        println("前10个示例: ${precisionLossValues.take(10)}")
        
        // 验证修复效果
        val mockDensity = 3.0f
        var maxPrecisionLoss = 0.0f
        
        for (cornerRadius in precisionLossValues.take(20)) { // 测试前20个
            val remoteRadiusPx = mockDpToPx(cornerRadius, mockDensity)
            val receiverRadiusPxBefore = mockDpToPx(cornerRadius.toInt().toFloat(), mockDensity)
            val receiverRadiusPxAfter = mockDpToPx(cornerRadius, mockDensity)
            
            val precisionLoss = Math.abs(remoteRadiusPx - receiverRadiusPxBefore)
            maxPrecisionLoss = Math.max(maxPrecisionLoss, precisionLoss)
            
            // 验证修复前有损失，修复后无损失
            assertTrue("${cornerRadius}dp 修复前应该有精度损失", precisionLoss > 0.01f)
            assertEquals("${cornerRadius}dp 修复后应该无精度损失", remoteRadiusPx, receiverRadiusPxAfter, 0.01f)
        }
        
        println("最大精度损失: ${maxPrecisionLoss} px")
        println("✅ 精度损失场景验证通过！")
    }
}
