<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#E0E0E0" />
            <corners android:radius="4dp" />
        </shape>
    </item>
    
    <!-- 禁用状态 -->
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="#F5F5F5" />
            <corners android:radius="4dp" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#F8F8F8" />
            <stroke android:width="1dp" android:color="#E0E0E0" />
            <corners android:radius="4dp" />
        </shape>
    </item>
</selector>
