<?xml version="1.0" encoding="utf-8"?>
<!-- 全覆盖橙色渐变 - 表示应用状态 -->
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 底层：保持原有背景 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#FFFFFF" />
            <corners android:radius="4dp" />
            <stroke android:width="1dp" android:color="#E0E0E0" />
        </shape>
    </item>

    <!-- 半透明橙色渐变覆盖层（覆盖整个按钮） -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#80FF9800"
                android:endColor="#80FFFFFF"
                android:angle="0" />
            <corners android:radius="4dp" />
        </shape>
    </item>
</layer-list>
