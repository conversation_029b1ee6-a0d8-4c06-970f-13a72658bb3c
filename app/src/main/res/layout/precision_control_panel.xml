<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/precision_control_background"
    android:padding="6dp"
    android:elevation="8dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="2dp">

        <!-- 拖拽手柄 -->
        <ImageView
            android:id="@+id/drag_handle"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_drag_handle"
            android:layout_marginEnd="4dp"
            android:contentDescription="拖拽调控面板" />

        <TextView
            android:id="@+id/tv_panel_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="设备名称"
            android:textSize="10sp"
            android:textStyle="bold"
            android:textColor="#FFFFFF"
            android:ellipsize="end"
            android:maxLines="1" />

        <!-- 关闭按钮 -->
        <ImageView
            android:id="@+id/btn_close_panel"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_close"
            android:tint="#FFFFFF"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="关闭精准调控面板" />

    </LinearLayout>

    <!-- 紧凑型控制区域 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- 坐标控制 -->
        <LinearLayout
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginEnd="2dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="坐标"
                android:textSize="10sp"
                android:textColor="#CCCCCC"
                android:layout_marginBottom="2dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/et_position_x"
                    android:layout_width="0dp"
                    android:layout_height="28dp"
                    android:layout_weight="1"
                    android:background="@drawable/edit_text_background"
                    android:textSize="10sp"
                    android:textColor="#333333"
                    android:textColorHint="#888888"
                    android:hint="X"
                    android:inputType="numberSigned|numberDecimal"
                    android:gravity="center"
                    android:layout_marginEnd="2dp" />

                <EditText
                    android:id="@+id/et_position_y"
                    android:layout_width="0dp"
                    android:layout_height="28dp"
                    android:layout_weight="1"
                    android:background="@drawable/edit_text_background"
                    android:textSize="10sp"
                    android:textColor="#333333"
                    android:textColorHint="#888888"
                    android:hint="Y"
                    android:inputType="numberSigned|numberDecimal"
                    android:gravity="center" />

            </LinearLayout>

        </LinearLayout>

        <!-- 缩放控制 -->
        <LinearLayout
            android:layout_width="40dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginEnd="2dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="缩放"
                android:textSize="10sp"
                android:textColor="#CCCCCC"
                android:layout_marginBottom="2dp" />

            <EditText
                android:id="@+id/et_scale_factor"
                android:layout_width="40dp"
                android:layout_height="28dp"
                android:background="@drawable/edit_text_background"
                android:textSize="10sp"
                android:textColor="#333333"
                android:textColorHint="#888888"
                android:hint="1.0"
                android:inputType="numberDecimal"
                android:gravity="center" />

        </LinearLayout>

        <!-- 旋转控制 -->
        <LinearLayout
            android:layout_width="30dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginEnd="4dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="旋转"
                android:textSize="10sp"
                android:textColor="#CCCCCC"
                android:layout_marginBottom="2dp" />

            <EditText
                android:id="@+id/et_rotation_angle"
                android:layout_width="30dp"
                android:layout_height="28dp"
                android:background="@drawable/edit_text_background"
                android:textSize="10sp"
                android:textColor="#333333"
                android:textColorHint="#888888"
                android:hint="0°"
                android:inputType="numberSigned|numberDecimal"
                android:gravity="center" />

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="70dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="操作"
                android:textSize="10sp"
                android:textColor="#CCCCCC"
                android:layout_marginBottom="2dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btn_apply_transform"
                    android:layout_width="0dp"
                    android:layout_height="28dp"
                    android:layout_weight="1"
                    android:text="应用"
                    android:textSize="9sp"
                    android:textColor="#FFFFFF"
                    android:background="@drawable/button_apply_background"
                    android:layout_marginEnd="4dp" />

                <Button
                    android:id="@+id/btn_reset_transform"
                    android:layout_width="0dp"
                    android:layout_height="28dp"
                    android:layout_weight="1"
                    android:text="重置"
                    android:textSize="9sp"
                    android:textColor="#FFFFFF"
                    android:background="@drawable/button_reset_background" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
