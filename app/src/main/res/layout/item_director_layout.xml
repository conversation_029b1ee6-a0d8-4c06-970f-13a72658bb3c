<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="4dp"
    android:layout_marginBottom="4dp"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center_vertical">

    <!-- 批量选择复选框 -->
    <CheckBox
        android:id="@+id/selection_checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="4dp"
        android:visibility="gone"
        android:clickable="false"
        android:focusable="false" />

    <!-- 布局信息容器 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/layout_name_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="会议室布局1"
            android:textSize="11sp"
            android:textColor="#2196F3"
            android:textStyle="bold"
            android:ellipsize="end"
            android:maxLines="1" />

        <TextView
            android:id="@+id/layout_date_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="2023-04-15 14:30"
            android:textSize="8sp"
            android:textColor="#757575"
            android:layout_marginTop="2dp" />

        <TextView
            android:id="@+id/layout_window_count_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="（2个窗口）"
            android:textSize="8sp"
            android:textColor="#757575"
            android:layout_marginTop="1dp" />

    </LinearLayout>

</LinearLayout>