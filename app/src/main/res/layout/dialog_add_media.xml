<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="4dp">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="添加媒体窗口"
        android:textSize="18sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="24dp" />

    <!-- 功能按钮组 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginBottom="24dp">

        <!-- 前置摄像头按钮 -->
        <LinearLayout
            android:id="@+id/btn_add_front_camera"
            android:layout_width="60dp"
            android:layout_height="65dp"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@drawable/button_background"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="8dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_add_camera"
                android:layout_marginBottom="8dp"
                app:tint="#FFFFFF" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="前置摄像头"
                android:textSize="10sp"
                android:textColor="#FFFFFF"
                android:gravity="center" />

        </LinearLayout>

        <!-- 后置摄像头按钮 -->
        <LinearLayout
            android:id="@+id/btn_add_rear_camera"
            android:layout_width="60dp"
            android:layout_height="65dp"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@drawable/button_background"
            android:layout_marginEnd="8dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_add_camera"
                android:layout_marginBottom="8dp"
                app:tint="#FFFFFF" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="后置摄像头"
                android:textSize="10sp"
                android:textColor="#FFFFFF"
                android:gravity="center" />

        </LinearLayout>

        <!-- 添加视频按钮 -->
        <LinearLayout
            android:id="@+id/btn_add_video"
            android:layout_width="45dp"
            android:layout_height="65dp"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@drawable/button_background"
            android:layout_marginEnd="8dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:src="@drawable/ic_add_video"
                android:layout_marginBottom="8dp"
                app:tint="#FFFFFF" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="视频"
                android:textSize="10sp"
                android:textColor="#FFFFFF"
                android:gravity="center" />

        </LinearLayout>

        <!-- 添加图片按钮 -->
        <LinearLayout
            android:id="@+id/btn_add_picture"
            android:layout_width="45dp"
            android:layout_height="65dp"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@drawable/button_background"
            android:layout_marginEnd="8dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:src="@drawable/ic_add_picture"
                android:layout_marginBottom="8dp"
                app:tint="#FFFFFF" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="图片"
                android:textSize="10sp"
                android:textColor="#FFFFFF"
                android:gravity="center" />

        </LinearLayout>

        <!-- 添加文本按钮 -->
        <LinearLayout
            android:id="@+id/btn_add_text"
            android:layout_width="45dp"
            android:layout_height="65dp"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@drawable/button_background"
            android:layout_marginEnd="4dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:src="@drawable/ic_add_text"
                android:layout_marginBottom="8dp"
                app:tint="#FFFFFF" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="文本"
                android:textSize="12sp"
                android:textColor="#FFFFFF"
                android:gravity="center" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
