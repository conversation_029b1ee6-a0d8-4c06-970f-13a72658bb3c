<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="保存当前窗口布局"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="布局名称"
        android:textStyle="bold" />

    <EditText
        android:id="@+id/layout_name_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:hint="请输入布局名称，如：会议室布局1"
        android:inputType="text"
        android:maxLines="1"
        android:minHeight="48dp" />

    <TextView
        android:id="@+id/window_count_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="当前有 0 个窗口将被保存"
        android:textColor="#757575" />

</LinearLayout>