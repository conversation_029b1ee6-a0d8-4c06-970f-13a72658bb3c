<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp"
    android:background="@android:color/white">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="📏 字间距设置"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:gravity="start" />

        <ImageView
            android:id="@+id/btn_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_close"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            app:tint="#666666" />

    </LinearLayout>

    <!-- 当前字间距显示和恢复默认 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="#F5F5F5"
        android:padding="4dp"
        android:layout_marginBottom="8dp"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tv_current_letter_spacing"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="当前字间距: 0.0em"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="#333333" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <Button
            android:id="@+id/btn_reset_default"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🔄 恢复默认"
            android:textSize="12sp"
            android:background="@null"
            android:textColor="#FF9800" />

    </LinearLayout>

    <!-- 字间距列表标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="可用字间距（点击选择，长按删除自定义字间距）"
        android:textSize="12sp"
        android:textColor="#666666"
        android:layout_marginBottom="8dp" />

    <!-- 字间距列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_letter_spacing_list"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:background="#FAFAFA"
        android:padding="8dp"
        android:layout_marginBottom="16dp" />

    <!-- 添加字间距区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="#F0F8FF"
        android:padding="4dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="添加字间距："
            android:textSize="13sp"
            android:textColor="#333333" />

        <EditText
            android:id="@+id/et_new_letter_spacing"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:hint="输入-0.5到2.0之间的数字"
            android:inputType="numberDecimal|numberSigned"
            android:textSize="12sp"
            android:background="@android:color/white"
            android:padding="8dp" />

        <Button
            android:id="@+id/btn_add_letter_spacing"
            android:layout_width="40dp"
            android:layout_height="38dp"
            android:text="添加"
            android:textSize="12sp"
            android:background="#4CAF50"
            android:textColor="@android:color/white"
            android:paddingStart="4dp"
            android:paddingEnd="4dp" />

    </LinearLayout>

</LinearLayout>
