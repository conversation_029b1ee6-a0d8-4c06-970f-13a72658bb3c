<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp"
    android:background="@android:color/white">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="📁 选择字体文件"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:gravity="start" />

        <ImageView
            android:id="@+id/btn_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_close"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            app:tint="#666666" />

    </LinearLayout>

    <!-- 当前路径显示 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="#F5F5F5"
        android:padding="8dp"
        android:layout_marginBottom="8dp"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="当前路径："
            android:textSize="12sp"
            android:textColor="#666666" />

        <TextView
            android:id="@+id/tv_current_path"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="/storage/emulated/0/Download"
            android:textSize="12sp"
            android:textColor="#333333"
            android:layout_marginStart="8dp"
            android:ellipsize="start"
            android:singleLine="true" />

    </LinearLayout>

    <!-- 文件列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_files"
        android:layout_width="match_parent"
        android:layout_height="400dp"
        android:background="#FAFAFA"
        android:padding="4dp" />

    <!-- 文件统计信息 -->
    <TextView
        android:id="@+id/tv_file_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="文件夹: 0, 字体文件: 0"
        android:textSize="11sp"
        android:textColor="#999999"
        android:gravity="center"
        android:layout_marginTop="4dp"
        android:padding="2dp" />

    <!-- 提示信息 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="💡 点击文件夹进入，点击.ttf/.otf文件选择"
        android:textSize="12sp"
        android:textColor="#666666"
        android:gravity="center"
        android:layout_marginTop="4dp"
        android:padding="4dp" />

</LinearLayout>
