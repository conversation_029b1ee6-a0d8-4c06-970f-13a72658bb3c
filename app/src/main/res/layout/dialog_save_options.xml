<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="320dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/dialog_background"
    android:padding="16dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="保存布局参数"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#2196F3" />

        <ImageButton
            android:id="@+id/btn_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_close"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:tint="#666666"
            android:contentDescription="关闭" />
    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0"
        android:layout_marginBottom="16dp"/>

    <!-- 说明文字 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="请选择保存模式："
        android:textSize="14sp"
        android:textColor="#333333"
        android:layout_marginBottom="12dp" />

    <!-- 保存选项 -->
    <RadioGroup
        android:id="@+id/radio_group_save_mode"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp">

        <RadioButton
            android:id="@+id/radio_update_existing"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="更新已有布局参数"
            android:textSize="14sp"
            android:textColor="#333333"
            android:checked="true"
            android:layout_marginBottom="8dp"
            android:buttonTint="#2196F3" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="• 只更新当前投屏设备的参数\n• 保留布局中其他设备的原有参数"
            android:textSize="12sp"
            android:textColor="#666666"
            android:layout_marginStart="32dp"
            android:layout_marginBottom="12dp"
            android:lineSpacingExtra="2dp" />

        <RadioButton
            android:id="@+id/radio_replace_all"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="清空已有布局参数并重新保存"
            android:textSize="14sp"
            android:textColor="#333333"
            android:layout_marginBottom="8dp"
            android:buttonTint="#2196F3" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="• 清空布局中的所有设备参数\n• 重新保存当前所有投屏设备"
            android:textSize="12sp"
            android:textColor="#666666"
            android:layout_marginStart="32dp"
            android:layout_marginBottom="16dp"
            android:lineSpacingExtra="2dp" />
    </RadioGroup>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0"
        android:layout_marginBottom="16dp"/>

    <!-- 底部按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="60dp"
            android:layout_height="32dp"
            android:text="取消"
            android:textSize="12sp"
            android:background="@drawable/button_cancel_background"
            android:textColor="#666666"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="60dp"
            android:layout_height="32dp"
            android:text="确定"
            android:textSize="12sp"
            android:background="@drawable/button_background"
            android:textColor="#FFFFFF" />
    </LinearLayout>
</LinearLayout>
