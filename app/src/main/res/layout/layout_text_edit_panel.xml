<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/precision_control_background"
    android:elevation="8dp">

    <!-- 紧凑型标题栏 -->
    <LinearLayout
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="#4CAF50"
        android:paddingStart="8dp"
        android:paddingEnd="8dp">

        <!-- 拖动手柄 -->
        <ImageView
            android:id="@+id/drag_handle"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_drag_handle"
            android:layout_marginEnd="6dp"
            app:tint="#FFFFFF" />

        <!-- 标题文本 -->
        <TextView
            android:id="@+id/tv_panel_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="格式"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="#FFFFFF" />

        <!-- 关闭按钮 -->
        <ImageView
            android:id="@+id/btn_close_panel"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_close"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="3dp"
            app:tint="#FFFFFF" />

    </LinearLayout>

    <!-- 按钮区域 - 分为两行 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#FFFFFF">

        <!-- 第一行：文字格式按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="4dp"
            android:gravity="center">

            <!-- 加粗按钮 -->
            <LinearLayout
                android:id="@+id/btn_bold"
                android:layout_width="30dp"
                android:layout_height="35dp"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/button_background"
                android:layout_marginEnd="6dp"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:src="@drawable/ic_format_bold"
                    app:tint="#333333" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="加粗"
                    android:textSize="9sp"
                    android:textColor="#333333"/>

            </LinearLayout>

            <!-- 倾斜按钮 -->
            <LinearLayout
                android:id="@+id/btn_italic"
                android:layout_width="30dp"
                android:layout_height="35dp"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/button_background"
                android:layout_marginEnd="6dp"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:src="@drawable/ic_format_italic"
                    app:tint="#333333" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="倾斜"
                    android:textSize="9sp"
                    android:textColor="#333333"/>

            </LinearLayout>

            <!-- 字号下拉框 -->
            <LinearLayout
                android:id="@+id/font_size_container"
                android:layout_width="30dp"
                android:layout_height="35dp"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/button_background"
                android:layout_marginEnd="6dp"
                android:clickable="true"
                android:focusable="true">

                <Spinner
                    android:id="@+id/spinner_font_size"
                    android:layout_width="30dp"
                    android:layout_height="15dp"
                    android:background="@android:color/transparent"
                    android:dropDownWidth="70dp"
                    android:dropDownVerticalOffset="30dp"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="字号"
                    android:textSize="9sp"
                    android:textColor="#333333"/>

            </LinearLayout>

            <!-- 字体下拉框 -->
            <LinearLayout
                android:id="@+id/font_family_container"
                android:layout_width="30dp"
                android:layout_height="35dp"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/button_background"
                android:layout_marginEnd="6dp"
                android:clickable="true"
                android:focusable="true">

                <Spinner
                    android:id="@+id/spinner_font_family"
                    android:layout_width="30dp"
                    android:layout_height="15dp"
                    android:background="@android:color/transparent"
                    android:dropDownWidth="80dp"
                    android:dropDownVerticalOffset="30dp"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="字体"
                    android:textSize="9sp"
                    android:textColor="#333333"/>

            </LinearLayout>

            <!-- 字间距按钮 -->
            <LinearLayout
                android:id="@+id/letter_spacing_container"
                android:layout_width="30dp"
                android:layout_height="35dp"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginEnd="6dp"
                android:background="@drawable/button_background"
                android:clickable="true"
                android:focusable="true">

                <Spinner
                    android:id="@+id/spinner_letter_spacing"
                    android:layout_width="30dp"
                    android:layout_height="15dp"
                    android:background="@android:color/transparent"
                    android:dropDownWidth="80dp"
                    android:dropDownVerticalOffset="30dp"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="字距"
                    android:textSize="9sp"
                    android:textColor="#333333"/>

            </LinearLayout>

            <!-- 行间距按钮 -->
            <LinearLayout
                android:id="@+id/line_spacing_container"
                android:layout_width="30dp"
                android:layout_height="35dp"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/button_background"
                android:clickable="true"
                android:focusable="true">

                <Spinner
                    android:id="@+id/spinner_line_spacing"
                    android:layout_width="30dp"
                    android:layout_height="15dp"
                    android:background="@android:color/transparent"
                    android:dropDownWidth="80dp"
                    android:dropDownVerticalOffset="30dp"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="行距"
                    android:textSize="9sp"
                    android:textColor="#333333"/>

            </LinearLayout>

        </LinearLayout>

        <!-- 第二行：颜色和窗口操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="4dp"
            android:gravity="center">

            <!-- 对齐按钮 -->
            <LinearLayout
                android:id="@+id/text_alignment_container"
                android:layout_width="30dp"
                android:layout_height="35dp"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/button_background"
                android:layout_marginEnd="6dp"
                android:clickable="true"
                android:focusable="true">

                <Spinner
                    android:id="@+id/spinner_text_alignment"
                    android:layout_width="30dp"
                    android:layout_height="15dp"
                    android:background="@android:color/transparent"
                    android:dropDownWidth="80dp"
                    android:dropDownVerticalOffset="30dp"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="对齐"
                    android:textSize="9sp"
                    android:textColor="#333333"/>

            </LinearLayout>

            <!-- 颜色按钮 -->
            <LinearLayout
                android:id="@+id/btn_color"
                android:layout_width="30dp"
                android:layout_height="35dp"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/button_background"
                android:layout_marginEnd="6dp"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:src="@drawable/ic_palette"
                    app:tint="#333333" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="字色"
                    android:textSize="9sp"
                    android:textColor="#333333"/>

            </LinearLayout>

            <!-- 描边按钮 -->
            <LinearLayout
                android:id="@+id/btn_stroke"
                android:layout_width="30dp"
                android:layout_height="35dp"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/button_background"
                android:layout_marginEnd="6dp"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:src="@drawable/ic_edit"
                    app:tint="#333333" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="描边"
                    android:textSize="9sp"
                    android:textColor="#333333"/>

            </LinearLayout>

            <!-- 窗色按钮 -->
            <LinearLayout
                android:id="@+id/btn_window_color"
                android:layout_width="30dp"
                android:layout_height="35dp"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/button_background"
                android:layout_marginEnd="6dp"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:src="@drawable/ic_layer"
                    app:tint="#333333" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="窗色"
                    android:textSize="9sp"
                    android:textColor="#333333"/>

            </LinearLayout>

            <!-- 清除格式按钮 -->
            <LinearLayout
                android:id="@+id/btn_clear_format"
                android:layout_width="30dp"
                android:layout_height="35dp"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/button_background"
                android:layout_marginEnd="6dp"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:src="@drawable/ic_format_clear"
                    app:tint="#333333" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="清除"
                    android:textSize="9sp"
                    android:textColor="#333333"/>

            </LinearLayout>

            <!-- 拖动手柄按钮 -->
            <LinearLayout
                android:id="@+id/btn_drag_window"
                android:layout_width="30dp"
                android:layout_height="35dp"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/button_background"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:src="@drawable/ic_drag_handle"
                    app:tint="#333333" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="拖动"
                    android:textSize="9sp"
                    android:textColor="#333333"/>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
