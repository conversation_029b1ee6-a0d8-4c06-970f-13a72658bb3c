<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:padding="8dp"
    android:background="?android:attr/selectableItemBackground">

    <ImageView
        android:id="@+id/iv_alignment_icon"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:src="@drawable/ic_format_align_center"
        android:layout_marginEnd="8dp"
        app:tint="#333333" />

    <TextView
        android:id="@+id/tv_alignment_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="居中"
        android:textSize="14sp"
        android:textColor="#333333" />

</LinearLayout>
