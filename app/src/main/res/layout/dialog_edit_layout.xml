<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="#FFFFFF">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="#2196F3"
        android:padding="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="编辑布局名称"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#FFFFFF" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="✏️"
            android:textSize="16sp" />
    </LinearLayout>

    <!-- 内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp"
        android:background="#FFFFFF">

        <!-- 当前名称显示区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="#F5F5F5"
            android:padding="12dp"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="当前名称"
                android:textSize="11sp"
                android:textColor="#666666"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/current_name_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="会议室布局1"
                android:textSize="14sp"
                android:textColor="#333333"
                android:textStyle="bold"
                android:ellipsize="end"
                android:maxLines="1" />
        </LinearLayout>

        <!-- 新名称输入区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="新名称"
                android:textSize="11sp"
                android:textColor="#666666"
                android:layout_marginBottom="6dp" />

            <EditText
                android:id="@+id/layout_name_input"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:hint="输入新的布局名称"
                android:textSize="14sp"
                android:textColor="#333333"
                android:textColorHint="#AAAAAA"
                android:background="@drawable/edit_text_background"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:maxLength="50"
                android:singleLine="true"
                android:imeOptions="actionDone"
                android:inputType="text" />
        </LinearLayout>

        <!-- 提示信息 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="最多50个字符，不能与现有布局重名"
            android:textSize="10sp"
            android:textColor="#888888"
            android:layout_marginBottom="8dp" />

    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end"
        android:background="#F8F8F8"
        android:padding="8dp">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="取消"
            android:textSize="12sp"
            android:textColor="#666666"
            android:background="@drawable/button_cancel_background"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:layout_marginEnd="12dp"
            android:minWidth="70dp" />

        <Button
            android:id="@+id/btn_save"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="保存"
            android:textSize="12sp"
            android:textColor="#FFFFFF"
            android:background="@drawable/button_background"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:minWidth="70dp" />
    </LinearLayout>

</LinearLayout>
