package com.example.castapp.rtp

import com.example.castapp.network.UdpReceiver
import com.example.castapp.network.DataView
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList
import java.util.Locale
import com.example.castapp.utils.AppLog

/**
 * RTP接收器 - 简化版
 */
class RtpReceiver(
    port: Int,
    private val multiConnectionManager: MultiConnectionManager,
    private val getConnections: () -> List<com.example.castapp.model.Connection> = { emptyList() }
) {
    companion object {        private const val PACKET_TIMEOUT_MS = 200L // 分片重组超时时间
        private const val MAX_FRAGMENTS_PER_FRAME = 600 // 最大分片数量限制
        private const val AGGRESSIVE_CLEANUP_THRESHOLD = 60 // 缓存清理阈值
        private const val MIN_FRAGMENTS_FOR_FORCE_REASSEMBLE = 2 // 强制重组最少分片数
        private const val FORCE_REASSEMBLE_TIMEOUT_MS = 120L // 强制重组超时时间

        // 🚀 CPU优化：增加统计报告间隔，减少日志频率
        private const val STATS_REPORT_INTERVAL = 180000 // 统计报告间隔180秒
        private const val PARTIAL_REASSEMBLE_MIN_FRAGMENTS = 3 // 部分重组最少分片数
    }

    // 🚀 零拷贝优化：使用DataView版本的UdpReceiver
    private val udpReceiver = UdpReceiver(port) { dataView ->
        handleRtpDataZeroCopy(dataView)
    }

    // 🎯 简化监控：只保留基础统计
    private var successfulReassembles = 0L
    private var failedReassembles = 0L
    private var totalFragmentsReceived = 0L
    private var lastStatsTime = System.currentTimeMillis()

    // 用于重组分片包的缓存 - 按投屏ID分组
    private val fragmentCache = ConcurrentHashMap<String, ConcurrentHashMap<Long, ConcurrentHashMap<Int, RtpPacket>>>()
    private val timestampLastSeen = ConcurrentHashMap<String, ConcurrentHashMap<Long, Long>>()

    // 已处理的断开通知包集合，防止重复处理
    private val processedDisconnects = ConcurrentHashMap.newKeySet<String>()

    // 已处理的屏幕分辨率信息包集合，防止重复处理
    private val processedScreenResolutions = ConcurrentHashMap.newKeySet<String>()

    /**
     * 启动RTP接收器
     */
    fun start(): Boolean {
        return udpReceiver.start()
    }

    /**
     * 停止RTP接收器
     */
    fun stop() {
        udpReceiver.stop()
        fragmentCache.clear()
        timestampLastSeen.clear()
        processedDisconnects.clear()
        processedScreenResolutions.clear()
        multiConnectionManager.cleanup()
    }

    /**
     * 处理接收到的RTP数据 - 智能零拷贝优化（完全零拷贝）
     */
    private fun handleRtpDataZeroCopy(dataView: DataView) {
        try {
            val packet = RtpPacket()

            // 🚀 智能零拷贝：统一使用SmartDataView，完全零拷贝
            val smartDataView = dataView as com.example.castapp.network.SmartDataView
            if (!packet.fromDataView(smartDataView) || packet.payloadType != RtpPacket.H264_PAYLOAD_TYPE) {
                return
            }

            // 直接从SSRC计算connectionId，无需查表
            val connectionId = findConnectionIdBySSRC(packet.ssrc) ?: return

            // 🎯 简化统计：只记录基础数据
            totalFragmentsReceived++

            // 🎯 简化统计报告：定期输出基础统计
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastStatsTime > STATS_REPORT_INTERVAL) {
                val totalReassembles = successfulReassembles + failedReassembles
                val successRate = if (totalReassembles > 0) {
                    (successfulReassembles * 100.0 / totalReassembles)
                } else 100.0

                AppLog.network("RTP接收统计: 总分片=${totalFragmentsReceived}, 重组成功率=${String.format(Locale.US, "%.1f%%", successRate)}")

                // 重置统计计数器
                successfulReassembles = 0
                failedReassembles = 0
                totalFragmentsReceived = 0
                lastStatsTime = currentTime
            }

            processH264Packet(packet, connectionId)

        } catch (e: Exception) {
            if (System.currentTimeMillis() % 1000 < 100) {
                AppLog.e("处理RTP数据失败", e)
            }
        }
    }

    /**
     * 通过SSRC查找connectionId - 简化版
     */
    private fun findConnectionIdBySSRC(ssrc: Long): String? {
        return getConnections().find { it.getSSRC() == ssrc }?.connectionId
    }

    /**
     * 处理H.264 RTP包（统一ID架构）
     */
    private fun processH264Packet(packet: RtpPacket, connectionId: String) {
        val timestamp = packet.timestamp

        // 快速路径：单个NAL单元包直接处理
        if (!packet.isFuA && packet.marker) {
            // 获取连接的分片缓存，检查是否有待处理的分片
            val connectionFragmentCache = fragmentCache[connectionId]?.get(timestamp)
            if (connectionFragmentCache != null) {
                // 有旧的分片，需要重组
                // 🚀 零拷贝重组：直接重组到SmartBuffer，避免临时ByteArray
                val reassembledPayloadView = reassembleFragmentsZeroCopy(connectionFragmentCache, packet)
                if (reassembledPayloadView != null) {
                    multiConnectionManager.handleH264Data(connectionId, reassembledPayloadView)
                }
                fragmentCache[connectionId]?.remove(timestamp)
                timestampLastSeen[connectionId]?.remove(timestamp)
            } else {
                // 🚀 零拷贝优化：单个包，安全的零拷贝路径
                multiConnectionManager.handleH264Data(connectionId, packet.payloadView)
            }
            return
        }

        // 处理分片包
        val currentTime = System.currentTimeMillis()

        // 获取或创建连接的缓存
        val connectionFragmentCache = fragmentCache.getOrPut(connectionId) { ConcurrentHashMap() }
        val connectionTimestampCache = timestampLastSeen.getOrPut(connectionId) { ConcurrentHashMap() }

        // 只在首次收到该时间戳的分片时记录时间，避免重复更新导致超时重置
        if (!connectionTimestampCache.containsKey(timestamp)) {
            connectionTimestampCache[timestamp] = currentTime
        }

        if (packet.isFuA) {
            // FU-A分片包处理
            val fragments = connectionFragmentCache.getOrPut(timestamp) { ConcurrentHashMap() }

            // 检查分片数量限制，防止内存泄漏
            if (fragments.size >= MAX_FRAGMENTS_PER_FRAME) {
                connectionFragmentCache.remove(timestamp)
                return
            }

            fragments[packet.sequenceNumber] = packet

            if (packet.fuEnd) {
                // 最后一个FU-A分片，立即重组
                // 🚀 零拷贝重组：直接重组到SmartBuffer，避免临时ByteArray
                val reassembledPayloadView = reassembleFuAFragmentsZeroCopy(fragments)
                if (reassembledPayloadView != null) {
                    multiConnectionManager.handleH264Data(connectionId, reassembledPayloadView)
                    successfulReassembles++
                } else {
                    failedReassembles++
                }
                connectionFragmentCache.remove(timestamp)
                connectionTimestampCache.remove(timestamp)
            } else {
                // 检查是否需要强制重组（更保守的策略，确保有足够分片和合理超时）
                val firstSeenTime = connectionTimestampCache[timestamp] ?: currentTime
                val waitTime = currentTime - firstSeenTime

                // 🎯 简化强制重组：基于时间和分片数量的简单策略
                if (shouldForceReassemble(fragments, waitTime)) {
                    AppLog.w("FU-A分片等待超时，强制重组 (等待时间: ${waitTime}ms, 分片数: ${fragments.size})")
                    // 🚀 零拷贝重组：直接重组到SmartBuffer，避免临时ByteArray
                    val reassembledPayloadView = reassembleFuAFragmentsZeroCopy(fragments)
                    if (reassembledPayloadView != null) {
                        multiConnectionManager.handleH264Data(connectionId, reassembledPayloadView)
                        successfulReassembles++
                    } else {
                        failedReassembles++
                    }
                    connectionFragmentCache.remove(timestamp)
                    connectionTimestampCache.remove(timestamp)
                }
            }
        } else {
            // 传统分片包处理
            val fragments = connectionFragmentCache.getOrPut(timestamp) { ConcurrentHashMap() }

            // 检查分片数量限制
            if (fragments.size >= MAX_FRAGMENTS_PER_FRAME) {
                connectionFragmentCache.remove(timestamp)
                return
            }

            fragments[packet.sequenceNumber] = packet
        }

        // 智能清理策略：降低清理频率，减少对正在重组分片的干扰
        if (currentTime % 3000 < 10) { // 调整清理频率到每3秒一次，给分片重组更多时间
            cleanupExpiredFragments(connectionId, currentTime)
        }

        // 适度清理：如果缓存的时间戳过多，强制清理
        val connectionCache = fragmentCache[connectionId]
        if (connectionCache != null && connectionCache.size > AGGRESSIVE_CLEANUP_THRESHOLD) {
            val sortedTimestamps = connectionCache.keys.sorted()
            // 保留最新的30个时间戳，删除其他的，给开始包更多保护时间
            sortedTimestamps.dropLast(30).forEach { oldTimestamp ->
                connectionCache.remove(oldTimestamp)
                timestampLastSeen[connectionId]?.remove(oldTimestamp)
            }
        }
    }







    /**
     * 清理过期的分片（统一ID架构）
     */
    private fun cleanupExpiredFragments(connectionId: String, currentTime: Long) {
        val connectionFragmentCache = fragmentCache[connectionId] ?: return
        val connectionTimestampCache = timestampLastSeen[connectionId] ?: return

        val expiredTimestamps = CopyOnWriteArrayList<Long>()

        connectionTimestampCache.entries.forEach { (timestamp, firstSeenTime) ->
            val waitTime = currentTime - firstSeenTime

            // 检查是否真的过期
            if (waitTime > PACKET_TIMEOUT_MS) {
                val fragments = connectionFragmentCache[timestamp]
                if (fragments != null) {
                    // 如果有开始分片，给更多时间等待其他分片
                    val hasStartFragment = fragments.values.any { it.fuStart }
                    val hasEndFragment = fragments.values.any { it.fuEnd }

                    // 智能超时策略：根据分片情况动态调整超时时间
                    val extendedTimeout = when {
                        hasStartFragment && !hasEndFragment -> PACKET_TIMEOUT_MS * 2.5 // 有开始分片但缺结束分片，给更多时间
                        !hasStartFragment && hasEndFragment -> PACKET_TIMEOUT_MS * 2.0 // 有结束分片但缺开始分片，适当延长
                        hasStartFragment && hasEndFragment -> PACKET_TIMEOUT_MS * 1.2 // 有开始和结束分片，稍微延长
                        fragments.size >= PARTIAL_REASSEMBLE_MIN_FRAGMENTS -> PACKET_TIMEOUT_MS * 1.8 // 分片数量足够，延长等待
                        else -> PACKET_TIMEOUT_MS
                    }.toLong()

                    if (waitTime > extendedTimeout) {
                        AppLog.w("清理过期分片 timestamp=$timestamp, 等待时间=${waitTime}ms, 分片数=${fragments.size}, 有开始分片=$hasStartFragment, 有结束分片=$hasEndFragment, 超时阈值=${extendedTimeout}ms")
                        expiredTimestamps.add(timestamp)
                    }
                } else {
                    // 没有分片数据，直接清理
                    expiredTimestamps.add(timestamp)
                }
            }
        }

        expiredTimestamps.forEach { timestamp ->
            connectionFragmentCache.remove(timestamp)
            connectionTimestampCache.remove(timestamp)
        }

        if (expiredTimestamps.isNotEmpty()) {
            AppLog.network("连接 $connectionId 清理了 ${expiredTimestamps.size} 个过期时间戳")
        }
    }



    /**
     * 🎯 简化强制重组判断：基于时间和分片数量的简单策略
     */
    private fun shouldForceReassemble(fragments: ConcurrentHashMap<Int, RtpPacket>, waitTime: Long): Boolean {
        return fragments.size >= MIN_FRAGMENTS_FOR_FORCE_REASSEMBLE &&
               waitTime > FORCE_REASSEMBLE_TIMEOUT_MS
    }

    /**
     * 🚀 零拷贝FU-A分片重组：直接重组到SmartBuffer，完全避免临时ByteArray
     * 这是延迟优化的核心方法，可减少10-15ms的数据拷贝延迟
     */
    private fun reassembleFuAFragmentsZeroCopy(fragments: ConcurrentHashMap<Int, RtpPacket>): PayloadView? {
        try {
            // 按序列号排序
            val sortedFragments = fragments.values.sortedBy { it.sequenceNumber }

            if (sortedFragments.isEmpty()) {
                return null
            }

            // 验证FU-A分片的完整性
            var hasStart = false
            var hasEnd = false
            var nalType = -1

            for (packet in sortedFragments) {
                if (!packet.isFuA) {
                    AppLog.w("FU-A零拷贝重组中发现非FU-A包")
                    return null
                }

                if (packet.fuStart) {
                    if (hasStart) {
                        AppLog.w("FU-A零拷贝重组中发现多个开始包")
                        return null
                    }
                    hasStart = true
                    nalType = packet.originalNalType
                }

                if (packet.fuEnd) {
                    if (hasEnd) {
                        AppLog.w("FU-A零拷贝重组中发现多个结束包")
                        return null
                    }
                    hasEnd = true
                }

                // 验证NAL类型一致性
                if (nalType != -1 && packet.originalNalType != nalType) {
                    AppLog.w("FU-A零拷贝重组中发现不一致的NAL类型")
                    return null
                }
            }

            // 容错处理：智能部分重组
            if (!hasStart && !hasEnd) {
                if (sortedFragments.size >= PARTIAL_REASSEMBLE_MIN_FRAGMENTS) {
                    AppLog.w("FU-A零拷贝重组缺少开始和结束包，尝试智能重组 (分片数: ${sortedFragments.size})")
                    nalType = sortedFragments.firstOrNull()?.originalNalType ?: return null
                } else {
                    AppLog.w("FU-A零拷贝重组缺少开始和结束包，分片数不足")
                    return null
                }
            } else if (!hasStart) {
                AppLog.w("FU-A零拷贝重组缺少开始包，尝试部分重组")
                nalType = sortedFragments.firstOrNull()?.originalNalType ?: return null
            } else if (!hasEnd) {
                AppLog.w("FU-A零拷贝重组缺少结束包，尝试部分重组")
            }

            // 🚀 零拷贝核心：计算总大小，直接分配目标SmartBuffer
            var totalDataSize = 1 // NAL头部
            sortedFragments.forEach { packet ->
                val nalDataView = packet.getFuANalDataView()
                if (nalDataView != null) {
                    totalDataSize += nalDataView.size()
                }
            }

            // 🚀 零拷贝优化：直接获取适当大小的SmartBuffer，避免临时数组
            val smartBuffer = com.example.castapp.network.SmartBufferManager.getInstance().acquireBuffer(totalDataSize)
            val targetBuffer = java.nio.ByteBuffer.wrap(smartBuffer.data, 0, totalDataSize)

            // 重构NAL头部
            val firstPacket = sortedFragments.first()
            val originalNalHeader = firstPacket.payloadView.getByte(0).toInt() and 0xFF
            val fBit = originalNalHeader and 0x80
            val nriBits = originalNalHeader and 0x60
            val reconstructedNalHeader = fBit or nriBits or nalType
            targetBuffer.put(reconstructedNalHeader.toByte())

            // 🚀 零拷贝拼接：直接从各分片的PayloadView写入目标缓冲区
            sortedFragments.forEach { packet ->
                packet.getFuANalDataView()?.writeTo(targetBuffer)
            }

            // 创建最终的PayloadView，完全零拷贝
            val smartDataView = com.example.castapp.network.SmartDataView(smartBuffer, 0, totalDataSize)
            val payloadView = PayloadView()
            payloadView.setView(smartDataView, 0, totalDataSize)

            // 🚀 CPU优化：移除高频FU-A重组成功日志，减少CPU占用
            return payloadView

        } catch (e: Exception) {
            AppLog.e("零拷贝FU-A分片重组失败", e)
            return null
        }
    }

    /**
     * 🚀 零拷贝普通分片重组：直接重组到SmartBuffer，避免临时ByteArray
     */
    private fun reassembleFragmentsZeroCopy(
        fragments: ConcurrentHashMap<Int, RtpPacket>,
        lastPacket: RtpPacket
    ): PayloadView? {
        try {
            // 添加最后一个包
            fragments[lastPacket.sequenceNumber] = lastPacket

            // 按序列号排序
            val sortedFragments = fragments.values.sortedBy { it.sequenceNumber }

            // 🚀 零拷贝核心：计算总大小，直接分配目标SmartBuffer
            val totalSize = sortedFragments.sumOf { it.payloadView.size() }

            if (totalSize <= 0) {
                return null
            }

            // 🚀 零拷贝优化：直接获取适当大小的SmartBuffer
            val smartBuffer = com.example.castapp.network.SmartBufferManager.getInstance().acquireBuffer(totalSize)
            val targetBuffer = java.nio.ByteBuffer.wrap(smartBuffer.data, 0, totalSize)

            // 🚀 零拷贝拼接：直接从各分片的PayloadView写入目标缓冲区
            sortedFragments.forEach { packet ->
                packet.payloadView.writeTo(targetBuffer)
            }

            // 创建最终的PayloadView，完全零拷贝
            val smartDataView = com.example.castapp.network.SmartDataView(smartBuffer, 0, totalSize)
            val payloadView = PayloadView()
            payloadView.setView(smartDataView, 0, totalSize)

            AppLog.network("🚀 零拷贝普通分片重组成功: 分片数=${sortedFragments.size}, 数据大小=${totalSize}")
            return payloadView

        } catch (e: Exception) {
            AppLog.e("零拷贝普通分片重组失败", e)
            return null
        }
    }
}