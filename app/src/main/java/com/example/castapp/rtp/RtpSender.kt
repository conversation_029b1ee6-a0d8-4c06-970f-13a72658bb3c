package com.example.castapp.rtp

import com.example.castapp.network.UdpSender
import java.util.concurrent.atomic.AtomicInteger
import java.nio.ByteBuffer
import com.example.castapp.utils.AppLog
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicLong

/**
 * RTP发送器 - 零拷贝池化优化版本
 */
class RtpSender(
    targetIp: String,
    targetPort: Int,
    private val ssrc: Long = System.currentTimeMillis() and 0xFFFFFFFFL
) {
    companion object {
        private const val MAX_PACKET_SIZE = 1472 // 🚀 激进优化：接近以太网MTU极限(1500-28=1472)

        // 🚀 零拷贝池化配置 - 大容量确保不耗尽
        private const val SMALL_PACKET_POOL_SIZE = 200  // 单包池大小
        private const val FRAGMENT_PACKET_POOL_SIZE = 500  // 分片包池大小
        private const val SMALL_PACKET_SIZE = 1500  // 单包最大大小
        private const val FRAGMENT_PACKET_SIZE = 1500  // 分片包大小
    }

    private val udpSender = UdpSender(targetIp, targetPort)
    private val sequenceNumber = AtomicInteger(0)
    private var startTime = System.currentTimeMillis()

    // 🚀 零拷贝RTP包池系统
    private val smallPacketPool = ConcurrentLinkedQueue<PooledRtpPacket>()
    private val fragmentPacketPool = ConcurrentLinkedQueue<PooledRtpPacket>()
    private val poolStatsCreated = AtomicLong(0)
    private val poolStatsReused = AtomicLong(0)

    /**
     * 🚀 池化RTP包 - 零拷贝核心数据结构
     */
    private class PooledRtpPacket(
        val buffer: ByteArray,
        val maxSize: Int
    ) {
        @Volatile var isInUse = false
        @Volatile private var dataSize = 0

        fun acquire(): Boolean {
            return if (!isInUse) {
                isInUse = true
                dataSize = 0
                true
            } else {
                false
            }
        }

        fun release() {
            dataSize = 0
            isInUse = false
        }

        fun getUsableBuffer(): ByteArray = buffer
        fun setActualSize(size: Int) { dataSize = size }
        fun getActualSize(): Int = dataSize
    }

    init {
        // 🚀 预分配RTP包池
        initializePacketPools()
        AppLog.network("🚀 零拷贝RTP发送器初始化完成")
    }

    /**
     * 初始化包池
     */
    private fun initializePacketPools() {
        // 预分配小包池
        repeat(SMALL_PACKET_POOL_SIZE) {
            val packet = PooledRtpPacket(ByteArray(SMALL_PACKET_SIZE), SMALL_PACKET_SIZE)
            smallPacketPool.offer(packet)
        }

        // 预分配分片包池
        repeat(FRAGMENT_PACKET_POOL_SIZE) {
            val packet = PooledRtpPacket(ByteArray(FRAGMENT_PACKET_SIZE), FRAGMENT_PACKET_SIZE)
            fragmentPacketPool.offer(packet)
        }

        AppLog.network("🚀 RTP包池初始化: 小包=${SMALL_PACKET_POOL_SIZE}, 分片包=${FRAGMENT_PACKET_POOL_SIZE}")
    }

    /**
     * 启动RTP发送器
     */
    fun start(): Boolean {
        startTime = System.currentTimeMillis()
        return udpSender.start()
    }

    /**
     * 停止RTP发送器
     */
    fun stop() {
        udpSender.stop()
    }

    /**
     * 完全清理RTP发送器资源，包括协程作用域
     * 在发送器不再使用时调用
     */
    fun cleanup() {
        AppLog.network("开始清理RtpSender资源...")

        // 🚀 清理包池资源
        cleanupPacketPools()

        udpSender.cleanup()
        AppLog.network("RtpSender资源清理完成")
    }

    /**
     * 清理包池资源
     */
    private fun cleanupPacketPools() {
        // 释放所有池化包
        while (smallPacketPool.isNotEmpty()) {
            smallPacketPool.poll()?.release()
        }
        while (fragmentPacketPool.isNotEmpty()) {
            fragmentPacketPool.poll()?.release()
        }

        val created = poolStatsCreated.get()
        val reused = poolStatsReused.get()
        val reuseRate = if (created > 0) (reused * 100.0 / created) else 0.0

        AppLog.network("🚀 RTP包池统计: 创建=$created, 复用=$reused, 复用率=${String.format("%.1f", reuseRate)}%")
    }



    /**
     * 🚀 发送H.264数据 - 终极零拷贝池化版本
     */
    fun sendH264Data(buffer: ByteBuffer, size: Int) {
        try {
            val timestamp = generateTimestamp()
            sendH264DataUltraZeroCopy(buffer, size, timestamp)

        } catch (e: Exception) {
            AppLog.e("发送H.264数据失败", e)
        }
    }



    /**
     * 🚀 终极零拷贝发送H.264数据 - 池化版本
     */
    private fun sendH264DataUltraZeroCopy(buffer: ByteBuffer, size: Int, timestamp: Long) {
        // 检查数据大小
        if (size <= 0) return

        // 获取NAL头部（第一个字节）
        val nalHeader = buffer.get(buffer.position()).toInt() and 0xFF
        val nalType = nalHeader and 0x1F

        if (size <= MAX_PACKET_SIZE - RtpPacket.RTP_HEADER_SIZE) {
            // 🚀 单包路径：纯池化包，无回退
            val pooledPacket = acquireSmallPacket()
            createSinglePacketInPlace(pooledPacket, buffer, size, timestamp)
            sendPooledPacketDirect(pooledPacket)
        } else {
            // 🚀 分片路径：纯池化分片包，无回退
            sendFragmentedUltraZeroCopy(buffer, size, timestamp, nalHeader.toByte(), nalType)
        }
    }

    /**
     * 🚀 获取小包池中的包 - 纯池化版本
     */
    private fun acquireSmallPacket(): PooledRtpPacket {
        // 🚀 从池中获取，如果没有就创建
        return smallPacketPool.poll()?.apply {
            acquire()
            poolStatsReused.incrementAndGet()
        } ?: run {
            // 创建新包
            val newPacket = PooledRtpPacket(ByteArray(SMALL_PACKET_SIZE), SMALL_PACKET_SIZE)
            newPacket.acquire()
            poolStatsCreated.incrementAndGet()
            newPacket
        }
    }

    /**
     * 🚀 释放小包回池 - 无条件回池
     */
    private fun releaseSmallPacket(packet: PooledRtpPacket) {
        packet.release()
        smallPacketPool.offer(packet)
    }

    /**
     * 🚀 获取分片包池中的包 - 纯池化版本
     */
    private fun acquireFragmentPacket(): PooledRtpPacket {
        // 🚀 从池中获取，如果没有就创建
        return fragmentPacketPool.poll()?.apply {
            acquire()
            poolStatsReused.incrementAndGet()
        } ?: run {
            // 创建新包
            val newPacket = PooledRtpPacket(ByteArray(FRAGMENT_PACKET_SIZE), FRAGMENT_PACKET_SIZE)
            newPacket.acquire()
            poolStatsCreated.incrementAndGet()
            newPacket
        }
    }

    /**
     * 🚀 释放分片包回池 - 无条件回池
     */
    private fun releaseFragmentPacket(packet: PooledRtpPacket) {
        packet.release()
        fragmentPacketPool.offer(packet)
    }

    /**
     * 🚀 在池化包中直接创建RTP包 - 终极零拷贝
     */
    private fun createSinglePacketInPlace(pooledPacket: PooledRtpPacket, buffer: ByteBuffer, size: Int, timestamp: Long): Int {
        val totalSize = RtpPacket.RTP_HEADER_SIZE + size

        // 检查池化包容量
        if (totalSize > pooledPacket.maxSize) {
            AppLog.w("🚀 包大小超出池化包容量: $totalSize > ${pooledPacket.maxSize}")
            return 0
        }

        val packetBuffer = ByteBuffer.wrap(pooledPacket.getUsableBuffer(), 0, totalSize)

        // 直接写入RTP头部
        writeRtpHeaderDirect(packetBuffer, timestamp)

        // 🚀 零拷贝核心：直接从源ByteBuffer写入负载数据
        val originalPos = buffer.position()
        val originalLimit = buffer.limit()

        // 设置源缓冲区范围
        buffer.limit(originalPos + size)

        // 直接复制数据到池化包
        packetBuffer.put(buffer)

        // 恢复源缓冲区状态
        buffer.limit(originalLimit)
        buffer.position(originalPos)

        // 设置实际大小
        pooledPacket.setActualSize(totalSize)

        // 🚀 CPU优化：移除高频RTP包创建日志，减少CPU占用
        return totalSize
    }

    /**
     * 🚀 直接发送池化包 - 终极零拷贝版本
     */
    private fun sendPooledPacketDirect(pooledPacket: PooledRtpPacket) {
        try {
            val actualSize = pooledPacket.getActualSize()

            // 🚀 终极零拷贝：直接使用池化包的缓冲区，避免任何拷贝
            udpSender.sendDirectZeroCopy(pooledPacket.getUsableBuffer(), 0, actualSize)
            // 🚀 CPU优化：移除高频池化包发送日志，减少CPU占用

        } finally {
            // 发送完成后立即释放回池
            releaseSmallPacket(pooledPacket)
        }
    }

    /**
     * 🔥 分片发送 - 降温优化版本
     * 减少内存分配，优化批量发送
     */
    private fun sendFragmentedUltraZeroCopy(
        buffer: ByteBuffer,
        size: Int,
        timestamp: Long,
        nalHeader: Byte,
        nalType: Int
    ) {
        val maxPayloadSize = MAX_PACKET_SIZE - RtpPacket.RTP_HEADER_SIZE - RtpPacket.FU_HEADER_TOTAL_SIZE
        var offset = 1 // 跳过原始NAL头部
        var isFirst = true

        // 🔥 优化：预计算分片数量，避免动态扩容
        val totalFragments = ((size - 1) + maxPayloadSize - 1) / maxPayloadSize
        val pooledPackets = ArrayList<PooledRtpPacket>(totalFragments)

        try {
            while (offset < size) {
                val remainingSize = size - offset
                val currentFragmentSize = minOf(maxPayloadSize, remainingSize)
                val isLast = (offset + currentFragmentSize) >= size

                // 获取池化分片包（保证成功）
                val pooledPacket = acquireFragmentPacket()
                createFragmentPacketInPlace(
                    pooledPacket, buffer, offset, currentFragmentSize,
                    timestamp, nalHeader, nalType, isFirst, isLast
                )

                pooledPackets.add(pooledPacket)
                offset += currentFragmentSize
                isFirst = false
            }

            // 🔥 智能批量发送：根据分片数量选择发送策略
            if (pooledPackets.size <= 4) {
                // 小分片数量：逐个发送，减少批量开销
                pooledPackets.forEach { packet ->
                    sendPooledPacketDirect(packet)
                }
            } else {
                // 大分片数量：批量发送，提高效率
                sendPooledFragmentsBatchOptimized(pooledPackets)
            }

        } catch (e: Exception) {
            // 异常情况下释放所有包
            pooledPackets.forEach { releaseFragmentPacket(it) }
            throw e
        }
    }

    /**
     * 🚀 在池化包中创建分片包
     */
    private fun createFragmentPacketInPlace(
        pooledPacket: PooledRtpPacket,
        buffer: ByteBuffer,
        offset: Int,
        fragmentSize: Int,
        timestamp: Long,
        nalHeader: Byte,
        nalType: Int,
        isFirst: Boolean,
        isLast: Boolean
    ): Int {
        val totalSize = RtpPacket.RTP_HEADER_SIZE + RtpPacket.FU_HEADER_TOTAL_SIZE + fragmentSize

        if (totalSize > pooledPacket.maxSize) {
            return 0
        }

        val packetBuffer = ByteBuffer.wrap(pooledPacket.getUsableBuffer(), 0, totalSize)

        // 写入RTP头部
        writeRtpHeaderDirectForFragment(packetBuffer, timestamp, isLast)

        // 写入FU-A头部
        writeFuAHeaderDirect(packetBuffer, nalHeader, nalType, isFirst, isLast)

        // 🚀 零拷贝：直接从源ByteBuffer写入分片数据
        val originalPos = buffer.position()
        val originalLimit = buffer.limit()

        buffer.position(originalPos + offset)
        buffer.limit(originalPos + offset + fragmentSize)
        packetBuffer.put(buffer)

        // 恢复源缓冲区状态
        buffer.limit(originalLimit)
        buffer.position(originalPos)

        pooledPacket.setActualSize(totalSize)
        return totalSize
    }

    /**
     * 🔥 优化的批量发送池化分片包 - 降温版本
     */
    private fun sendPooledFragmentsBatchOptimized(pooledPackets: List<PooledRtpPacket>) {
        try {
            // 🔥 预分配精确大小的列表，避免动态扩容
            val packetDataList = ArrayList<ByteArray>(pooledPackets.size)

            // 🔥 优化内存访问模式：批量处理
            pooledPackets.forEach { pooledPacket ->
                val actualSize = pooledPacket.getActualSize()
                // 🔥 创建精确大小的数组，减少内存浪费
                val packetData = ByteArray(actualSize)
                System.arraycopy(pooledPacket.getUsableBuffer(), 0, packetData, 0, actualSize)
                packetDataList.add(packetData)
            }

            // 🔥 智能批量发送：利用UDP发送器的优化批量机制
            udpSender.sendBatch(packetDataList)

        } finally {
            // 🔥 及时释放所有包回池，减少内存压力
            pooledPackets.forEach { releaseFragmentPacket(it) }
        }
    }

    /**
     * 直接写入RTP头部，避免RtpPacket对象创建
     */
    private fun writeRtpHeaderDirect(buffer: ByteBuffer, timestamp: Long) {
        val seqNum = sequenceNumber.getAndIncrement()

        // RTP固定头部 (12字节)
        buffer.put((0x80).toByte())  // V=2, P=0, X=0, CC=0
        buffer.put((RtpPacket.H264_PAYLOAD_TYPE or 0x80).toByte())  // M=1, PT=96
        buffer.putShort(seqNum.toShort())  // 序列号
        buffer.putInt(timestamp.toInt())   // 时间戳
        buffer.putInt(ssrc.toInt())        // SSRC
    }

    /**
     * 直接写入RTP头部（分片版本）
     */
    private fun writeRtpHeaderDirectForFragment(buffer: ByteBuffer, timestamp: Long, isLast: Boolean) {
        val seqNum = sequenceNumber.getAndIncrement()

        // RTP固定头部 (12字节)
        buffer.put((0x80).toByte())  // V=2, P=0, X=0, CC=0
        val markerBit = if (isLast) 0x80 else 0x00
        buffer.put((RtpPacket.H264_PAYLOAD_TYPE or markerBit).toByte())  // M位 + PT=96
        buffer.putShort(seqNum.toShort())  // 序列号
        buffer.putInt(timestamp.toInt())   // 时间戳
        buffer.putInt(ssrc.toInt())        // SSRC
    }

    /**
     * 直接写入FU-A头部
     */
    private fun writeFuAHeaderDirect(buffer: ByteBuffer, nalHeader: Byte, nalType: Int, isStart: Boolean, isEnd: Boolean) {
        // FU指示符 (1字节)
        val fuIndicator = (nalHeader.toInt() and 0xE0) or 28  // 保持原始NAL的NRI，类型设为28(FU-A)
        buffer.put(fuIndicator.toByte())

        // FU头部 (1字节)
        var fuHeader = nalType and 0x1F  // NAL单元类型
        if (isStart) fuHeader = fuHeader or 0x80  // S位
        if (isEnd) fuHeader = fuHeader or 0x40    // E位
        buffer.put(fuHeader.toByte())
    }









    /**
     * 生成RTP时间戳
     */
    private fun generateTimestamp(): Long {
        // 90kHz时钟频率用于视频
        val elapsedMs = System.currentTimeMillis() - startTime
        return (elapsedMs * 90) and 0xFFFFFFFFL
    }
}
