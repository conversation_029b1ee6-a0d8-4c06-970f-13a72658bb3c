package com.example.castapp.rtp

import com.example.castapp.codec.VideoDecoder
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList
import com.example.castapp.utils.AppLog

/**
 * 视频解码器管理器 - 简化版
 * 专注于解码器生命周期管理，移除连接管理职责
 */
class MultiConnectionManager {

    // 投屏ID到解码器的映射
    private val decoders = ConcurrentHashMap<String, VideoDecoder>()

    // 投屏ID到显示回调的映射
    private val displayCallbacks = ConcurrentHashMap<String, (String) -> Unit>()

    // 连接断开回调的映射
    private val disconnectCallbacks = ConcurrentHashMap<String, (String) -> Unit>()

    // 屏幕分辨率回调的映射
    private val screenResolutionCallbacks = ConcurrentHashMap<String, (String, Int, Int) -> Unit>()

    // 正在创建解码器的投屏ID集合，防止重复创建
    private val creatingDecoders = ConcurrentHashMap.newKeySet<String>()

    // 待处理的H.264数据缓存
    private val pendingData = ConcurrentHashMap<String, CopyOnWriteArrayList<ByteArray>>()

    // 配置数据缓存（SPS/PPS），按投屏ID分组
    private val pendingConfigurationData = ConcurrentHashMap<String, CopyOnWriteArrayList<ByteArray>>()

    // 🚀 新增：分辨率数据缓存，按投屏ID分组
    private val pendingResolutionData = ConcurrentHashMap<String, Pair<Int, Int>>()

    // 已断开的连接ID集合，防止重复处理断开事件
    private val disconnectedConnections = ConcurrentHashMap.newKeySet<String>()

    // 视频流已停止的连接ID集合，防止重新创建窗口
    private val videoStreamStoppedConnections = ConcurrentHashMap.newKeySet<String>()

    /**
     * 注册连接的显示回调
     */
    fun registerDisplayCallback(connectionId: String, callback: (String) -> Unit) {
        displayCallbacks[connectionId] = callback
        AppLog.network("注册连接显示回调: $connectionId")
    }

    /**
     * 注册连接断开回调
     */
    fun registerDisconnectCallback(connectionId: String, callback: (String) -> Unit) {
        disconnectCallbacks[connectionId] = callback
        AppLog.network("注册连接断开回调: $connectionId")
    }

    /**
     * 为特定连接动态注册回调（如果尚未注册）
     */
    private fun ensureCallbackRegistered(
        connectionId: String,
        callbacks: ConcurrentHashMap<String, (String) -> Unit>,
        callbackType: String
    ) {
        if (!callbacks.containsKey(connectionId)) {
            val defaultCallback = callbacks["default"]
            if (defaultCallback != null) {
                callbacks[connectionId] = defaultCallback
                AppLog.network("为连接 $connectionId 复制默认${callbackType}回调")
            } else {
                AppLog.w("无法为连接 $connectionId 注册${callbackType}回调：没有默认回调")
            }
        }
    }

    fun ensureDisplayCallbackRegistered(connectionId: String) =
        ensureCallbackRegistered(connectionId, displayCallbacks, "显示")

    fun ensureDisconnectCallbackRegistered(connectionId: String) =
        ensureCallbackRegistered(connectionId, disconnectCallbacks, "断开")

    /**
     * 注册屏幕分辨率回调
     */
    fun registerScreenResolutionCallback(connectionId: String, callback: (String, Int, Int) -> Unit) {
        screenResolutionCallbacks[connectionId] = callback
        AppLog.network("注册屏幕分辨率回调: $connectionId")
    }

    /**
     * 检查连接状态是否有效
     */
    fun isConnectionValid(connectionId: String): Boolean {
        return !disconnectedConnections.contains(connectionId) &&
               !videoStreamStoppedConnections.contains(connectionId)
    }

    /**
     * 触发显示回调（用于WebSocket连接请求）
     */
    fun triggerDisplayCallback(connectionId: String) {
        if (!isConnectionValid(connectionId)) {
            AppLog.network("连接 $connectionId 状态无效，跳过显示回调")
            return
        }

        AppLog.network("触发显示回调: $connectionId")
        ensureDisplayCallbackRegistered(connectionId)
        ensureDisconnectCallbackRegistered(connectionId)

        displayCallbacks[connectionId]?.invoke(connectionId)
            ?: AppLog.e("未找到连接 $connectionId 的显示回调")
    }

    /**
     * 处理通过WebSocket接收到的H.264配置数据（兼容性方法）
     */
    fun handleWebSocketH264Config(connectionId: String, spsData: ByteArray?, ppsData: ByteArray?) {
        handleWebSocketH264ConfigWithResolution(connectionId, spsData, ppsData, null, null)
    }

    /**
     * 🚀 新增：处理通过WebSocket接收到的H.264配置数据（包含分辨率信息）
     */
    fun handleWebSocketH264ConfigWithResolution(connectionId: String, spsData: ByteArray?, ppsData: ByteArray?, width: Int?, height: Int?) {
        try {
            if (!isConnectionValid(connectionId)) {
                AppLog.network("连接 $connectionId 状态无效，跳过WebSocket配置数据处理")
                return
            }

            if (spsData == null && ppsData == null) {
                AppLog.w("WebSocket配置数据为空，跳过处理: $connectionId")
                return
            }

            if (width != null && height != null) {
                AppLog.network("处理WebSocket H.264配置数据: $connectionId, SPS: ${spsData?.size ?: 0} bytes, PPS: ${ppsData?.size ?: 0} bytes, 分辨率: ${width}x${height}")
            } else {
                AppLog.network("处理WebSocket H.264配置数据: $connectionId, SPS: ${spsData?.size ?: 0} bytes, PPS: ${ppsData?.size ?: 0} bytes")
            }

            val configDataList = CopyOnWriteArrayList<ByteArray>()
            spsData?.takeIf { it.isNotEmpty() }?.let { configDataList.add(it) }
            ppsData?.takeIf { it.isNotEmpty() }?.let { configDataList.add(it) }

            if (configDataList.isEmpty()) {
                AppLog.w("没有有效的配置数据: $connectionId")
                return
            }

            decoders[connectionId]?.let { decoder ->
                // 🚀 如果有分辨率信息，先设置WebSocket分辨率
                if (width != null && height != null) {
                    decoder.setWebSocketResolution(width, height)
                }

                configDataList.forEach {
                    val dataView = VideoDecoder.ByteArrayDataView(it)
                    val payloadView = PayloadView()
                    payloadView.setViewFromByteArray(dataView, 0, it.size)
                    decoder.decode(payloadView)
                }
                AppLog.network("已将WebSocket配置数据发送到现有解码器: $connectionId")
            } ?: run {
                val configList = pendingConfigurationData.getOrPut(connectionId) { CopyOnWriteArrayList() }
                configDataList.forEach { configList.add(it) }

                // 🚀 缓存分辨率信息
                if (width != null && height != null) {
                    pendingResolutionData[connectionId] = Pair(width, height)
                    AppLog.network("已缓存WebSocket分辨率信息等待解码器创建: $connectionId, ${width}x${height}")
                }

                AppLog.network("已缓存WebSocket配置数据等待解码器创建: $connectionId")
            }
        } catch (e: Exception) {
            AppLog.e("处理WebSocket H.264配置数据失败: $connectionId", e)
        }
    }

    /**
     * 缓存帧数据，支持大小限制
     */
    private fun cacheFrameData(
        connectionId: String,
        data: ByteArray
    ) {
        val dataList = pendingData.getOrPut(connectionId) { CopyOnWriteArrayList() }
        if (dataList.size < 150) {
            dataList.add(data)
        } else {
            AppLog.w("连接 $connectionId 帧数据缓存已满，丢弃旧数据")
            val removedFrame = dataList.removeAt(0)
            if (removedFrame.isNotEmpty()) {
                val nalType = removedFrame[0].toInt() and 0x1F
                if (nalType == 5) AppLog.w("警告：丢弃了I帧，可能影响解码质量")
            }
            dataList.add(data)
        }
    }



    /**
     * 处理接收到的H.264数据（零拷贝优化）- 增强连接状态检查
     */
    fun handleH264Data(connectionId: String, payloadView: PayloadView) {
        val startTime = System.nanoTime()
        try {
            // 🎯 根源级修复：严格的连接状态检查
            if (!isConnectionValid(connectionId)) {
                // 如果连接无效，直接丢弃数据，不进行任何处理
                return
            }

            decoders[connectionId]?.let {
                it.decode(payloadView)
                return
            }

            // 🎯 架构修正：RTP只传输帧数据，不检查配置数据
            // 配置数据只通过WebSocket传输，RTP路径不应该包含配置数据
            val h264Data = payloadView.toByteArray()

            // 🎯 根源级修复：在缓存前再次检查连接状态
            if (!isConnectionValid(connectionId)) {
                AppLog.network("连接状态在处理过程中变为无效，丢弃数据: $connectionId")
                return
            }

            cacheFrameData(connectionId, h264Data)

            // 尝试创建解码器
            if (!creatingDecoders.contains(connectionId)) {
                getOrCreateDecoder(connectionId)
            }

            // 监控处理延迟
            val processingTime = (System.nanoTime() - startTime) / 1_000_000
            if (processingTime > 1) {
                AppLog.w("H264数据处理耗时: ${processingTime}ms, 连接: $connectionId")
            }
        } catch (e: Exception) {
            AppLog.e("处理连接 $connectionId 的H.264数据失败", e)
        }
    }

    // 🎯 架构修正：移除配置数据检查函数
    // RTP路径不应该处理配置数据，配置数据只通过WebSocket传输

    /**
     * 获取或创建解码器
     */
    private fun getOrCreateDecoder(connectionId: String): VideoDecoder? {
        if (!isConnectionValid(connectionId)) return null

        decoders[connectionId]?.let { return it }

        if (creatingDecoders.contains(connectionId)) return null

        creatingDecoders.add(connectionId)

        // 再次检查连接状态
        if (!isConnectionValid(connectionId)) {
            creatingDecoders.remove(connectionId)
            return null
        }

        AppLog.network("解码器创建标记已设置，等待UI创建Surface: $connectionId")
        return null
    }

    /**
     * 清理连接相关数据
     * 🚀 增强版：清理分辨率缓存
     */
    private fun cleanupConnectionData(connectionId: String) {
        creatingDecoders.remove(connectionId)
        pendingData.remove(connectionId)
        pendingConfigurationData.remove(connectionId)
        pendingResolutionData.remove(connectionId) // 🚀 清理分辨率缓存
    }

    /**
     * 处理缓存数据 - 架构优化：只处理WebSocket配置数据和RTP帧数据
     * 🚀 增强版：支持分辨率信息处理
     */
    private fun processCachedData(connectionId: String, decoder: VideoDecoder): Boolean {
        var hasConfigData = false

        // 🚀 优先处理缓存的分辨率信息
        pendingResolutionData[connectionId]?.let { (width, height) ->
            decoder.setWebSocketResolution(width, height)
            AppLog.network("已设置缓存的WebSocket分辨率: $connectionId, ${width}x${height}")
        }

        // 🎯 架构优化：只处理WebSocket传输的配置数据
        pendingConfigurationData[connectionId]?.let { configList ->
            if (configList.isNotEmpty()) {
                hasConfigData = true
                configList.forEach {
                    val dataView = VideoDecoder.ByteArrayDataView(it)
                    val payloadView = PayloadView()
                    payloadView.setViewFromByteArray(dataView, 0, it.size)
                    decoder.decode(payloadView)
                }
                pendingConfigurationData.remove(connectionId)
                AppLog.network("已处理连接 $connectionId 的WebSocket缓存配置数据: ${configList.size} 个")
            }
        }

        // 处理RTP传输的帧数据
        pendingData[connectionId]?.let { dataList ->
            dataList.forEach {
                val dataView = VideoDecoder.ByteArrayDataView(it)
                val payloadView = PayloadView()
                payloadView.setViewFromByteArray(dataView, 0, it.size)
                decoder.decode(payloadView)
            }
            pendingData.remove(connectionId)
            AppLog.network("已处理连接 $connectionId 的RTP缓存帧数据: ${dataList.size} 个")
        }

        return hasConfigData
    }

    /**
     * 为连接设置Surface
     */
    fun setSurface(connectionId: String, surface: android.view.Surface?) {
        try {
            AppLog.network("为连接 $connectionId 设置Surface: surface=${surface != null}, isValid=${surface?.isValid}")

            if (surface == null) {
                // 🚀 改进Surface清理逻辑：确保解码器完全停止后再清理数据
                AppLog.network("Surface为null，开始清理连接 $connectionId 的解码器")

                val decoder = decoders[connectionId]
                if (decoder != null) {
                    try {
                        // 🚀 安全停止解码器，使用cleanup方法确保完全清理
                        AppLog.network("停止连接 $connectionId 的解码器...")
                        decoder.cleanup() // 使用cleanup而不是stop，确保完全清理
                        AppLog.network("连接 $connectionId 的解码器已完全停止")
                    } catch (e: Exception) {
                        AppLog.w("停止连接 $connectionId 的解码器时发生异常", e)
                        // 即使停止失败，也要继续清理
                    }

                    // 从映射中移除
                    decoders.remove(connectionId)
                    AppLog.network("已从解码器映射中移除连接: $connectionId")
                }

                // 清理连接数据
                cleanupConnectionData(connectionId)
                AppLog.network("连接 $connectionId 的Surface清理完成")

                // 🚀 添加清理完成确认日志
                AppLog.network("✅ 连接 $connectionId 的MediaCodec和Surface资源已完全清理")
                return
            }

            // 🚀 验证Surface有效性
            if (!surface.isValid) {
                AppLog.w("连接 $connectionId 的Surface无效，跳过设置")
                cleanupConnectionData(connectionId)
                return
            }

            if (!isConnectionValid(connectionId)) {
                AppLog.w("连接 $connectionId 无效，跳过Surface设置")
                cleanupConnectionData(connectionId)
                return
            }

            AppLog.network("为连接 $connectionId 创建新的解码器...")
            val decoder = VideoDecoder(surface)
            if (decoder.start()) {
                // 🚀 在设置新解码器前，确保旧解码器已完全清理
                val oldDecoder = decoders.put(connectionId, decoder)
                if (oldDecoder != null) {
                    AppLog.network("发现连接 $connectionId 的旧解码器，开始清理...")
                    try {
                        oldDecoder.cleanup()
                        AppLog.network("连接 $connectionId 的旧解码器已清理")
                    } catch (e: Exception) {
                        AppLog.w("清理连接 $connectionId 的旧解码器时发生异常", e)
                    }
                }

                creatingDecoders.remove(connectionId)

                val (decoderName, decoderType) = decoder.getDecoderInfo()
                val isHardwareAccelerated = decoder.isUsingHardwareAcceleration()

                AppLog.network("=== 解码器启动完成 ===")
                AppLog.network("连接ID: $connectionId, 解码器: $decoderName ($decoderType)")
                AppLog.network("硬件加速: ${if (isHardwareAccelerated) "是" else "否"}")

                val hasConfigData = processCachedData(connectionId, decoder)
                if (!hasConfigData) {
                    AppLog.w("连接 $connectionId 的解码器已启动，但没有缓存的配置数据")
                }
            } else {
                AppLog.e("为连接 $connectionId 启动解码器失败")
                cleanupConnectionData(connectionId)
            }
        } catch (e: Exception) {
            AppLog.e("为连接 $connectionId 设置Surface失败", e)
            cleanupConnectionData(connectionId)
        }
    }



    /**
     * 更新解码器分辨率
     * 用于处理发送端分辨率实时调整
     */
    fun updateDecoderResolution(connectionId: String, newWidth: Int, newHeight: Int): Boolean {
        return try {
            AppLog.network("更新解码器分辨率: $connectionId, ${newWidth}x${newHeight}")

            val decoder = decoders[connectionId]
            if (decoder != null) {
                // 解码器存在，直接更新分辨率
                decoder.updateResolution(newWidth, newHeight)
                AppLog.network("解码器分辨率更新成功: $connectionId, ${newWidth}x${newHeight}")
                true
            } else {
                AppLog.w("解码器不存在，无法更新分辨率: $connectionId")
                false
            }

        } catch (e: Exception) {
            AppLog.e("更新解码器分辨率失败: $connectionId", e)
            false
        }
    }

    /**
     * 安全停止解码器
     */
    private fun safeStopDecoder(connectionId: String, useCleanup: Boolean = false) {
        decoders[connectionId]?.let { decoder ->
            try {
                if (useCleanup) decoder.cleanup() else decoder.stop()
                AppLog.network("停止连接 $connectionId 的解码器")
            } catch (e: Exception) {
                AppLog.e("停止解码器时发生异常: $connectionId", e)
                if (useCleanup) {
                    try { decoder.stop() } catch (stopException: Exception) {
                        AppLog.e("停止解码器时也发生异常: $connectionId", stopException)
                    }
                }
            }
            decoders.remove(connectionId)
        }
    }

    /**
     * 处理视频流停止（但连接可能仍然存在，用于音频流）
     */
    fun handleVideoStreamStop(connectionId: String) {
        AppLog.network("处理视频流停止: $connectionId")
        videoStreamStoppedConnections.add(connectionId)
        safeStopDecoder(connectionId)
        cleanupConnectionData(connectionId)
        AppLog.network("视频流停止处理完成: $connectionId")
    }

    /**
     * 安全调用断开回调
     */
    private fun safeInvokeDisconnectCallback(connectionId: String, callback: ((String) -> Unit)?) {
        try {
            callback?.invoke(connectionId)
            AppLog.network("已调用连接 $connectionId 的断开回调")
        } catch (e: Exception) {
            AppLog.e("调用断开回调时发生异常: $connectionId", e)
        }
    }

    /**
     * 处理连接断开
     */
    fun handleConnectionDisconnect(connectionId: String) {
        if (disconnectedConnections.contains(connectionId)) {
            AppLog.network("连接 $connectionId 的断开事件已处理，跳过重复处理")
            return
        }

        AppLog.network("开始处理连接断开: $connectionId")
        disconnectedConnections.add(connectionId)
        videoStreamStoppedConnections.remove(connectionId)

        safeStopDecoder(connectionId, useCleanup = true)
        cleanupConnectionData(connectionId)

        // 调用断开回调
        ensureDisconnectCallbackRegistered(connectionId)
        val callback = disconnectCallbacks[connectionId] ?: disconnectCallbacks["default"]
        safeInvokeDisconnectCallback(connectionId, callback)

        AppLog.network("连接断开处理完成: $connectionId")
    }

    /**
     * 清理指定连接的断开标记，允许重连（统一ID架构）- 根源级清理
     */
    fun clearDisconnectionFlag(connectionId: String) {
        val wasDisconnected = disconnectedConnections.remove(connectionId)
        val wasVideoStopped = videoStreamStoppedConnections.remove(connectionId)

        // 🎯 根源级修复：彻底清理所有残留数据
        if (wasDisconnected || wasVideoStopped) {
            AppLog.network("清理连接 $connectionId 的断开标记和视频流停止状态")

            // 强制清理所有残留的缓存数据
            val pendingFrames = pendingData.remove(connectionId)
            val pendingConfig = pendingConfigurationData.remove(connectionId)

            if (pendingFrames?.isNotEmpty() == true) {
                AppLog.network("清理连接 $connectionId 的 ${pendingFrames.size} 个残留帧数据")
            }
            if (pendingConfig?.isNotEmpty() == true) {
                AppLog.network("清理连接 $connectionId 的 ${pendingConfig.size} 个残留配置数据")
            }

            // 清理正在创建的解码器标记
            if (creatingDecoders.remove(connectionId)) {
                AppLog.network("清理连接 $connectionId 的解码器创建标记")
            }

            // 如果存在旧的解码器，强制清理
            decoders[connectionId]?.let { decoder ->
                AppLog.network("发现连接 $connectionId 的旧解码器，强制清理")
                try {
                    decoder.cleanup()
                } catch (e: Exception) {
                    AppLog.w("清理旧解码器时发生异常", e)
                    try { decoder.stop() } catch (stopException: Exception) {
                        AppLog.w("停止旧解码器时也发生异常", stopException)
                    }
                }
                decoders.remove(connectionId)
            }

            AppLog.network("连接 $connectionId 的所有残留数据已彻底清理，可以重新连接")
        }
    }

    /**
     * 获取所有活跃连接ID
     */
    fun getAllConnectionIds(): Set<String> {
        val allConnectionIds = ConcurrentHashMap.newKeySet<String>()

        // 从解码器映射中获取连接ID
        allConnectionIds.addAll(decoders.keys)

        // 从正在创建的解码器中获取连接ID
        allConnectionIds.addAll(creatingDecoders)

        // 从待处理数据中获取连接ID
        allConnectionIds.addAll(pendingData.keys)

        // 从待处理配置数据中获取连接ID
        allConnectionIds.addAll(pendingConfigurationData.keys)

        // 排除已断开的连接
        allConnectionIds.removeAll(disconnectedConnections)

        AppLog.network("获取所有活跃连接ID: $allConnectionIds")
        return allConnectionIds
    }

    /**
     * 清理视频流停止状态，允许视频流重新启动（统一ID架构）- 根源级修复
     */
    fun clearVideoStreamStoppedState(connectionId: String) {
        val wasVideoStopped = videoStreamStoppedConnections.remove(connectionId)
        val wasDisconnected = disconnectedConnections.remove(connectionId)

        if (wasVideoStopped || wasDisconnected) {
            AppLog.network("清理视频流停止状态和断开标记: $connectionId")

            // 🎯 根源级修复：同时清理所有可能阻止重连的状态
            // 强制清理所有残留的缓存数据
            val pendingFrames = pendingData.remove(connectionId)
            val pendingConfig = pendingConfigurationData.remove(connectionId)

            if (pendingFrames?.isNotEmpty() == true) {
                AppLog.network("清理连接 $connectionId 的 ${pendingFrames.size} 个残留帧数据")
            }
            if (pendingConfig?.isNotEmpty() == true) {
                AppLog.network("清理连接 $connectionId 的 ${pendingConfig.size} 个残留配置数据")
            }

            // 清理正在创建的解码器标记
            if (creatingDecoders.remove(connectionId)) {
                AppLog.network("清理连接 $connectionId 的解码器创建标记")
            }

            // 如果存在旧的解码器，强制清理
            decoders[connectionId]?.let { decoder ->
                AppLog.network("发现连接 $connectionId 的旧解码器，强制清理")
                try {
                    decoder.cleanup()
                } catch (e: Exception) {
                    AppLog.w("清理旧解码器时发生异常", e)
                    try { decoder.stop() } catch (stopException: Exception) {
                        AppLog.w("停止旧解码器时也发生异常", stopException)
                    }
                }
                decoders.remove(connectionId)
            }

            AppLog.network("连接 $connectionId 的所有状态已彻底清理，可以重新投屏")
        } else {
            AppLog.network("视频流停止状态不存在或已清理: $connectionId")
        }
    }

    /**
     * 清理所有连接
     */
    fun cleanup() {
        val allConnectionIds = ConcurrentHashMap.newKeySet<String>().apply {
            addAll(getAllConnectionIds())
            addAll(decoders.keys)
            addAll(creatingDecoders)
            addAll(pendingData.keys)
            addAll(pendingConfigurationData.keys)
        }

        AppLog.network("准备清理 ${allConnectionIds.size} 个连接")

        // 通知UI移除投屏窗口
        allConnectionIds.forEach { connectionId ->
            ensureDisconnectCallbackRegistered(connectionId)
            val callback = disconnectCallbacks[connectionId]
            safeInvokeDisconnectCallback(connectionId, callback)
        }

        // 清理所有解码器
        decoders.values.forEach { decoder ->
            try {
                decoder.cleanup()
            } catch (e: Exception) {
                AppLog.e("清理解码器资源时发生异常", e)
                try { decoder.stop() } catch (stopException: Exception) {
                    AppLog.e("停止解码器时也发生异常", stopException)
                }
            }
        }

        // 清理所有数据结构
        decoders.clear()
        creatingDecoders.clear()
        pendingData.clear()
        pendingConfigurationData.clear()
        displayCallbacks.clear()
        disconnectCallbacks.clear()
        screenResolutionCallbacks.clear()
        disconnectedConnections.clear()
        videoStreamStoppedConnections.clear()

        AppLog.network("已清理所有 ${allConnectionIds.size} 个连接")
    }


}
