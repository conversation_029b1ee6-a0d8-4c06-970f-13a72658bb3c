package com.example.castapp.manager

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager as AndroidMediaProjectionManager
import android.os.Build
import android.util.DisplayMetrics
import android.view.Surface
import android.view.WindowManager
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger
import java.lang.ref.WeakReference
import com.example.castapp.utils.AppLog

/**
 * 统一的MediaProjection管理器
 * 整合了原有的UnifiedMediaProjectionManager、MediaProjectionHelper和MediaAudioProjectionHelper
 * 解决Android系统同时只能有一个MediaProjection实例的限制
 * 实现投屏和媒体音频功能的权限复用
 */
class MediaProjectionManager private constructor(private val context: Context) {

    companion object {
        // 功能类型常量
        const val FEATURE_SCREEN_CASTING = "SCREEN_CASTING"
        const val FEATURE_MEDIA_AUDIO = "MEDIA_AUDIO"
        const val FEATURE_APP_STARTUP_CACHE = "APP_STARTUP_CACHE"  // 🔥 新增：APP启动时的权限缓存

        @Volatile
        @SuppressLint("StaticFieldLeak") // 使用ApplicationContext，安全持有
        private var INSTANCE: MediaProjectionManager? = null

        /**
         * 获取单例实例
         */
        fun getInstance(context: Context): MediaProjectionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: MediaProjectionManager(context.applicationContext).also {
                    INSTANCE = it
                }
            }
        }
    }

    // Android系统MediaProjectionManager
    private var mediaProjectionManager: AndroidMediaProjectionManager? = null

    // MediaProjection实例（全局唯一）
    private var mediaProjection: MediaProjection? = null

    // MediaProjection回调（Android 14+要求）
    private var mediaProjectionCallback: MediaProjection.Callback? = null

    // 权限相关状态
    private var isPermissionGranted = false
    private var cachedResultCode = 0
    private var cachedResultData: Intent? = null

    // Android 14+设备检测
    private val isAndroid14PlusDevice = (Build.VERSION.SDK_INT >= 34)

    // 使用计数器（跟踪有多少功能在使用MediaProjection）
    private val usageCounter = AtomicInteger(0)

    // 跟踪各功能的使用状态
    private val featureUsage = ConcurrentHashMap<String, Boolean>()

    // MediaProjection状态监听器（使用WeakReference防止内存泄漏）
    private val stateListeners = ConcurrentHashMap.newKeySet<WeakReference<MediaProjectionStateListener>>()

    // 屏幕捕获相关
    private var virtualDisplay: VirtualDisplay? = null

    /**
     * MediaProjection状态监听接口
     */
    interface MediaProjectionStateListener {
        fun onMediaProjectionCreated(mediaProjection: MediaProjection)
        fun onMediaProjectionStopped()
    }

    /**
     * MediaProjection回调实现（Android 14+要求）
     */
    private inner class MediaProjectionCallbackImpl : MediaProjection.Callback() {
        override fun onStop() {
            AppLog.d("MediaProjection.Callback: onStop() 被调用")
            // 通知监听器MediaProjection已停止
            notifyMediaProjectionStopped()
        }

        override fun onCapturedContentResize(width: Int, height: Int) {
            AppLog.d("MediaProjection.Callback: 捕获内容尺寸变化 ${width}x${height}")
        }

        override fun onCapturedContentVisibilityChanged(isVisible: Boolean) {
            AppLog.d("MediaProjection.Callback: 捕获内容可见性变化 $isVisible")
        }
    }

    init {
        mediaProjectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as AndroidMediaProjectionManager
        AppLog.d("统一MediaProjection管理器初始化完成")
    }

    /**
     * 安全执行代码块，统一异常处理
     */
    private inline fun safeExecute(operation: String, block: () -> Unit) {
        try {
            block()
        } catch (e: Exception) {
            AppLog.e("$operation 失败", e)
        }
    }

    /**
     * 安全释放资源
     */
    private fun safeRelease(resource: Any?, resourceName: String, releaseAction: () -> Unit) {
        resource?.let {
            try {
                releaseAction()
                AppLog.d("$resourceName 已释放")
            } catch (e: Exception) {
                AppLog.w("释放 $resourceName 时发生异常", e)
            }
        }
    }

    /**
     * 获取MediaProjection实例
     */
    fun getMediaProjection(): MediaProjection? {
        return mediaProjection
    }

    /**
     * 检查是否有有效的MediaProjection权限
     * 现在只检查权限缓存是否存在，不检查MediaProjection实例
     * 因为实例可能被清理但权限缓存仍然有效
     */
    fun hasValidPermission(): Boolean {
        return isPermissionGranted && cachedResultData != null
    }

    /**
     * 验证MediaProjection实例是否仍然有效
     */
    private fun isMediaProjectionValid(): Boolean {
        val projection = mediaProjection ?: return false

        return try {
            // 如果已有VirtualDisplay在运行，直接认为有效
            if (virtualDisplay != null) {
                AppLog.d("MediaProjection有效（基于现有VirtualDisplay）")
                return true
            }

            // 创建最小测试VirtualDisplay验证有效性
            val testDisplay = projection.createVirtualDisplay(
                "ValidityTest", 1, 1, 1,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                null, null, null
            )
            testDisplay?.release()
            AppLog.d("MediaProjection验证: 有效")
            true
        } catch (e: Exception) {
            AppLog.w("MediaProjection验证: 无效 - ${e.message}")
            false
        }
    }

    /**
     * 获取缓存的权限数据
     */
    fun getCachedPermissionData(): Pair<Int, Intent>? {
        return cachedResultData?.let { data ->
            Pair(cachedResultCode, data)
        }
    }

    /**
     * 使用权限结果数据启动MediaProjection
     */
    fun startMediaProjectionWithPermission(
        resultCode: Int,
        resultData: Intent?,
        featureType: String
    ): Boolean {
        return try {
            if (resultData == null) {
                AppLog.e("权限结果数据为空")
                return false
            }

            cachedResultCode = resultCode
            cachedResultData = resultData
            isPermissionGranted = true

            // 异步注册功能使用
            Thread {
                safeExecute("异步注册功能使用") {
                    registerFeatureUsage(featureType)
                    AppLog.d("权限数据已缓存，功能: $featureType")
                }
            }.start()

            true
        } catch (e: Exception) {
            AppLog.e("缓存权限数据失败", e)
            false
        }
    }

    /**
     * 在前台服务中创建MediaProjection实例
     * 这是Android 14+的要求
     */
    fun createMediaProjectionInForegroundService(featureType: String): Boolean {
        return cachedResultData?.let { data ->
            if (isPermissionGranted) {
                AppLog.d("在前台服务中创建MediaProjection，功能: $featureType")
                createMediaProjection(cachedResultCode, data, featureType)
            } else {
                AppLog.e("无法在前台服务中创建MediaProjection：权限未授予")
                false
            }
        } ?: run {
            AppLog.e("无法在前台服务中创建MediaProjection：权限数据不可用")
            false
        }
    }

    /**
     * 创建MediaProjection实例
     */
    private fun createMediaProjection(resultCode: Int, resultData: Intent, featureType: String): Boolean {
        return try {
            AppLog.d("请求创建MediaProjection，功能: $featureType，Android 14+: $isAndroid14PlusDevice")

            // Android 14+投屏功能：强制重新创建
            if (isAndroid14PlusDevice && featureType == FEATURE_SCREEN_CASTING) {
                cleanupForAndroid14ScreenCasting()
            } else {
                // 尝试复用现有实例
                val reuseResult = tryReuseExistingProjection(resultCode, resultData, featureType)
                if (reuseResult != null) return reuseResult
            }

            // 清理现有实例（如果需要）
            cleanupExistingProjection()

            // 创建新实例
            return createNewMediaProjection(resultCode, resultData, featureType)

        } catch (e: Exception) {
            AppLog.e("创建MediaProjection失败，功能: $featureType", e)
            isPermissionGranted = false
            false
        }
    }

    private fun cleanupForAndroid14ScreenCasting() {
        AppLog.d("Android 14+投屏：强制重新创建MediaProjection实例")
        mediaProjection?.let {
            it.stop()
            mediaProjection = null
            mediaProjectionCallback = null
            featureUsage.clear()
            usageCounter.set(0)
        }
    }

    private fun tryReuseExistingProjection(resultCode: Int, resultData: Intent, featureType: String): Boolean? {
        if (mediaProjection == null || !isPermissionGranted) return null

        val isSamePermissionData = (cachedResultCode == resultCode &&
            cachedResultData?.toString() == resultData.toString())

        if (isSamePermissionData && isMediaProjectionValid()) {
            AppLog.d("复用现有MediaProjection实例，功能: $featureType")
            if (!featureUsage.containsKey(featureType)) {
                registerFeatureUsage(featureType)
                AppLog.d("功能 $featureType 已注册")
            }
            return true
        }

        // 特殊处理：投屏运行时的媒体音频请求
        if (featureUsage.containsKey(FEATURE_SCREEN_CASTING) && featureType == FEATURE_MEDIA_AUDIO) {
            AppLog.w("投屏运行中，媒体音频强制复用现有实例")
            if (!featureUsage.containsKey(featureType)) {
                registerFeatureUsage(featureType)
            }
            return true
        }

        cleanupInvalidMediaProjectionInstance()
        return null
    }

    private fun cleanupExistingProjection() {
        mediaProjection?.let {
            AppLog.d("清理现有MediaProjection实例")
            it.stop()
            mediaProjection = null
            featureUsage.clear()
            usageCounter.set(0)
        }
    }

    private fun createNewMediaProjection(resultCode: Int, resultData: Intent, featureType: String): Boolean {
        AppLog.d("创建新的MediaProjection实例，功能: $featureType")
        mediaProjection = mediaProjectionManager?.getMediaProjection(resultCode, resultData)

        if (mediaProjection == null) {
            AppLog.e("无法创建MediaProjection实例")
            isPermissionGranted = false
            return false
        }

        // 注册回调（Android 14+要求）
        mediaProjectionCallback = MediaProjectionCallbackImpl()
        mediaProjection?.registerCallback(mediaProjectionCallback!!, null)
        AppLog.d("MediaProjection回调已注册")

        isPermissionGranted = true
        registerFeatureUsage(featureType)

        mediaProjection?.let { projection ->
            notifyMediaProjectionCreated(projection)
            AppLog.d("MediaProjection创建成功，功能: $featureType")
            return true
        }

        AppLog.e("MediaProjection创建后为null")
        return false
    }

    /**
     * 注册功能使用
     */
    private fun registerFeatureUsage(featureType: String) {
        // 检查是否已经注册过该功能
        if (featureUsage.containsKey(featureType)) {
            AppLog.d("功能 $featureType 已经注册过，跳过重复注册")
            return
        }

        featureUsage[featureType] = true
        val count = usageCounter.incrementAndGet()
        AppLog.d("注册功能使用: $featureType, 当前使用计数: $count")
        AppLog.d("当前已注册功能: ${featureUsage.keys}")
    }

    /**
     * 取消注册功能使用
     */
    fun unregisterFeatureUsage(featureType: String) {
        // 检查功能是否已注册
        if (!featureUsage.containsKey(featureType)) {
            AppLog.w("尝试取消注册未注册的功能: $featureType")
            return
        }

        featureUsage.remove(featureType)
        val count = usageCounter.decrementAndGet()
        AppLog.d("取消注册功能使用: $featureType, 当前使用计数: $count")
        AppLog.d("剩余已注册功能: ${featureUsage.keys}")

        // 如果没有功能在使用，停止MediaProjection
        if (count <= 0) {
            AppLog.d("所有功能已停止，准备停止MediaProjection")
            stopMediaProjection()
        } else {
            AppLog.d("还有其他功能在使用MediaProjection，保持实例运行")
        }
    }

    /**
     * 停止MediaProjection
     */
    fun stopMediaProjection() {
        AppLog.d("开始停止MediaProjection，当前注册功能: ${featureUsage.keys}")

        // 释放VirtualDisplay
        safeRelease(virtualDisplay, "VirtualDisplay") { virtualDisplay?.release() }
        virtualDisplay = null

        // 注销MediaProjection回调
        mediaProjection?.let { projection ->
            mediaProjectionCallback?.let { callback ->
                safeRelease(callback, "MediaProjection回调") { projection.unregisterCallback(callback) }
            }
        }
        mediaProjectionCallback = null

        // 停止MediaProjection
        safeRelease(mediaProjection, "MediaProjection") { mediaProjection?.stop() }
        mediaProjection = null

        // 重置使用状态，保留权限缓存
        val previousCount = usageCounter.get()
        val previousFeatures = featureUsage.keys.toSet()
        usageCounter.set(0)
        featureUsage.clear()

        AppLog.d("使用计数从 $previousCount 重置为 0，功能从 $previousFeatures 清空")

        // 通知监听器
        safeExecute("通知MediaProjection停止") { notifyMediaProjectionStopped() }
        AppLog.d("MediaProjection资源清理完成，权限缓存已保留")
    }

    /**
     * 清理缓存的权限数据
     */
    fun clearCachedPermission() {
        cachedResultCode = 0
        cachedResultData = null
        isPermissionGranted = false
        AppLog.d("已清理缓存的权限数据")
    }

    /**
     * 清理无效的MediaProjection实例，保留权限缓存
     */
    private fun cleanupInvalidMediaProjectionInstance() {
        try {
            AppLog.d("清理无效MediaProjection实例，当前功能: ${featureUsage.keys}")
            val currentFeatures = featureUsage.keys.toSet()
            val currentCount = usageCounter.get()

            // 注销回调并停止实例
            mediaProjection?.let { projection ->
                mediaProjectionCallback?.let { callback ->
                    safeRelease(callback, "无效MediaProjection回调") { projection.unregisterCallback(callback) }
                }
                safeRelease(projection, "无效MediaProjection") { projection.stop() }
            }
            mediaProjectionCallback = null
            mediaProjection = null

            // 重置使用状态，保留权限缓存
            usageCounter.set(0)
            featureUsage.clear()

            AppLog.d("无效实例已清理，之前状态: $currentFeatures (计数: $currentCount)，权限缓存已保留")
        } catch (e: Exception) {
            AppLog.e("清理无效MediaProjection实例失败", e)
        }
    }


    // ===== 屏幕捕获功能 =====

    /**
     * 启动屏幕捕获
     */
    fun startScreenCapture(data: Intent?, surface: Surface): Boolean {
        return try {
            if (data == null) {
                AppLog.e("Intent数据为空")
                return false
            }

            if (!createMediaProjectionInForegroundService(FEATURE_SCREEN_CASTING)) {
                AppLog.e("无法创建MediaProjection")
                return false
            }

            val mediaProjection = getMediaProjection() ?: run {
                AppLog.e("MediaProjection实例为空")
                return false
            }

            // 创建VirtualDisplay
            val resolutionManager = ResolutionManager.getInstance(context)
            val currentScale = resolutionManager.getCurrentResolutionScale()
            val (outputWidth, outputHeight) = resolutionManager.calculateCaptureResolution(currentScale)
            val originalMetrics = getScreenMetrics()

            // 🚀 修复音频卡顿：异步创建VirtualDisplay，避免阻塞音频播放
            virtualDisplay = mediaProjection.createVirtualDisplay(
                "ScreenCapture", outputWidth, outputHeight, originalMetrics.densityDpi,
                // 🚀 关键修复：移除AUTO_MIRROR标志，避免影响音频焦点
                DisplayManager.VIRTUAL_DISPLAY_FLAG_PRESENTATION,
                surface, null, null
            )

            AppLog.d("屏幕捕获启动成功，音频播放不受影响: 捕获区域 ${originalMetrics.widthPixels}x${originalMetrics.heightPixels}, 输出 ${outputWidth}x${outputHeight} (${currentScale}%)")
            true
        } catch (e: Exception) {
            AppLog.e("启动屏幕捕获失败", e)
            false
        }
    }

    /**
     * 停止屏幕捕获
     */
    fun stopScreenCapture() {
        safeRelease(virtualDisplay, "屏幕捕获VirtualDisplay") { virtualDisplay?.release() }
        virtualDisplay = null
        safeExecute("注销屏幕捕获功能") { unregisterFeatureUsage(FEATURE_SCREEN_CASTING) }
        AppLog.d("屏幕捕获已停止")
    }



    /**
     * 获取屏幕尺寸信息的通用方法
     */
    private fun getScreenMetrics(): DisplayMetrics {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val metrics = DisplayMetrics()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val windowMetrics = windowManager.currentWindowMetrics
            val bounds = windowMetrics.bounds
            metrics.widthPixels = bounds.width()
            metrics.heightPixels = bounds.height()
            metrics.densityDpi = context.resources.displayMetrics.densityDpi
        } else {
            @Suppress("DEPRECATION")
            windowManager.defaultDisplay.getRealMetrics(metrics)
        }

        return metrics
    }

    /**
     * 使用新分辨率重启屏幕捕获
     */
    fun restartScreenCaptureWithNewResolution(surface: Surface, newWidth: Int, newHeight: Int): Boolean {
        return try {
            AppLog.d("调整屏幕捕获分辨率: ${newWidth}x${newHeight}")

            val projection = mediaProjection ?: return recreateEntireMediaProjection(surface, newWidth, newHeight)

            // 检查VirtualDisplay是否存在，如果不存在则重新创建
            if (virtualDisplay == null) {
                return recreateVirtualDisplayWithNewResolution(projection, surface, newWidth, newHeight)
            }

            // 🚀 修复：强制重新创建以确保全屏捕获（Android 12和13统一行为）
            // resize方法在某些Android版本上可能不会重新进行全屏捕获
            AppLog.d("强制重新创建VirtualDisplay以确保全屏捕获: ${newWidth}x${newHeight}")
            recreateVirtualDisplayWithNewResolution(projection, surface, newWidth, newHeight)
        } catch (e: Exception) {
            AppLog.e("调整屏幕捕获分辨率失败", e)
            recreateEntireMediaProjection(surface, newWidth, newHeight)
        }
    }

    /**
     * 重新创建整个MediaProjection
     */
    private fun recreateEntireMediaProjection(surface: Surface, newWidth: Int, newHeight: Int): Boolean {
        return try {
            AppLog.w("重新创建整个MediaProjection: ${newWidth}x${newHeight}")

            if (cachedResultData == null) {
                AppLog.e("没有缓存的权限数据，无法重新创建MediaProjection")
                return false
            }

            // 停止当前实例但保留权限缓存
            stopMediaProjection()

            // 重新创建MediaProjection
            if (!createMediaProjectionInForegroundService(FEATURE_SCREEN_CASTING)) {
                AppLog.e("重新创建MediaProjection失败")
                return false
            }

            val newProjection = mediaProjection ?: run {
                AppLog.e("重新创建的MediaProjection实例为空")
                return false
            }

            // 创建新的VirtualDisplay
            val originalMetrics = getScreenMetrics()
            // 🚀 修复音频卡顿：重新创建时也避免影响音频焦点
            virtualDisplay = newProjection.createVirtualDisplay(
                "ScreenCapture_Recreated", newWidth, newHeight, originalMetrics.densityDpi,
                // 🚀 关键修复：移除AUTO_MIRROR标志，避免影响音频焦点
                DisplayManager.VIRTUAL_DISPLAY_FLAG_PRESENTATION,
                surface, null, null
            )

            AppLog.d("MediaProjection重新创建成功: 捕获区域 ${originalMetrics.widthPixels}x${originalMetrics.heightPixels}, 输出 ${newWidth}x${newHeight}")
            true
        } catch (e: Exception) {
            AppLog.e("重新创建整个MediaProjection失败", e)
            false
        }
    }

    /**
     * 重新创建VirtualDisplay
     */
    private fun recreateVirtualDisplayWithNewResolution(
        projection: MediaProjection,
        surface: Surface,
        newWidth: Int,
        newHeight: Int
    ): Boolean {
        return try {
            AppLog.d("重新创建VirtualDisplay: ${newWidth}x${newHeight}")

            // 释放旧的VirtualDisplay
            safeRelease(virtualDisplay, "旧VirtualDisplay") { virtualDisplay?.release() }
            virtualDisplay = null

            // 创建新的VirtualDisplay
            val originalMetrics = getScreenMetrics()
            // 🚀 修复音频卡顿：分辨率调整时也避免影响音频焦点
            virtualDisplay = projection.createVirtualDisplay(
                "ScreenCapture_NewResolution", newWidth, newHeight, originalMetrics.densityDpi,
                // 🚀 关键修复：移除AUTO_MIRROR标志，避免影响音频焦点
                DisplayManager.VIRTUAL_DISPLAY_FLAG_PRESENTATION,
                surface, null, null
            )

            AppLog.d("VirtualDisplay重新创建成功: 捕获区域 ${originalMetrics.widthPixels}x${originalMetrics.heightPixels}, 输出 ${newWidth}x${newHeight}")
            true
        } catch (e: Exception) {
            AppLog.e("重新创建VirtualDisplay失败", e)
            false
        }
    }



    // ===== 媒体音频功能 =====

    /**
     * 启动媒体音频MediaProjection
     */
    fun startMediaAudioProjection(data: Intent?): Boolean {
        return try {
            if (data == null) {
                AppLog.e("Intent数据为空")
                return false
            }

            val success = createMediaProjectionInForegroundService(FEATURE_MEDIA_AUDIO)
            if (!success) {
                AppLog.e("无法创建MediaProjection")
                return false
            }

            AppLog.d("媒体音频MediaProjection启动成功")
            true
        } catch (e: Exception) {
            AppLog.e("启动媒体音频MediaProjection失败", e)
            false
        }
    }

    /**
     * 停止媒体音频但保留权限
     */
    fun stopMediaAudioProjectionKeepPermission() {
        safeExecute("停止媒体音频") {
            AppLog.d("媒体音频已停止，权限已保留")
        }
    }

    /**
     * 清理失效的监听器引用
     */
    private fun cleanupListeners() {
        stateListeners.removeAll { it.get() == null }
    }

    /**
     * 通知MediaProjection已创建
     */
    private fun notifyMediaProjectionCreated(mediaProjection: MediaProjection) {
        cleanupListeners()
        stateListeners.forEach { it.get()?.onMediaProjectionCreated(mediaProjection) }
    }

    /**
     * 通知MediaProjection已停止
     */
    private fun notifyMediaProjectionStopped() {
        cleanupListeners()
        stateListeners.forEach { it.get()?.onMediaProjectionStopped() }
    }

    /**
     * 释放所有资源
     */
    fun release() {
        stopMediaProjection()
        clearCachedPermission()
        stateListeners.clear()
        mediaProjectionCallback = null
        AppLog.d("MediaProjection管理器已释放")
    }
}
