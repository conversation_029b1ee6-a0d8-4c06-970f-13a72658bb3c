package com.example.castapp.manager

import android.content.Context
import androidx.core.content.edit
import com.example.castapp.model.RemoteSenderConnection
import com.example.castapp.remote.RemoteSenderWebSocketClient
import com.example.castapp.ui.dialog.RemoteSenderControlDialog
import com.example.castapp.utils.AppLog
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * 🐾 远程连接全局管理器
 * 负责管理所有远程连接的全局状态，包括WebSocket客户端、连接状态和控制对话框
 * 
 * 主要功能：
 * - 全局WebSocket客户端管理（发送端和接收端）
 * - 连接状态的持久化存储和恢复
 * - 活跃控制对话框的生命周期管理
 * - 线程安全的状态操作
 */
class RemoteConnectionManager private constructor() {

    companion object {
        @Volatile
        private var INSTANCE: RemoteConnectionManager? = null

        fun getInstance(): RemoteConnectionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: RemoteConnectionManager().also { INSTANCE = it }
            }
        }
    }

    // 🐾 全局WebSocket客户端管理器
    private val globalRemoteClients = mutableMapOf<String, RemoteSenderWebSocketClient>()
    private val globalReceiverClients = mutableMapOf<String, RemoteSenderWebSocketClient>()
    
    // 🐾 活跃控制对话框管理器
    private val globalActiveControlDialogs = mutableMapOf<String, RemoteSenderControlDialog>()
    
    // 🐾 全局连接状态副本，独立于UI组件
    private val globalConnectionStates = mutableMapOf<String, RemoteSenderConnection>()
    
    private val gson = Gson()

    // ========== 发送端WebSocket客户端管理 ==========

    /**
     * 获取发送端远程连接客户端
     */
    fun getRemoteClient(connectionId: String): RemoteSenderWebSocketClient? {
        return synchronized(globalRemoteClients) {
            globalRemoteClients[connectionId]
        }
    }

    /**
     * 添加发送端远程连接客户端
     */
    fun addRemoteClient(connectionId: String, client: RemoteSenderWebSocketClient) {
        synchronized(globalRemoteClients) {
            globalRemoteClients[connectionId] = client
        }
        AppLog.d("添加远程连接到全局管理器: $connectionId")
    }

    /**
     * 移除发送端远程连接客户端
     */
    fun removeRemoteClient(connectionId: String) {
        val client = synchronized(globalRemoteClients) {
            globalRemoteClients.remove(connectionId)
        }
        
        client?.let {
            it.disconnect()
            AppLog.d("从全局管理器移除并断开远程连接: $connectionId")
        }
        
        // 同时清理对应的控制对话框
        removeActiveControlDialog(connectionId)
    }

    /**
     * 获取所有发送端远程连接的状态
     */
    fun getAllRemoteConnectionStates(): Map<String, Boolean> {
        return synchronized(globalRemoteClients) {
            globalRemoteClients.mapValues { (_, client) ->
                client.getConnectionStatus()
            }
        }
    }

    // ========== 接收端WebSocket客户端管理 ==========

    /**
     * 添加接收端WebSocket客户端
     */
    fun addReceiverClient(receiverId: String, client: RemoteSenderWebSocketClient) {
        synchronized(globalReceiverClients) {
            globalReceiverClients[receiverId] = client
        }
        AppLog.d("添加接收端连接到全局管理器: $receiverId")
    }

    /**
     * 🚀 高效移除接收端WebSocket客户端
     * 优化：原子化操作，避免资源泄漏
     */
    fun removeReceiverClient(receiverId: String) {
        val client = synchronized(globalReceiverClients) {
            globalReceiverClients.remove(receiverId)
        }
        
        if (client != null) {
            try {
                // 立即断开连接，不发送额外消息（避免重复处理）
                client.disconnect()
                AppLog.d("✅ 从全局管理器移除并断开接收端连接: $receiverId")
            } catch (e: Exception) {
                AppLog.e("❌ 断开接收端连接时发生异常: $receiverId", e)
                // 即使断开失败，也要确保从管理器中移除，避免内存泄漏
            }
        } else {
            AppLog.d("🔍 接收端客户端不存在于全局管理器中: $receiverId")
        }
    }

    /**
     * 获取接收端WebSocket客户端
     */
    fun getReceiverClient(receiverId: String): RemoteSenderWebSocketClient? {
        return synchronized(globalReceiverClients) {
            globalReceiverClients[receiverId]
        }
    }

    // ========== 活跃控制对话框管理 ==========

    /**
     * 添加活跃的控制对话框
     */
    fun addActiveControlDialog(connectionId: String, dialog: RemoteSenderControlDialog) {
        synchronized(globalActiveControlDialogs) {
            globalActiveControlDialogs[connectionId] = dialog
        }
        AppLog.d("添加控制对话框到全局管理器: $connectionId")
    }

    /**
     * 移除活跃的控制对话框
     */
    fun removeActiveControlDialog(connectionId: String) {
        synchronized(globalActiveControlDialogs) {
            globalActiveControlDialogs.remove(connectionId)
        }
        AppLog.d("从全局管理器移除控制对话框: $connectionId")
    }

    /**
     * 获取活跃的控制对话框
     */
    fun getActiveControlDialog(connectionId: String): RemoteSenderControlDialog? {
        return synchronized(globalActiveControlDialogs) {
            globalActiveControlDialogs[connectionId]
        }
    }

    // ========== 全局连接状态管理 ==========

    /**
     * 🐾 更新全局连接状态
     */
    fun updateGlobalConnectionState(connection: RemoteSenderConnection) {
        synchronized(globalConnectionStates) {
            globalConnectionStates[connection.id] = connection
        }
        AppLog.d("✅ 更新全局连接状态: ${connection.deviceName} -> ${if (connection.isConnected) "已连接" else "未连接"}")
    }

    /**
     * 🐾 获取全局连接状态列表
     */
    fun getGlobalConnectionStates(): List<RemoteSenderConnection> {
        return synchronized(globalConnectionStates) {
            globalConnectionStates.values.toList()
        }
    }

    /**
     * 🐾 移除全局连接状态
     */
    fun removeGlobalConnectionState(connectionId: String) {
        synchronized(globalConnectionStates) {
            globalConnectionStates.remove(connectionId)
        }
        AppLog.d("🗑️ 移除全局连接状态: $connectionId")
    }

    // ========== 状态持久化管理 ==========

    /**
     * 🐾 独立的状态保存方法，不依赖Fragment
     */
    fun saveConnectionsToPreferences(context: Context) {
        try {
            val sharedPrefs = context.getSharedPreferences("remote_control_settings", Context.MODE_PRIVATE)
            val connectionsJson = synchronized(globalConnectionStates) {
                gson.toJson(globalConnectionStates.values.toList())
            }
            
            sharedPrefs.edit {
                putString("remote_connections", connectionsJson)
            }
            
            val connectionCount = synchronized(globalConnectionStates) { globalConnectionStates.size }
            AppLog.d("✅ 连接状态已保存到SharedPreferences (${connectionCount}个连接)")
        } catch (e: Exception) {
            AppLog.e("❌ 保存连接状态失败", e)
        }
    }

    /**
     * 🐾 初始化全局连接状态（从SharedPreferences加载）
     */
    fun initializeGlobalConnectionStates(context: Context) {
        try {
            val sharedPrefs = context.getSharedPreferences("remote_control_settings", Context.MODE_PRIVATE)
            val connectionsJson = sharedPrefs.getString("remote_connections", "[]")
            val type = object : TypeToken<List<RemoteSenderConnection>>() {}.type
            val savedConnections: List<RemoteSenderConnection> = gson.fromJson(connectionsJson, type) ?: emptyList()

            synchronized(globalConnectionStates) {
                globalConnectionStates.clear()
                savedConnections.forEach { connection ->
                    globalConnectionStates[connection.id] = connection
                }
            }
            
            AppLog.d("🔄 初始化全局连接状态: ${savedConnections.size}个连接")
        } catch (e: Exception) {
            AppLog.e("❌ 初始化全局连接状态失败", e)
        }
    }

    /**
     * 清理所有资源（用于应用退出时）
     */
    fun cleanup() {
        synchronized(globalRemoteClients) {
            globalRemoteClients.values.forEach { it.disconnect() }
            globalRemoteClients.clear()
        }
        
        synchronized(globalReceiverClients) {
            globalReceiverClients.values.forEach { it.disconnect() }
            globalReceiverClients.clear()
        }
        
        synchronized(globalActiveControlDialogs) {
            globalActiveControlDialogs.clear()
        }
        
        synchronized(globalConnectionStates) {
            globalConnectionStates.clear()
        }
        
        AppLog.d("🧹 RemoteConnectionManager 资源清理完成")
    }
}
