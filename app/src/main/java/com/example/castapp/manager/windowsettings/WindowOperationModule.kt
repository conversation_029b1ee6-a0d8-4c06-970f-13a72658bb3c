package com.example.castapp.manager.windowsettings

import android.view.View
import com.example.castapp.utils.AppLog

/**
 * 窗口操作模块
 * 负责处理投屏窗口的各种操作和功能切换
 */
class WindowOperationModule(
    private val dataModule: WindowDataModule
) {
    
    // 精准控制面板更新回调
    private var precisionControlUpdateCallback: (() -> Unit)? = null

    // 对话框刷新回调
    private var dialogRefreshCallback: (() -> Unit)? = null

    /**
     * 设置精准控制面板更新回调
     */
    fun setPrecisionControlUpdateCallback(callback: () -> Unit) {
        this.precisionControlUpdateCallback = callback
    }

    /**
     * 设置对话框刷新回调
     */
    fun setDialogRefreshCallback(callback: () -> Unit) {
        this.dialogRefreshCallback = callback
    }
    
    /**
     * 切换指定连接的裁剪模式
     */
    fun toggleCropMode(connectionId: String, enable: Boolean) {
        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    if (enable) {
                        // 开启裁剪开关：进入裁剪模式
                        transformHandler.startCropMode()
                        AppLog.d("开启裁剪模式: $connectionId")
                    } else {
                        // 🎯 修复：关闭裁剪开关应该与取消按钮功能一致，恢复到进入裁剪模式前的状态
                        transformHandler.endCropMode(isCancel = true)
                        AppLog.d("关闭裁剪开关（取消模式）: $connectionId")
                    }
                } else {
                    AppLog.w("未找到连接对应的窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("切换裁剪模式失败: $connectionId", e)
            }
        }
    }

    /**
     * 切换拖动功能
     */
    fun toggleDragMode(connectionId: String, isEnabled: Boolean) {
        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    transformHandler.setDragEnabled(isEnabled)
                    AppLog.d("【窗口管理】切换拖动功能: $connectionId, 启用: $isEnabled")
                } else {
                    AppLog.w("【窗口管理】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【窗口管理】切换拖动功能失败: $connectionId", e)
            }
        }
    }

    /**
     * 📝 切换编辑功能（仅文本窗口）
     */
    fun toggleEditMode(connectionId: String, isEnabled: Boolean) {
        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                // 检查是否为文本窗口
                if (!connectionId.startsWith("text_")) {
                    AppLog.w("【编辑功能】非文本窗口不支持编辑功能: $connectionId")
                    return@runOnUiThread
                }

                // 更新数据模块中的编辑状态
                dataModule.setEditState(connectionId, isEnabled)

                // 🎯 新增：直接控制编辑模式的进入和退出
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    val textWindowManager = transformHandler.getTextWindowManager()
                    if (textWindowManager != null) {
                        if (isEnabled) {
                            // 开启编辑开关：直接进入编辑模式
                            textWindowManager.showEditPanel()
                            AppLog.d("【编辑功能】编辑开关开启，直接进入编辑模式: $connectionId")
                        } else {
                            // 关闭编辑开关：退出编辑模式
                            textWindowManager.hideEditPanel()
                            AppLog.d("【编辑功能】编辑开关关闭，退出编辑模式: $connectionId")
                        }
                    } else {
                        AppLog.w("【编辑功能】未找到文本窗口管理器: $connectionId")
                    }
                } else {
                    AppLog.w("【编辑功能】未找到对应的TransformHandler: $connectionId")
                }

                AppLog.d("【编辑功能】切换编辑功能: $connectionId, 启用: $isEnabled")

                // 触发对话框刷新以更新UI显示
                dialogRefreshCallback?.invoke()

            } catch (e: Exception) {
                AppLog.e("【编辑功能】切换编辑功能失败: $connectionId", e)
            }
        }
    }

    /**
     * 切换缩放功能
     */
    fun toggleScaleMode(connectionId: String, isEnabled: Boolean) {
        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    transformHandler.setScaleEnabled(isEnabled)
                    AppLog.d("【窗口管理】切换缩放功能: $connectionId, 启用: $isEnabled")
                } else {
                    AppLog.w("【窗口管理】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【窗口管理】切换缩放功能失败: $connectionId", e)
            }
        }
    }

    /**
     * 切换旋转功能
     */
    fun toggleRotationMode(connectionId: String, isEnabled: Boolean) {
        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    transformHandler.setRotationEnabled(isEnabled)
                    AppLog.d("【窗口管理】切换旋转功能: $connectionId, 启用: $isEnabled")
                } else {
                    AppLog.w("【窗口管理】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【窗口管理】切换旋转功能失败: $connectionId", e)
            }
        }
    }

    /**
     * 切换窗口显示/隐藏
     */
    fun toggleWindowVisibility(connectionId: String, isVisible: Boolean) {
        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    transformHandler.visibility = if (isVisible) View.VISIBLE else View.GONE
                    AppLog.d("【窗口管理】切换窗口可见性: $connectionId, 可见: $isVisible")
                } else {
                    AppLog.w("【窗口管理】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【窗口管理】切换窗口可见性失败: $connectionId", e)
            }
        }
    }

    /**
     * 切换镜像模式
     */
    fun toggleMirrorMode(connectionId: String, isEnabled: Boolean) {
        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    transformHandler.setMirrorEnabled(isEnabled)
                    AppLog.d("【窗口管理】切换镜像模式: $connectionId, 启用: $isEnabled")
                } else {
                    AppLog.w("【窗口管理】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【窗口管理】切换镜像模式失败: $connectionId", e)
            }
        }
    }

    /**
     * 设置窗口圆角半径
     */
    fun setWindowCornerRadius(connectionId: String, cornerRadius: Float) {
        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    transformHandler.setCornerRadius(cornerRadius)
                    AppLog.d("【窗口管理】设置圆角半径: $connectionId, 半径: ${cornerRadius}dp")
                } else {
                    AppLog.w("【窗口管理】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【窗口管理】设置圆角半径失败: $connectionId", e)
            }
        }
    }

    /**
     * 设置窗口透明度
     */
    fun setWindowAlpha(connectionId: String, alpha: Float) {
        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    transformHandler.setWindowAlpha(alpha)
                    AppLog.d("【窗口管理】设置透明度: $connectionId, 透明度: ${(alpha * 100).toInt()}%")
                } else {
                    AppLog.w("【窗口管理】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【窗口管理】设置透明度失败: $connectionId", e)
            }
        }
    }

    /**
     * 切换精准调控功能
     */
    fun toggleControlMode(connectionId: String, isEnabled: Boolean) {
        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    // 保存调控状态
                    dataModule.setControlState(connectionId, isEnabled)
                    AppLog.d("【窗口管理】切换精准调控功能: $connectionId, 启用: $isEnabled")

                    // 通知MainActivity更新精准控制面板
                    precisionControlUpdateCallback?.invoke()
                } else {
                    AppLog.w("【窗口管理】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【窗口管理】切换精准调控功能失败: $connectionId", e)
            }
        }
    }



    /**
     * 切换边框显示模式
     */
    fun toggleBorderMode(connectionId: String, isEnabled: Boolean) {
        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    transformHandler.setBorderEnabled(isEnabled)
                    AppLog.d("【窗口管理】切换边框显示: $connectionId, 启用: $isEnabled")
                } else {
                    AppLog.w("【窗口管理】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【窗口管理】切换边框显示失败: $connectionId", e)
            }
        }
    }

    /**
     * 设置边框颜色
     */
    fun setBorderColor(connectionId: String, color: Int) {
        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    transformHandler.setBorderColor(color)
                    AppLog.d("【窗口管理】设置边框颜色: $connectionId, 颜色: ${String.format("#%06X", 0xFFFFFF and color)}")

                    // 触发对话框刷新回调，确保窗口信息更新
                    dialogRefreshCallback?.invoke()
                } else {
                    AppLog.w("【窗口管理】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【窗口管理】设置边框颜色失败: $connectionId", e)
            }
        }
    }

    /**
     * 设置边框宽度
     */
    fun setBorderWidth(connectionId: String, width: Float) {
        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    transformHandler.setBorderWidth(width)
                    AppLog.d("【窗口管理】设置边框宽度: $connectionId, 宽度: ${width}dp")

                    // 触发对话框刷新回调，确保窗口信息更新
                    dialogRefreshCallback?.invoke()
                } else {
                    AppLog.w("【窗口管理】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【窗口管理】设置边框宽度失败: $connectionId", e)
            }
        }
    }

}
