package com.example.castapp.manager

import android.content.Context
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.asLiveData
import com.example.castapp.database.CastAppDatabase
import com.example.castapp.database.entity.WindowLayoutEntity
import com.example.castapp.database.entity.WindowLayoutItemEntity
import com.example.castapp.model.CastWindowInfo
import com.example.castapp.ui.dialog.SaveLayoutDialog
import com.example.castapp.ui.dialog.DirectorDialog
import com.example.castapp.utils.AppLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Date

/**
 * 布局管理器
 * 负责窗口布局的保存、加载和恢复功能
 */
class LayoutManager private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: LayoutManager? = null
        
        fun getInstance(): LayoutManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: LayoutManager().also { INSTANCE = it }
            }
        }
    }
    
    // 数据库实例
    private var database: CastAppDatabase? = null

    // 对话框实例
    private var saveLayoutDialog: SaveLayoutDialog? = null
    private var directorDialog: DirectorDialog? = null

    /**
     * 初始化管理器
     */
    fun initialize(context: Context) {
        this.database = CastAppDatabase.getDatabase(context)
        AppLog.d("LayoutManager初始化完成")
    }
    
    /**
     * 保存当前窗口布局
     * @param layoutName 布局名称
     * @param windowInfoList 当前窗口信息列表
     * @param callback 保存结果回调
     */
    fun saveLayout(
        layoutName: String,
        windowInfoList: List<CastWindowInfo>,
        context: Context,
        callback: (success: Boolean, message: String) -> Unit
    ) {
        val db = database
        if (db == null) {
            callback(false, "数据库未初始化")
            return
        }
        
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val dao = db.windowLayoutDao()
                
                // 检查布局名称是否已存在
                val existingCount = dao.isLayoutNameExists(layoutName)
                if (existingCount > 0) {
                    withContext(Dispatchers.Main) {
                        callback(false, "布局名称已存在，请使用其他名称")
                    }
                    return@launch
                }
                
                // 获取下一个排序顺序值
                val maxSortOrder = dao.getMaxSortOrder() ?: 0
                val nextSortOrder = maxSortOrder + 1

                // 创建布局实体
                val currentTime = Date()
                val layoutEntity = WindowLayoutEntity(
                    layoutName = layoutName,
                    description = null,
                    createdAt = currentTime,
                    updatedAt = currentTime,
                    windowCount = windowInfoList.size,
                    sortOrder = nextSortOrder
                )
                
                // 插入布局并获取ID
                val layoutId = dao.insertLayout(layoutEntity)
                
                // 创建布局项实体列表
                // 🐾 注意：现在 zOrder 是显示层级（1=最顶层），需要转换为容器索引
                val layoutItems = windowInfoList.mapIndexed { index, windowInfo ->
                    // 🐾 转换显示层级为容器索引：显示层级1 -> orderIndex (totalWindows-1)
                    val containerIndex = windowInfoList.size - windowInfo.zOrder
                    WindowLayoutItemEntity.fromCastWindowInfo(
                        layoutId = layoutId,
                        orderIndex = containerIndex, // 🐾 使用转换后的容器索引
                        windowInfo = windowInfo,
                        context = context // 📝 传递Context用于获取文本格式信息
                    )
                }
                
                // 插入布局项
                dao.insertLayoutItems(layoutItems)

                // 🐾 调试日志：显示保存的层级信息
                AppLog.d("【布局保存】布局保存成功: $layoutName, 包含${windowInfoList.size}个窗口")
                windowInfoList.forEachIndexed { index, windowInfo ->
                    val containerIndex = windowInfoList.size - windowInfo.zOrder
                    AppLog.d("【布局保存】  列表位置${index} -> 显示层级${windowInfo.zOrder} -> 保存为orderIndex${containerIndex}: ${windowInfo.getDisplayTextWithDevice()}")
                }
                
                withContext(Dispatchers.Main) {
                    callback(true, "布局保存成功")
                }
                
            } catch (e: Exception) {
                AppLog.e("保存布局失败: $layoutName", e)
                withContext(Dispatchers.Main) {
                    callback(false, "保存失败: ${e.message}")
                }
            }
        }
    }
    
    /**
     * 获取所有保存的布局列表
     */
    fun getAllLayouts(): LiveData<List<WindowLayoutEntity>>? {
        return database?.windowLayoutDao()?.getAllLayouts()?.asLiveData()
    }
    
    /**
     * 获取指定布局的详细信息
     * @param layoutId 布局ID
     * @param callback 获取结果回调
     */
    fun getLayoutDetails(
        layoutId: Long,
        callback: (success: Boolean, items: List<WindowLayoutItemEntity>?, message: String) -> Unit
    ) {
        val db = database
        if (db == null) {
            callback(false, null, "数据库未初始化")
            return
        }
        
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val dao = db.windowLayoutDao()
                val items = dao.getLayoutItemsByLayoutId(layoutId)

                // 🐾 按照层级顺序排序，最上层（orderIndex最大）在前
                val sortedItems = items.sortedByDescending { it.orderIndex }

                AppLog.d("【导播台】获取布局详情: layoutId=$layoutId, 共${sortedItems.size}个窗口")
                sortedItems.forEachIndexed { index, item ->
                    AppLog.d("【导播台】  显示位置${index} -> 层级${item.orderIndex}: ${item.getDisplayDeviceInfo()}")
                }

                withContext(Dispatchers.Main) {
                    callback(true, sortedItems, "获取成功")
                }
                
            } catch (e: Exception) {
                AppLog.e("获取布局详情失败: layoutId=$layoutId", e)
                withContext(Dispatchers.Main) {
                    callback(false, null, "获取失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 更新布局名称
     * @param layoutId 布局ID
     * @param newLayoutName 新的布局名称
     * @param callback 更新结果回调
     */
    fun updateLayoutName(
        layoutId: Long,
        newLayoutName: String,
        callback: (success: Boolean, message: String) -> Unit
    ) {
        val db = database
        if (db == null) {
            callback(false, "数据库未初始化")
            return
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val dao = db.windowLayoutDao()

                // 检查新名称是否已存在（排除当前布局）
                val existingLayout = dao.getLayoutByName(newLayoutName)
                if (existingLayout != null && existingLayout.id != layoutId) {
                    withContext(Dispatchers.Main) {
                        callback(false, "布局名称已存在，请使用其他名称")
                    }
                    return@launch
                }

                // 获取当前布局
                val currentLayout = dao.getLayoutById(layoutId)
                if (currentLayout == null) {
                    withContext(Dispatchers.Main) {
                        callback(false, "布局不存在")
                    }
                    return@launch
                }

                // 更新布局名称和修改时间
                val updatedLayout = currentLayout.copy(
                    layoutName = newLayoutName,
                    updatedAt = Date()
                )

                dao.updateLayout(updatedLayout)

                AppLog.d("布局名称更新成功: ${currentLayout.layoutName} -> $newLayoutName")

                withContext(Dispatchers.Main) {
                    callback(true, "布局名称更新成功")
                }

            } catch (e: Exception) {
                AppLog.e("更新布局名称失败: layoutId=$layoutId, newName=$newLayoutName", e)
                withContext(Dispatchers.Main) {
                    callback(false, "更新失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 批量更新布局排序顺序
     * @param layouts 按新顺序排列的布局列表
     * @param callback 更新结果回调
     */
    fun updateLayoutsOrder(
        layouts: List<WindowLayoutEntity>,
        callback: (success: Boolean, message: String) -> Unit
    ) {
        val db = database
        if (db == null) {
            callback(false, "数据库未初始化")
            return
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val dao = db.windowLayoutDao()

                // 批量更新排序顺序
                layouts.forEachIndexed { index, layout ->
                    dao.updateLayoutSortOrder(layout.id, index)
                    AppLog.d("更新布局排序: ${layout.layoutName} -> sortOrder=$index")
                }

                AppLog.d("批量更新布局排序完成，共更新${layouts.size}个布局")

                withContext(Dispatchers.Main) {
                    callback(true, "排序更新成功")
                }

            } catch (e: Exception) {
                AppLog.e("批量更新布局排序失败", e)
                withContext(Dispatchers.Main) {
                    callback(false, "排序更新失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 删除指定布局
     * @param layoutId 布局ID
     * @param callback 删除结果回调
     */
    fun deleteLayout(
        layoutId: Long,
        callback: (success: Boolean, message: String) -> Unit
    ) {
        val db = database
        if (db == null) {
            callback(false, "数据库未初始化")
            return
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val dao = db.windowLayoutDao()

                // 获取布局信息用于日志
                val layout = dao.getLayoutById(layoutId)
                val layoutName = layout?.layoutName ?: "未知布局"

                // 删除布局（会级联删除相关的布局项）
                dao.deleteLayoutById(layoutId)

                AppLog.d("布局删除成功: $layoutName")

                withContext(Dispatchers.Main) {
                    callback(true, "布局删除成功")
                }

            } catch (e: Exception) {
                AppLog.e("删除布局失败: layoutId=$layoutId", e)
                withContext(Dispatchers.Main) {
                    callback(false, "删除失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 🐾 批量删除布局
     * @param layoutIds 布局ID列表
     * @param callback 删除结果回调
     */
    fun deleteLayouts(
        layoutIds: List<Long>,
        callback: (success: Boolean, message: String) -> Unit
    ) {
        val db = database
        if (db == null) {
            callback(false, "数据库未初始化")
            return
        }

        if (layoutIds.isEmpty()) {
            callback(false, "没有要删除的布局")
            return
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val dao = db.windowLayoutDao()

                // 🐾 先获取所有布局信息用于日志
                val layoutInfos = mutableListOf<Pair<Long, String>>()
                layoutIds.forEach { layoutId ->
                    val layout = dao.getLayoutById(layoutId)
                    val layoutName = layout?.layoutName ?: "未知布局"
                    layoutInfos.add(layoutId to layoutName)
                }

                // 🐾 批量删除布局（Room会自动处理事务）
                layoutIds.forEach { layoutId ->
                    // 删除布局（会级联删除相关的布局项）
                    dao.deleteLayoutById(layoutId)
                }

                // 🐾 输出删除日志
                layoutInfos.forEach { (layoutId, layoutName) ->
                    AppLog.d("批量删除布局: $layoutName (id=$layoutId)")
                }

                AppLog.d("批量删除成功: 共删除${layoutIds.size}个布局")

                withContext(Dispatchers.Main) {
                    callback(true, "批量删除成功")
                }

            } catch (e: Exception) {
                AppLog.e("批量删除布局失败: layoutIds=$layoutIds", e)
                withContext(Dispatchers.Main) {
                    callback(false, "批量删除失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 🐾 设置布局应用状态（新增）
     */
    fun setLayoutAppliedStatus(
        layoutId: Long,
        isApplied: Boolean,
        callback: (success: Boolean, message: String) -> Unit
    ) {
        val db = database
        if (db == null) {
            callback(false, "数据库未初始化")
            return
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val dao = db.windowLayoutDao()

                if (isApplied) {
                    // 如果要设置为应用状态，先清除其他布局的应用状态
                    dao.clearAllAppliedStatus()
                }

                // 设置当前布局的应用状态
                dao.updateLayoutAppliedStatus(layoutId, isApplied)

                AppLog.d("布局应用状态更新: layoutId=$layoutId, isApplied=$isApplied")

                withContext(Dispatchers.Main) {
                    callback(true, "应用状态更新成功")
                }

            } catch (e: Exception) {
                AppLog.e("更新布局应用状态失败: layoutId=$layoutId", e)
                withContext(Dispatchers.Main) {
                    callback(false, "更新失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 🐾 获取当前应用的布局（新增）
     */
    fun getCurrentAppliedLayout(callback: (layout: WindowLayoutEntity?) -> Unit) {
        val db = database
        if (db == null) {
            callback(null)
            return
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val dao = db.windowLayoutDao()
                val appliedLayout = dao.getCurrentAppliedLayout()

                withContext(Dispatchers.Main) {
                    callback(appliedLayout)
                }

            } catch (e: Exception) {
                AppLog.e("获取当前应用布局失败", e)
                withContext(Dispatchers.Main) {
                    callback(null)
                }
            }
        }
    }

    /**
     * 更新布局参数（更新已有参数模式）
     * @param layoutId 布局ID
     * @param currentWindowInfoList 当前投屏窗口信息列表
     * @param context 上下文，用于获取媒体文件信息
     * @param callback 更新结果回调
     */
    fun updateLayoutParameters(
        layoutId: Long,
        currentWindowInfoList: List<CastWindowInfo>,
        context: Context,
        callback: (success: Boolean, message: String) -> Unit
    ) {
        val db = database
        if (db == null) {
            callback(false, "数据库未初始化")
            return
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val dao = db.windowLayoutDao()

                // 获取现有布局项
                val existingItems = dao.getLayoutItemsByLayoutId(layoutId)
                AppLog.d("【更新布局参数】现有布局项数量: ${existingItems.size}")

                // 使用新的更新策略：保留所有原有设备，更新当前投屏设备，插入新设备
                updateLayoutWithMergedDevices(dao, layoutId, currentWindowInfoList, existingItems, context)

                // 更新布局的修改时间和窗口数量
                val layout = dao.getLayoutById(layoutId)
                if (layout != null) {
                    val updatedLayout = layout.copy(
                        updatedAt = Date(),
                        windowCount = dao.getLayoutItemCount(layoutId)
                    )
                    dao.updateLayout(updatedLayout)
                }

                AppLog.d("【更新布局参数】更新完成: 处理${currentWindowInfoList.size}个窗口")

                withContext(Dispatchers.Main) {
                    callback(true, "布局参数更新成功")
                }

            } catch (e: Exception) {
                AppLog.e("更新布局参数失败: layoutId=$layoutId", e)
                withContext(Dispatchers.Main) {
                    callback(false, "更新失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 更新布局参数的核心方法：保留所有原有设备，更新当前投屏设备，插入新设备
     */
    private suspend fun updateLayoutWithMergedDevices(
        dao: com.example.castapp.database.dao.WindowLayoutDao,
        layoutId: Long,
        currentWindowInfoList: List<CastWindowInfo>,
        existingItems: List<WindowLayoutItemEntity>,
        context: Context
    ) {
        // 创建设备ID到现有布局项的映射
        val existingItemsMap = existingItems.associateBy { it.deviceId }

        // 分离当前投屏设备和新设备
        val newDevices = currentWindowInfoList.filter { !existingItemsMap.containsKey(it.connectionId) }

        AppLog.d("【更新布局参数】原有设备: ${existingItems.size}个, 当前投屏: ${currentWindowInfoList.size}个, 新设备: ${newDevices.size}个")

        // 检查现有设备的层级是否发生变化
        val layerChanged = checkIfLayerOrderChanged(currentWindowInfoList, existingItemsMap)

        if (newDevices.isEmpty() && !layerChanged) {
            // 没有新设备且层级未变化，只更新现有设备参数，保持原有层级
            currentWindowInfoList.forEach { windowInfo ->
                val existingItem = existingItemsMap[windowInfo.connectionId]!!
                val updatedItem = WindowLayoutItemEntity.fromCastWindowInfo(
                    layoutId = layoutId,
                    orderIndex = existingItem.orderIndex, // 保持原有层级
                    windowInfo = windowInfo,
                    context = context // 📁 传递Context用于获取媒体文件信息
                ).copy(id = existingItem.id)

                dao.updateLayoutItem(updatedItem)
                AppLog.d("【更新布局参数】更新现有设备: ${windowInfo.connectionId}, 保持层级: ${existingItem.orderIndex}")
            }
        } else {
            // 有新设备或层级发生变化，需要重新分配所有设备的层级
            AppLog.d("【更新布局参数】检测到层级变化或新设备，重新分配层级")
            reassignAllDeviceLayers(dao, layoutId, currentWindowInfoList, existingItems, existingItemsMap, context)
        }
    }

    /**
     * 检查现有设备的层级顺序是否发生变化
     */
    private fun checkIfLayerOrderChanged(
        currentWindowInfoList: List<CastWindowInfo>,
        existingItemsMap: Map<String, WindowLayoutItemEntity>
    ): Boolean {
        // 获取当前投屏设备按层级排序的顺序
        val currentOrder = currentWindowInfoList
            .sortedBy { currentWindowInfoList.size - it.zOrder } // 转换为orderIndex排序
            .map { it.connectionId }

        // 获取原有设备按层级排序的顺序
        val originalOrder = currentWindowInfoList
            .mapNotNull { windowInfo ->
                existingItemsMap[windowInfo.connectionId]?.let { existingItem ->
                    windowInfo.connectionId to existingItem.orderIndex
                }
            }
            .sortedBy { it.second }
            .map { it.first }

        val changed = currentOrder != originalOrder

        AppLog.d("【层级检查】当前投屏顺序: ${currentOrder.joinToString(" -> ")}")
        AppLog.d("【层级检查】原有保存顺序: ${originalOrder.joinToString(" -> ")}")
        AppLog.d("【层级检查】层级是否变化: $changed")

        return changed
    }

    /**
     * 重新分配所有设备层级：保留原有设备，按当前投屏层级插入新设备
     * 示例：已保存布局：A(层级1)、B(层级2)、C(层级3)
     *      当前投屏：B(层级1)、D(层级2)、C(层级3)
     *      保存后布局：A(层级1)、B(层级2)、D(层级3)、C(层级4)
     */
    private suspend fun reassignAllDeviceLayers(
        dao: com.example.castapp.database.dao.WindowLayoutDao,
        layoutId: Long,
        currentWindowInfoList: List<CastWindowInfo>,
        existingItems: List<WindowLayoutItemEntity>,
        existingItemsMap: Map<String, WindowLayoutItemEntity>,
        context: Context
    ) {
        // 创建最终的设备列表，按新的层级顺序排列
        val finalDeviceList = mutableListOf<String>()

        // 按当前投屏的层级顺序处理（从底层到顶层）
        val sortedCurrentWindows = currentWindowInfoList.sortedBy {
            currentWindowInfoList.size - it.zOrder // 转换为orderIndex排序
        }

        // 遍历当前投屏设备，严格按当前投屏的层级顺序插入
        sortedCurrentWindows.forEach { windowInfo ->
            val deviceId = windowInfo.connectionId

            // 直接按当前投屏的层级顺序添加设备
            finalDeviceList.add(deviceId)
            AppLog.d("【层级重排】按当前投屏层级插入设备: $deviceId, 当前层级: ${windowInfo.zOrder}")
        }

        // 添加剩余的原有设备（当前未投屏的设备）
        val remainingDevices = existingItems.filter { item ->
            !currentWindowInfoList.any { it.connectionId == item.deviceId }
        }.sortedBy { it.orderIndex }

        remainingDevices.forEach { remainingDevice ->
            finalDeviceList.add(remainingDevice.deviceId)
            AppLog.d("【层级重排】插入未投屏的原有设备: ${remainingDevice.deviceId}, 原层级: ${remainingDevice.orderIndex}")
        }

        // 应用新的层级分配
        finalDeviceList.forEachIndexed { newOrderIndex, deviceId ->
            val existingItem = existingItemsMap[deviceId]
            val currentWindowInfo = currentWindowInfoList.find { it.connectionId == deviceId }

            if (existingItem != null) {
                if (currentWindowInfo != null) {
                    // 更新现有设备（当前投屏）
                    val updatedItem = WindowLayoutItemEntity.fromCastWindowInfo(
                        layoutId = layoutId,
                        orderIndex = newOrderIndex,
                        windowInfo = currentWindowInfo,
                        context = context // 📁 传递Context用于获取媒体文件信息
                    ).copy(id = existingItem.id)

                    dao.updateLayoutItem(updatedItem)
                    AppLog.d("【层级重排】更新现有设备: $deviceId, 原层级: ${existingItem.orderIndex} -> 新层级: $newOrderIndex")
                } else {
                    // 保留原有设备（当前未投屏），只更新层级
                    val updatedItem = existingItem.copy(orderIndex = newOrderIndex)
                    dao.updateLayoutItem(updatedItem)
                    AppLog.d("【层级重排】保留原有设备: $deviceId, 原层级: ${existingItem.orderIndex} -> 新层级: $newOrderIndex")
                }
            } else if (currentWindowInfo != null) {
                // 插入新设备
                val newItem = WindowLayoutItemEntity.fromCastWindowInfo(
                    layoutId = layoutId,
                    orderIndex = newOrderIndex,
                    windowInfo = currentWindowInfo,
                    context = context // 📁 传递Context用于获取媒体文件信息
                )

                dao.insertLayoutItem(newItem)
                AppLog.d("【层级重排】新增设备: $deviceId, 分配层级: $newOrderIndex")
            }
        }

        AppLog.d("【层级重排】完成，最终设备顺序: ${finalDeviceList.joinToString(" -> ")}")
    }

    /**
     * 替换布局参数（清空重新保存模式）
     * @param layoutId 布局ID
     * @param currentWindowInfoList 当前投屏窗口信息列表
     * @param context 上下文，用于获取媒体文件信息
     * @param callback 替换结果回调
     */
    fun replaceLayoutParameters(
        layoutId: Long,
        currentWindowInfoList: List<CastWindowInfo>,
        context: Context,
        callback: (success: Boolean, message: String) -> Unit
    ) {
        val db = database
        if (db == null) {
            callback(false, "数据库未初始化")
            return
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val dao = db.windowLayoutDao()

                // 删除现有的所有布局项
                dao.deleteLayoutItemsByLayoutId(layoutId)
                AppLog.d("【替换布局参数】已清空现有布局项")

                // 创建新的布局项列表
                val newLayoutItems = currentWindowInfoList.mapIndexed { index, windowInfo ->
                    // 转换显示层级为容器索引：显示层级1 -> orderIndex (totalWindows-1)
                    val containerIndex = currentWindowInfoList.size - windowInfo.zOrder
                    WindowLayoutItemEntity.fromCastWindowInfo(
                        layoutId = layoutId,
                        orderIndex = containerIndex,
                        windowInfo = windowInfo,
                        context = context // 📁 传递Context用于获取媒体文件信息
                    )
                }

                // 插入新的布局项
                dao.insertLayoutItems(newLayoutItems)

                // 更新布局的修改时间和窗口数量
                val layout = dao.getLayoutById(layoutId)
                if (layout != null) {
                    val updatedLayout = layout.copy(
                        updatedAt = Date(),
                        windowCount = currentWindowInfoList.size
                    )
                    dao.updateLayout(updatedLayout)
                }

                AppLog.d("【替换布局参数】替换完成: 新增${currentWindowInfoList.size}个窗口")
                currentWindowInfoList.forEachIndexed { index, windowInfo ->
                    val containerIndex = currentWindowInfoList.size - windowInfo.zOrder
                    AppLog.d("【替换布局参数】  列表位置${index} -> 显示层级${windowInfo.zOrder} -> 保存为orderIndex${containerIndex}: ${windowInfo.getDisplayTextWithDevice()}")
                }

                withContext(Dispatchers.Main) {
                    callback(true, "布局参数替换成功")
                }

            } catch (e: Exception) {
                AppLog.e("替换布局参数失败: layoutId=$layoutId", e)
                withContext(Dispatchers.Main) {
                    callback(false, "替换失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 显示保存布局对话框
     */
    fun showSaveLayoutDialog(activity: FragmentActivity, windowInfoList: List<CastWindowInfo>) {
        try {
            // 关闭之前的对话框
            saveLayoutDialog?.dismiss()

            saveLayoutDialog = SaveLayoutDialog(
                context = activity,
                windowInfoList = windowInfoList,
                onSaveSuccess = {
                    AppLog.d("布局保存成功回调")
                }
            )

            saveLayoutDialog?.show()
            AppLog.d("显示保存布局对话框")
        } catch (e: Exception) {
            AppLog.e("显示保存布局对话框失败", e)
        }
    }

    /**
     * 显示导播台BottomSheet对话框
     */
    fun showDirectorDialog(activity: FragmentActivity, windowSettingsManager: WindowSettingsManager, onRestoreLayout: (List<WindowLayoutItemEntity>) -> Unit) {
        try {
            // 关闭之前的对话框
            directorDialog?.dismiss()

            val dialog = DirectorDialog(
                windowSettingsManager = windowSettingsManager,
                onRestoreLayout = onRestoreLayout
            )

            // 设置关闭回调
            dialog.onDialogDismissed = {
                directorDialog = null // 🔄 清理引用
                AppLog.d("【导播台】对话框已关闭")
            }

            // 🔄 保存对话框引用
            directorDialog = dialog

            dialog.show(activity.supportFragmentManager, "DirectorBottomSheet")
            AppLog.d("显示导播台BottomSheet对话框")
        } catch (e: Exception) {
            AppLog.e("显示导播台BottomSheet对话框失败", e)
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        saveLayoutDialog?.dismiss()
        directorDialog?.dismiss()
        saveLayoutDialog = null
        directorDialog = null
        database = null
        AppLog.d("LayoutManager清理完成")
    }
}
