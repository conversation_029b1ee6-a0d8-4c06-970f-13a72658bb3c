package com.example.castapp.manager

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.provider.Settings
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import com.example.castapp.utils.AppLog

/**
 * 统一的权限管理器 - 简化版
 * 以MediaProjectionManager为核心的权限管理
 */
class PermissionManager private constructor() {
    companion object {
        // 应用所需的基础权限
        private val BASIC_PERMISSIONS: Array<String>
            get() {
                val permissions = mutableListOf(
                    Manifest.permission.INTERNET,
                    Manifest.permission.ACCESS_NETWORK_STATE,
                    Manifest.permission.ACCESS_WIFI_STATE,
                    Manifest.permission.RECORD_AUDIO,
                    Manifest.permission.CAMERA
                )

                // 🔥 新增：添加存储权限到基础权限中，应用启动时就请求
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    // Android 13+ 使用细分权限
                    permissions.add(Manifest.permission.READ_MEDIA_VIDEO)
                    permissions.add(Manifest.permission.READ_MEDIA_IMAGES)
                } else {
                    // Android 12及以下使用READ_EXTERNAL_STORAGE
                    permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
                }

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    permissions.add(Manifest.permission.FOREGROUND_SERVICE)
                }

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    permissions.add(Manifest.permission.POST_NOTIFICATIONS)
                }

                return permissions.toTypedArray()
            }

        @Volatile
        private var INSTANCE: PermissionManager? = null

        fun getInstance(): PermissionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: PermissionManager().also { INSTANCE = it }
            }
        }
    }

    /**
     * 权限结果回调接口
     */
    interface PermissionCallback {
        fun onPermissionGranted()
        fun onPermissionDenied(deniedPermissions: List<String>)
    }

    /**
     * 统一MediaProjection权限回调接口
     * 唯一的MediaProjection权限管理接口
     */
    interface UnifiedMediaProjectionCallback {
        fun onMediaProjectionGranted(resultCode: Int, data: Intent)
        fun onMediaProjectionDenied()
        fun onPermissionAlreadyGranted(resultCode: Int, data: Intent)
    }

    /**
     * 悬浮窗权限回调接口
     */
    interface OverlayPermissionCallback {
        fun onOverlayPermissionGranted()
        fun onOverlayPermissionDenied()
    }

    /**
     * Fragment权限管理器辅助类
     * 简化版：只保留统一MediaProjection权限管理
     */
    class PermissionHelper(
        private val fragment: Fragment
    ) {
        private var unifiedMediaProjectionCallback: UnifiedMediaProjectionCallback? = null

        // 统一MediaProjection权限请求启动器（唯一的权限管理入口）
        private val unifiedMediaProjectionLauncher = fragment.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            // 异步处理权限结果，避免阻塞主线程
            fragment.lifecycleScope.launch(Dispatchers.IO) {
                val granted = result.resultCode == Activity.RESULT_OK && result.data != null

                AppLog.permission("统一MediaProjection权限结果: granted=$granted")

                // 切换回主线程执行回调
                withContext(Dispatchers.Main) {
                    if (granted) {
                        unifiedMediaProjectionCallback?.onMediaProjectionGranted(result.resultCode, result.data!!)
                    } else {
                        unifiedMediaProjectionCallback?.onMediaProjectionDenied()
                    }

                    unifiedMediaProjectionCallback = null
                }
            }
        }
    }

    /**
     * Activity版本的权限管理器辅助类
     */
    class ActivityPermissionHelper(
        private val activity: Activity
    ) {
        private var permissionCallback: PermissionCallback? = null
        private var overlayPermissionCallback: OverlayPermissionCallback? = null
        private var unifiedMediaProjectionCallback: UnifiedMediaProjectionCallback? = null

        // 基础权限请求启动器 - 需要在Activity中注册
        private lateinit var permissionLauncher: ActivityResultLauncher<Array<String>>

        // 悬浮窗权限请求启动器
        private lateinit var overlayPermissionLauncher: ActivityResultLauncher<Intent>

        // 🔥 关键修复：添加MediaProjection权限请求启动器
        private lateinit var unifiedMediaProjectionLauncher: ActivityResultLauncher<Intent>

        fun initialize() {
            // 注意：这个方法需要在Activity的onCreate中调用
            if (activity is androidx.activity.ComponentActivity) {
                permissionLauncher = activity.registerForActivityResult(
                    ActivityResultContracts.RequestMultiplePermissions()
                ) { permissions ->
                    // 异步处理权限结果，避免阻塞主线程
                    Thread {
                        try {
                            val deniedPermissions = permissions.filter { !it.value }.keys.toList()

                            // 切换回主线程执行回调
                            activity.runOnUiThread {
                                if (deniedPermissions.isEmpty()) {
                                    AppLog.permission("所有基础权限已授予")
                                    permissionCallback?.onPermissionGranted()
                                } else {
                                    AppLog.w("以下权限被拒绝: ${deniedPermissions.joinToString()}")
                                    permissionCallback?.onPermissionDenied(deniedPermissions)
                                }

                                permissionCallback = null
                            }
                        } catch (e: Exception) {
                            AppLog.e("处理权限结果失败", e)
                            activity.runOnUiThread {
                                permissionCallback?.onPermissionDenied(permissions.keys.toList())
                                permissionCallback = null
                            }
                        }
                    }.start()
                }

                // 注册悬浮窗权限启动器
                overlayPermissionLauncher = activity.registerForActivityResult(
                    ActivityResultContracts.StartActivityForResult()
                ) { result ->
                    // 异步检查悬浮窗权限，避免阻塞主线程
                    Thread {
                        try {
                            val hasPermission = Settings.canDrawOverlays(activity)

                            // 切换回主线程执行回调
                            activity.runOnUiThread {
                                if (hasPermission) {
                                    AppLog.permission("悬浮窗权限已授予")
                                    overlayPermissionCallback?.onOverlayPermissionGranted()
                                } else {
                                    AppLog.w("悬浮窗权限被拒绝")
                                    overlayPermissionCallback?.onOverlayPermissionDenied()
                                }
                                overlayPermissionCallback = null
                            }
                        } catch (e: Exception) {
                            AppLog.e("检查悬浮窗权限失败", e)
                            activity.runOnUiThread {
                                overlayPermissionCallback?.onOverlayPermissionDenied()
                                overlayPermissionCallback = null
                            }
                        }
                    }.start()
                }

                // 🔥 关键修复：注册MediaProjection权限启动器
                unifiedMediaProjectionLauncher = activity.registerForActivityResult(
                    ActivityResultContracts.StartActivityForResult()
                ) { result ->
                    // 异步处理权限结果，避免阻塞主线程
                    Thread {
                        try {
                            val granted = result.resultCode == Activity.RESULT_OK && result.data != null

                            AppLog.permission("【ActivityPermissionHelper】统一MediaProjection权限结果: granted=$granted")

                            // 切换回主线程执行回调
                            activity.runOnUiThread {
                                if (granted) {
                                    unifiedMediaProjectionCallback?.onMediaProjectionGranted(result.resultCode, result.data!!)
                                } else {
                                    unifiedMediaProjectionCallback?.onMediaProjectionDenied()
                                }
                                unifiedMediaProjectionCallback = null
                            }
                        } catch (e: Exception) {
                            AppLog.e("【ActivityPermissionHelper】处理MediaProjection权限结果失败", e)
                            activity.runOnUiThread {
                                unifiedMediaProjectionCallback?.onMediaProjectionDenied()
                                unifiedMediaProjectionCallback = null
                            }
                        }
                    }.start()
                }
            }
        }

        /**
         * 请求基础权限
         */
        fun requestBasicPermissions(callback: PermissionCallback? = null) {
            val permissionManager = getInstance()
            val missingPermissions = permissionManager.getMissingBasicPermissions(activity)

            if (missingPermissions.isEmpty()) {
                AppLog.permission("所有基础权限已授予")
                callback?.onPermissionGranted()
                return
            }

            AppLog.permission("请求基础权限: ${missingPermissions.joinToString()}")

            permissionCallback = callback
            if (::permissionLauncher.isInitialized) {
                permissionLauncher.launch(missingPermissions.toTypedArray())
            } else {
                AppLog.e("权限启动器未初始化，请在Activity的onCreate中调用initialize()")
                callback?.onPermissionDenied(missingPermissions)
            }
        }

        /**
         * 请求悬浮窗权限
         */
        fun requestOverlayPermission(callback: OverlayPermissionCallback) {
            if (Settings.canDrawOverlays(activity)) {
                AppLog.permission("悬浮窗权限已授予")
                callback.onOverlayPermissionGranted()
                return
            }

            AppLog.permission("请求悬浮窗权限")
            overlayPermissionCallback = callback

            if (::overlayPermissionLauncher.isInitialized) {
                val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                    data = "package:${activity.packageName}".toUri()
                }
                overlayPermissionLauncher.launch(intent)
            } else {
                AppLog.e("悬浮窗权限启动器未初始化，请在Activity的onCreate中调用initialize()")
                callback.onOverlayPermissionDenied()
            }
        }

        /**
         * 🔥 关键修复：请求MediaProjection权限（Activity版本）
         * 智能权限复用：检查是否已有可用权限，如果有则直接返回，否则申请新权限
         */
        fun requestMediaProjection(callback: UnifiedMediaProjectionCallback) {
            val mediaProjectionManager = com.example.castapp.manager.MediaProjectionManager.getInstance(activity)

            // 检查是否已有可用权限
            if (mediaProjectionManager.hasValidPermission()) {
                AppLog.permission("【ActivityPermissionHelper】MediaProjection权限已存在，直接使用")
                // 获取缓存的权限数据
                val cachedData = mediaProjectionManager.getCachedPermissionData()
                if (cachedData != null) {
                    callback.onPermissionAlreadyGranted(cachedData.first, cachedData.second)
                } else {
                    // 理论上不应该到这里，但为了安全起见
                    AppLog.w("【ActivityPermissionHelper】权限可用但缓存数据为空，重新申请权限")
                    requestNewMediaProjectionPermission(callback)
                }
                return
            }

            // 没有可用权限，申请新权限
            requestNewMediaProjectionPermission(callback)
        }

        /**
         * 申请新的MediaProjection权限
         */
        private fun requestNewMediaProjectionPermission(callback: UnifiedMediaProjectionCallback) {
            val permissionManager = getInstance()
            val intent = permissionManager.createMediaProjectionIntent(activity)
            if (intent != null) {
                AppLog.permission("【ActivityPermissionHelper】请求统一MediaProjection权限")
                unifiedMediaProjectionCallback = callback
                if (::unifiedMediaProjectionLauncher.isInitialized) {
                    unifiedMediaProjectionLauncher.launch(intent)
                } else {
                    AppLog.e("【ActivityPermissionHelper】MediaProjection权限启动器未初始化，请在Activity的onCreate中调用initialize()")
                    callback.onMediaProjectionDenied()
                }
            } else {
                AppLog.e("【ActivityPermissionHelper】无法创建MediaProjection Intent")
                callback.onMediaProjectionDenied()
            }
        }
    }

    /**
     * 获取未授予的基础权限
     */
    fun getMissingBasicPermissions(context: Context): List<String> {
        return BASIC_PERMISSIONS.filter { permission ->
            ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 创建MediaProjection权限请求Intent
     */
    fun createMediaProjectionIntent(context: Context): Intent? {
        val mediaProjectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as? MediaProjectionManager
        return mediaProjectionManager?.createScreenCaptureIntent()
    }
}
