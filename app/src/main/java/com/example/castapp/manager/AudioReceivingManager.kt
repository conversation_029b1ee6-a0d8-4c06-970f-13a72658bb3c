package com.example.castapp.manager

import android.content.Context
import com.example.castapp.audio.AudioRtpReceiver
import com.example.castapp.audio.AudioDecoder
import com.example.castapp.audio.AudioPlayer
import com.example.castapp.audio.AudioSyncManager
import com.example.castapp.network.DataView
import com.example.castapp.utils.AppLog
import java.util.concurrent.ConcurrentHashMap

/**
 * 音频接收管理器
 * 负责管理音频接收、解码和播放的所有逻辑
 */
class AudioReceivingManager(
    private val context: Context,
    private val stateManager: StateManager,
    private val callback: AudioReceivingCallback
) {
    companion object {
        // 音频配置常量
        private const val AUDIO_SAMPLE_RATE = 48000
        private const val MEDIA_AUDIO_CHANNELS = 2 // 立体声
        private const val MIC_AUDIO_CHANNELS = 1 // 单声道
        private const val MEDIA_AUDIO_PORT_OFFSET = 2
        private const val MIC_AUDIO_PORT_OFFSET = 3
        // 🚀 CPU优化：增加日志间隔，减少服务统计日志频率
        private const val LOG_INTERVAL_MS = 120000L // 120秒
    }

    // 音频接收器
    private var mediaAudioReceiver: AudioRtpReceiver? = null
    private var micAudioReceiver: AudioRtpReceiver? = null

    // 音频同步管理器
    private val audioSyncManager = AudioSyncManager()

    // 为每个发送端创建独立的音频播放器
    private val mediaAudioDecoders = ConcurrentHashMap<String, AudioDecoder>()
    private val micAudioDecoders = ConcurrentHashMap<String, AudioDecoder>()
    private val mediaAudioPlayers = ConcurrentHashMap<String, AudioPlayer>()
    private val micAudioPlayers = ConcurrentHashMap<String, AudioPlayer>()

    // 音频播放状态管理，按connectionId管理
    private val mediaAudioPlayingStates = ConcurrentHashMap<String, Boolean>()
    private val micAudioPlayingStates = ConcurrentHashMap<String, Boolean>()

    // 音频输出模式：true=扬声器，false=听筒
    @Volatile
    private var isSpeakerMode: Boolean = true

    // 接收端音量（0-100）
    @Volatile
    private var receiverVolume: Int = 80

    // 音频处理统计信息
    private var lastMediaAudioLogTime = 0L
    private var lastMicAudioLogTime = 0L
    private var mediaAudioPacketsProcessed = 0
    private var micAudioPacketsProcessed = 0
    private var mediaAudioBytesProcessed = 0L
    private var micAudioBytesProcessed = 0L

    // 配置数据缓存，用于SSRC映射建立前的配置数据
    private val pendingMediaAudioConfigs = ConcurrentHashMap<Long, ByteArray>()
    private val pendingMicAudioConfigs = ConcurrentHashMap<Long, ByteArray>()

    /**
     * 音频接收管理器回调接口
     */
    interface AudioReceivingCallback {
        fun onAudioPlayerCreated(connectionId: String, audioType: String)
        fun onAudioPlayerStopped(connectionId: String, audioType: String)
        fun onAudioError(connectionId: String, audioType: String, error: String)
        fun onAudioStatistics(audioType: String, packetsProcessed: Int, bytesProcessed: Long, activeConnections: Int)
    }

    /**
     * 启动音频接收器
     */
    fun startAudioReceivers(basePort: Int): Boolean {
        return try {
            mediaAudioReceiver = AudioRtpReceiver(
                basePort + MEDIA_AUDIO_PORT_OFFSET,
                ::handleMediaAudioData
            ).takeIf { it.start() }

            micAudioReceiver = AudioRtpReceiver(
                basePort + MIC_AUDIO_PORT_OFFSET,
                ::handleMicAudioData
            ).takeIf { it.start() }

            val success = mediaAudioReceiver != null && micAudioReceiver != null
            if (success) {
                AppLog.service("音频接收器启动成功: 媒体音频端口${basePort + MEDIA_AUDIO_PORT_OFFSET}, 麦克风音频端口${basePort + MIC_AUDIO_PORT_OFFSET}")
            } else {
                AppLog.e("音频接收器启动失败")
                stopAudioReceivers()
            }
            success
        } catch (e: Exception) {
            AppLog.e("启动音频接收器失败", e)
            callback.onAudioError("", "音频接收器", "启动失败: ${e.message}")
            false
        }
    }

    /**
     * 停止音频接收器
     */
    fun stopAudioReceivers() {
        try {
            mediaAudioReceiver?.stop()
            mediaAudioReceiver = null

            micAudioReceiver?.stop()
            micAudioReceiver = null

            // 停止所有音频播放器和解码器
            cleanupAllAudioComponents()

            AppLog.service("音频接收器已停止")

        } catch (e: Exception) {
            AppLog.e("停止音频接收器失败", e)
            callback.onAudioError("", "音频接收器", "停止失败: ${e.message}")
        }
    }

    /**
     * 设置音频输出模式
     */
    fun setAudioOutputMode(isSpeakerMode: Boolean) {
        this.isSpeakerMode = isSpeakerMode
        AppLog.d("设置音频输出模式: ${if (isSpeakerMode) "扬声器" else "听筒"}")

        // 更新所有现有的音频播放器
        try {
            mediaAudioPlayers.values.forEach { player ->
                player.setPlaybackMode(isSpeakerMode)
            }
            micAudioPlayers.values.forEach { player ->
                player.setPlaybackMode(isSpeakerMode)
            }
            AppLog.d("已更新所有音频播放器的播放模式")
        } catch (e: Exception) {
            AppLog.e("更新音频播放器播放模式失败", e)
            callback.onAudioError("", "音频播放器", "更新播放模式失败: ${e.message}")
        }
    }

    /**
     * 设置接收端音量
     */
    fun setReceiverVolume(volume: Int) {
        this.receiverVolume = volume.coerceIn(0, 100)
        AppLog.d("接收端音量配置更新: ${this.receiverVolume}% (纯系统音量控制)")
    }

    /**
     * 加载音频输出模式设置
     */
    fun loadAudioOutputMode() {
        val sharedPrefs = context.getSharedPreferences("receiver_settings", Context.MODE_PRIVATE)
        isSpeakerMode = sharedPrefs.getBoolean("audio_output_speaker_mode", true) // 默认扬声器
        receiverVolume = sharedPrefs.getInt("receiver_volume", 80) // 默认80%音量
        AppLog.d("加载音频输出模式: ${if (isSpeakerMode) "扬声器" else "听筒"}")
        AppLog.d("加载接收端音量: ${receiverVolume}%")
    }

    /**
     * 处理媒体音频控制
     */
    fun handleMediaAudioControl(connectionId: String, isEnabled: Boolean) {
        handleAudioControl(connectionId, isEnabled, "媒体音频",
            mediaAudioPlayingStates, mediaAudioPlayers, ::ensureMediaAudioPlayerReady)
    }

    /**
     * 处理麦克风音频控制
     */
    fun handleMicAudioControl(connectionId: String, isEnabled: Boolean) {
        handleAudioControl(connectionId, isEnabled, "麦克风音频",
            micAudioPlayingStates, micAudioPlayers, ::ensureMicAudioPlayerReady)
    }

    /**
     * 处理媒体音频配置数据
     */
    fun processMediaAudioConfig(connectionId: String, configData: ByteArray) {
        processAudioConfig(connectionId, configData, "媒体音频",
            mediaAudioPlayingStates, mediaAudioDecoders, ::createMediaAudioPlayer)
    }

    /**
     * 处理麦克风音频配置数据
     */
    fun processMicAudioConfig(connectionId: String, configData: ByteArray) {
        processAudioConfig(connectionId, configData, "麦克风音频",
            micAudioPlayingStates, micAudioDecoders, ::createMicAudioPlayer)
    }

    /**
     * 清理指定连接的音频组件
     */
    fun cleanupAudioComponents(connectionId: String) {
        try {
            AppLog.service("清理连接 $connectionId 的音频组件")

            // 停止并移除媒体音频组件
            mediaAudioDecoders[connectionId]?.stop()
            mediaAudioDecoders.remove(connectionId)

            mediaAudioPlayers[connectionId]?.stop()
            mediaAudioPlayers.remove(connectionId)

            // 停止并移除麦克风音频组件
            micAudioDecoders[connectionId]?.stop()
            micAudioDecoders.remove(connectionId)

            micAudioPlayers[connectionId]?.stop()
            micAudioPlayers.remove(connectionId)

            // 清理播放状态
            mediaAudioPlayingStates.remove(connectionId)
            micAudioPlayingStates.remove(connectionId)

            callback.onAudioPlayerStopped(connectionId, "所有音频")
            AppLog.service("连接 $connectionId 的音频组件已清理")

        } catch (e: Exception) {
            AppLog.e("清理音频组件失败: $connectionId", e)
            callback.onAudioError(connectionId, "音频组件", "清理失败: ${e.message}")
        }
    }

    /**
     * 清理所有音频组件
     */
    fun cleanupAllAudioComponents() {
        try {
            listOf(mediaAudioDecoders, micAudioDecoders).forEach { decoders ->
                decoders.values.forEach { it.stop() }
                decoders.clear()
            }
            listOf(mediaAudioPlayers, micAudioPlayers).forEach { players ->
                players.values.forEach { it.stop() }
                players.clear()
            }

            // 清理播放状态
            mediaAudioPlayingStates.clear()
            micAudioPlayingStates.clear()

            // 清理缓存的配置数据
            pendingMediaAudioConfigs.clear()
            pendingMicAudioConfigs.clear()

            // 清理音频同步管理器
            audioSyncManager.clearAll()

            AppLog.service("所有音频组件已清理")
        } catch (e: Exception) {
            AppLog.e("清理所有音频组件失败", e)
            callback.onAudioError("", "音频组件", "清理失败: ${e.message}")
        }
    }

    /**
     * 根据AAC配置数据确定音频类型
     */
    fun determineAudioTypeFromConfig(configData: ByteArray): String {
        try {
            if (configData.size < 2) {
                AppLog.w("AAC配置数据太短，无法解析")
                return "unknown"
            }

            // 解析AAC AudioSpecificConfig格式
            // 提取通道配置（Channel Configuration）：第二个字节的第4-7位
            val channelConfig = (configData[1].toInt() and 0x78) shr 3

            AppLog.audio("AAC配置数据解析: 字节0=0x${configData[0].toUByte().toString(16)}, 字节1=0x${configData[1].toUByte().toString(16)}, 通道配置=$channelConfig")

            return when (channelConfig) {
                1 -> "mic_audio"    // 单声道 -> 麦克风音频
                2 -> "media_audio"  // 立体声 -> 媒体音频
                else -> {
                    AppLog.w("未知的通道配置: $channelConfig")
                    "unknown"
                }
            }
        } catch (e: Exception) {
            AppLog.e("解析AAC配置数据失败", e)
            return "unknown"
        }
    }

    /**
     * 处理缓存的音频配置数据
     */
    fun processPendingAudioConfigs(connectionId: String, ssrc: Long) {
        try {
            // 处理媒体音频配置
            pendingMediaAudioConfigs[ssrc]?.let { configData ->
                processMediaAudioConfig(connectionId, configData)
                pendingMediaAudioConfigs.remove(ssrc)
                AppLog.audio("处理缓存的媒体音频配置数据: connectionId=$connectionId, ssrc=$ssrc")
            }

            // 处理麦克风音频配置
            pendingMicAudioConfigs[ssrc]?.let { configData ->
                processMicAudioConfig(connectionId, configData)
                pendingMicAudioConfigs.remove(ssrc)
                AppLog.audio("处理缓存的麦克风音频配置数据: connectionId=$connectionId, ssrc=$ssrc")
            }
        } catch (e: Exception) {
            AppLog.e("处理缓存的音频配置数据失败: connectionId=$connectionId, ssrc=$ssrc", e)
            callback.onAudioError(connectionId, "音频配置", "处理缓存配置失败: ${e.message}")
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 🚀 零拷贝优化：通用音频数据处理方法 - 支持DataView
     */
    private fun handleAudioDataView(
        aacDataView: DataView,
        ssrc: Long,
        audioType: String,
        playingStates: ConcurrentHashMap<String, Boolean>,
        players: ConcurrentHashMap<String, AudioPlayer>,
        decoders: ConcurrentHashMap<String, AudioDecoder>,
        createPlayerFunc: (String) -> Boolean,
        updateStats: () -> Unit
    ) {
        try {
            val connectionId = stateManager.getAllConnections().find { it.getSSRC() == ssrc }?.connectionId
                ?: return

            audioSyncManager.recordPacketReceived(connectionId)
            if (audioSyncManager.shouldDropPacket(connectionId)) return

            val isEnabled = playingStates[connectionId] ?: true
            if (!isEnabled) return

            if (!players.containsKey(connectionId) || !decoders.containsKey(connectionId)) {
                if (!createPlayerFunc(connectionId)) return
            }

            val decoder = decoders[connectionId]
            val player = players[connectionId]
            if (decoder != null && player != null && player.isPlaying()) {
                updateStats()
                // 🚀 零拷贝优化：直接传递DataView给解码器
                decoder.decode(aacDataView)
            }
        } catch (e: Exception) {
            AppLog.e("处理${audioType}数据失败", e)
            callback.onAudioError("", audioType, "数据处理失败: ${e.message}")
        }
    }

    private fun handleMediaAudioData(aacDataView: DataView, ssrc: Long) {
        handleAudioDataView(
            aacDataView, ssrc, "媒体音频",
            mediaAudioPlayingStates, mediaAudioPlayers, mediaAudioDecoders,
            ::createMediaAudioPlayer
        ) {
            mediaAudioPacketsProcessed++
            mediaAudioBytesProcessed += aacDataView.size
            logMediaAudioStatisticsIfNeeded()
        }
    }

    private fun handleMicAudioData(aacDataView: DataView, ssrc: Long) {
        handleAudioDataView(
            aacDataView, ssrc, "麦克风音频",
            micAudioPlayingStates, micAudioPlayers, micAudioDecoders,
            ::createMicAudioPlayer
        ) {
            micAudioPacketsProcessed++
            micAudioBytesProcessed += aacDataView.size
            logMicAudioStatisticsIfNeeded()
        }
    }

    /**
     * 通用音频播放器创建方法
     */
    private fun createAudioPlayer(
        connectionId: String,
        channelCount: Int,
        players: ConcurrentHashMap<String, AudioPlayer>,
        decoders: ConcurrentHashMap<String, AudioDecoder>,
        audioType: String
    ): Boolean {
        try {
            if (players.containsKey(connectionId) && decoders.containsKey(connectionId)) {
                return true
            }

            // 清理不完整的组件
            players[connectionId]?.stop()
            decoders[connectionId]?.stop()
            players.remove(connectionId)
            decoders.remove(connectionId)

            val audioPlayer = AudioPlayer(
                context = context,
                sampleRate = AUDIO_SAMPLE_RATE,
                channelCount = channelCount,
                lowLatencyMode = true,
                isSpeakerMode = isSpeakerMode
            )
            val audioDecoder = AudioDecoder(
                sampleRate = AUDIO_SAMPLE_RATE,
                channelCount = channelCount,
                onDecodedData = { dataView, size ->
                    // 🚀 零拷贝优化：直接使用DataView播放音频
                    audioPlayer.playDataView(dataView, size)
                },
                lowLatencyMode = true
            )

            return if (audioPlayer.start() && audioDecoder.start()) {
                players[connectionId] = audioPlayer
                decoders[connectionId] = audioDecoder
                callback.onAudioPlayerCreated(connectionId, audioType)
                true
            } else {
                audioPlayer.stop()
                audioDecoder.stop()
                false
            }
        } catch (e: Exception) {
            AppLog.e("创建${audioType}播放器失败: $connectionId", e)
            callback.onAudioError(connectionId, audioType, "播放器创建失败: ${e.message}")
            return false
        }
    }

    private fun createMediaAudioPlayer(connectionId: String): Boolean =
        createAudioPlayer(connectionId, MEDIA_AUDIO_CHANNELS, mediaAudioPlayers, mediaAudioDecoders, "媒体音频")

    private fun createMicAudioPlayer(connectionId: String): Boolean =
        createAudioPlayer(connectionId, MIC_AUDIO_CHANNELS, micAudioPlayers, micAudioDecoders, "麦克风音频")

    /**
     * 通用音频控制处理方法
     */
    private fun handleAudioControl(
        connectionId: String,
        isEnabled: Boolean,
        audioType: String,
        playingStates: ConcurrentHashMap<String, Boolean>,
        players: ConcurrentHashMap<String, AudioPlayer>,
        ensurePlayerReadyFunc: (String) -> Unit
    ) {
        try {
            playingStates[connectionId] = isEnabled
            if (!isEnabled) {
                players[connectionId]?.clearQueue()
            } else {
                ensurePlayerReadyFunc(connectionId)
            }
        } catch (e: Exception) {
            AppLog.e("处理${audioType}控制失败: $connectionId", e)
            callback.onAudioError(connectionId, audioType, "控制失败: ${e.message}")
        }
    }

    /**
     * 通用确保播放器就绪方法
     */
    private fun ensureAudioPlayerReady(
        connectionId: String,
        audioType: String,
        players: ConcurrentHashMap<String, AudioPlayer>,
        decoders: ConcurrentHashMap<String, AudioDecoder>,
        createPlayerFunc: (String) -> Boolean
    ) {
        try {
            val hasPlayer = players.containsKey(connectionId)
            val hasDecoder = decoders.containsKey(connectionId)

            if (!hasPlayer || !hasDecoder) {
                players[connectionId]?.stop()
                decoders[connectionId]?.stop()
                players.remove(connectionId)
                decoders.remove(connectionId)

                if (!createPlayerFunc(connectionId)) return
            }

            val player = players[connectionId]
            if (player?.isPlaying() != true) {
                player?.start()
            }
        } catch (e: Exception) {
            AppLog.e("确保${audioType}播放器就绪失败: $connectionId", e)
            callback.onAudioError(connectionId, audioType, "播放器就绪失败: ${e.message}")
        }
    }

    private fun ensureMediaAudioPlayerReady(connectionId: String) {
        ensureAudioPlayerReady(connectionId, "媒体音频", mediaAudioPlayers, mediaAudioDecoders, ::createMediaAudioPlayer)
    }

    private fun ensureMicAudioPlayerReady(connectionId: String) {
        ensureAudioPlayerReady(connectionId, "麦克风音频", micAudioPlayers, micAudioDecoders, ::createMicAudioPlayer)
    }

    /**
     * 通用音频配置处理核心逻辑
     */
    private fun processAudioConfig(
        connectionId: String,
        configData: ByteArray,
        audioType: String,
        playingStates: ConcurrentHashMap<String, Boolean>,
        decoders: ConcurrentHashMap<String, AudioDecoder>,
        createPlayerFunc: (String) -> Boolean
    ) {
        try {
            val isEnabled = playingStates[connectionId] ?: true
            if (!isEnabled) return

            if (!decoders.containsKey(connectionId)) {
                if (!createPlayerFunc(connectionId)) return
            }

            decoders[connectionId]?.setConfigData(configData)
        } catch (e: Exception) {
            AppLog.e("处理${audioType}配置数据失败: $connectionId", e)
            callback.onAudioError(connectionId, audioType, "配置处理失败: ${e.message}")
        }
    }

    /**
     * 定期输出媒体音频统计信息
     */
    private fun logMediaAudioStatisticsIfNeeded() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastMediaAudioLogTime >= LOG_INTERVAL_MS) {
            if (mediaAudioPacketsProcessed > 0) {
                callback.onAudioStatistics(
                    "媒体音频",
                    mediaAudioPacketsProcessed,
                    mediaAudioBytesProcessed,
                    mediaAudioDecoders.size
                )

                // 重置统计计数器
                mediaAudioPacketsProcessed = 0
                mediaAudioBytesProcessed = 0L
            }
            lastMediaAudioLogTime = currentTime
        }
    }

    /**
     * 定期输出麦克风音频统计信息
     */
    private fun logMicAudioStatisticsIfNeeded() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastMicAudioLogTime >= LOG_INTERVAL_MS) {
            if (micAudioPacketsProcessed > 0) {
                callback.onAudioStatistics(
                    "麦克风音频",
                    micAudioPacketsProcessed,
                    micAudioBytesProcessed,
                    micAudioDecoders.size
                )

                // 重置统计计数器
                micAudioPacketsProcessed = 0
                micAudioBytesProcessed = 0L
            }
            lastMicAudioLogTime = currentTime
        }
    }
}
