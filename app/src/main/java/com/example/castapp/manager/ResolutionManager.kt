package com.example.castapp.manager

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import android.util.DisplayMetrics
import android.view.WindowInsets
import android.view.WindowManager
import androidx.core.content.edit
import kotlin.math.roundToInt
import com.example.castapp.utils.AppLog

/**
 * 分辨率管理器
 * 负责管理动态分辨率调整功能
 */
class ResolutionManager private constructor(private val context: Context) {
    
    companion object {        private const val PREFS_NAME = "resolution_settings"
        private const val KEY_RESOLUTION_SCALE = "resolution_scale_percent"
        private const val DEFAULT_RESOLUTION_SCALE = 100 // 默认100%
        private const val MIN_RESOLUTION_SCALE = 1 // 最小1%
        private const val MAX_RESOLUTION_SCALE = 150 // 最大150%
        
        @Volatile
        @SuppressLint("StaticFieldLeak") // 使用ApplicationContext，安全持有
        private var INSTANCE: ResolutionManager? = null

        fun getInstance(context: Context): ResolutionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ResolutionManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    // 缓存设备原始屏幕分辨率
    private var cachedOriginalResolution: Pair<Int, Int>? = null
    
    /**
     * 获取设备原始屏幕分辨率
     */
    fun getOriginalScreenResolution(): Pair<Int, Int> {
        if (cachedOriginalResolution == null) {
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager

            val width: Int
            val height: Int

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // 使用新的WindowMetrics API (Android R+)
                val windowMetrics = windowManager.currentWindowMetrics
                val bounds = windowMetrics.bounds
                width = bounds.width()
                height = bounds.height()
            } else {
                // 兼容旧版本API
                val displayMetrics = DisplayMetrics()
                @Suppress("DEPRECATION")
                windowManager.defaultDisplay.getRealMetrics(displayMetrics)
                width = displayMetrics.widthPixels
                height = displayMetrics.heightPixels
            }

            cachedOriginalResolution = Pair(width, height)
            AppLog.d("设备原始屏幕分辨率: ${width}x${height}")
        }

        return cachedOriginalResolution!!
    }

    /**
     * 获取状态栏高度
     * 使用WindowInsets API获取状态栏高度，避免使用内部资源
     */
    @SuppressLint("InternalInsetResource", "DiscouragedApi")
    private fun getStatusBarHeight(): Int {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11+ 使用WindowInsets API
                val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
                val windowMetrics = windowManager.currentWindowMetrics
                val windowInsets = windowMetrics.windowInsets
                val insets = windowInsets.getInsetsIgnoringVisibility(WindowInsets.Type.statusBars())
                insets.top
            } else {
                // 兼容旧版本，使用资源获取（已添加抑制警告注解）
                val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
                if (resourceId > 0) {
                    context.resources.getDimensionPixelSize(resourceId)
                } else {
                    // 如果无法获取，使用经验值
                    (24 * context.resources.displayMetrics.density).toInt() // 24dp转换为px
                }
            }
        } catch (e: Exception) {
            AppLog.w("获取状态栏高度失败，使用默认值: ${e.message}")
            // 使用经验值作为后备方案
            (24 * context.resources.displayMetrics.density).toInt() // 24dp转换为px
        }
    }

    /**
     * 获取可用屏幕分辨率（不包括状态栏）
     * 用于遥控端计算远程控制窗口尺寸
     */
    fun getUsableScreenResolution(): Pair<Int, Int> {
        val (fullWidth, fullHeight) = getOriginalScreenResolution()
        val statusBarHeight = getStatusBarHeight()
        val usableHeight = fullHeight - statusBarHeight

        AppLog.d("可用屏幕分辨率: ${fullWidth}x${usableHeight} (状态栏高度: ${statusBarHeight}px)")
        return Pair(fullWidth, usableHeight)
    }
    
    /**
     * 获取当前分辨率缩放百分比
     */
    fun getCurrentResolutionScale(): Int {
        return sharedPreferences.getInt(KEY_RESOLUTION_SCALE, DEFAULT_RESOLUTION_SCALE)
    }
    
    /**
     * 设置分辨率缩放百分比
     */
    fun setResolutionScale(scalePercent: Int): Boolean {
        if (scalePercent < MIN_RESOLUTION_SCALE || scalePercent > MAX_RESOLUTION_SCALE) {
            AppLog.w("分辨率缩放百分比超出有效范围: $scalePercent%, 有效范围: $MIN_RESOLUTION_SCALE%-$MAX_RESOLUTION_SCALE%")
            return false
        }
        
        sharedPreferences.edit {
            putInt(KEY_RESOLUTION_SCALE, scalePercent)
        }
        AppLog.d("分辨率缩放百分比已设置为: $scalePercent%")
        return true
    }
    
    /**
     * 根据当前缩放百分比计算实际使用的分辨率
     */
    fun getCurrentResolution(): Pair<Int, Int> {
        val scalePercent = getCurrentResolutionScale()
        return calculateResolution(scalePercent)
    }
    
    /**
     * 根据指定缩放百分比计算分辨率，保持等比缩放
     */
    fun calculateResolution(scalePercent: Int): Pair<Int, Int> {
        if (scalePercent < MIN_RESOLUTION_SCALE || scalePercent > MAX_RESOLUTION_SCALE) {
            AppLog.w("分辨率缩放百分比超出有效范围: $scalePercent%")
            // 避免递归调用，直接使用默认值
            return calculateResolutionInternal(DEFAULT_RESOLUTION_SCALE)
        }

        return calculateResolutionInternal(scalePercent)
    }

    /**
     * 内部分辨率计算方法，保持等比缩放
     */
    private fun calculateResolutionInternal(scalePercent: Int): Pair<Int, Int> {
        val (originalWidth, originalHeight) = getOriginalScreenResolution()

        val scaledWidth = (originalWidth * scalePercent / 100.0).roundToInt()
        val scaledHeight = (originalHeight * scalePercent / 100.0).roundToInt()

        // 保持等比缩放的偶数调整
        val evenWidth = makeEven(scaledWidth)
        val evenHeight = makeEven(scaledHeight)

        AppLog.d("计算得到的分辨率: ${evenWidth}x${evenHeight} (缩放比例: $scalePercent%)")
        return Pair(evenWidth, evenHeight)
    }

    /**
     * 根据指定缩放百分比计算屏幕捕获分辨率
     * 专门用于VirtualDisplay的动态分辨率调整，保持等比缩放
     */
    fun calculateCaptureResolution(scalePercent: Int): Pair<Int, Int> {
        if (scalePercent < MIN_RESOLUTION_SCALE || scalePercent > MAX_RESOLUTION_SCALE) {
            AppLog.w("捕获分辨率缩放百分比超出有效范围: $scalePercent%")
            // 返回当前编码分辨率作为回退
            return getCurrentResolution()
        }

        val (originalWidth, originalHeight) = getOriginalScreenResolution()

        // 计算缩放后的分辨率
        val scaledWidth = (originalWidth * scalePercent / 100.0).roundToInt()
        val scaledHeight = (originalHeight * scalePercent / 100.0).roundToInt()

        // 保持等比缩放的偶数调整
        val evenWidth = makeEven(scaledWidth)
        val evenHeight = makeEven(scaledHeight)

        // 检查硬件编码器最小分辨率限制
        // 大多数Android硬件编码器要求最小分辨率为128x128
        val hardwareMinWidth = 128
        val hardwareMinHeight = 128

        val finalWidth: Int
        val finalHeight: Int

        if (evenWidth < hardwareMinWidth || evenHeight < hardwareMinHeight) {
            // 计算需要的缩放比例以满足硬件编码器最小分辨率要求
            val widthScale = hardwareMinWidth.toDouble() / evenWidth
            val heightScale = hardwareMinHeight.toDouble() / evenHeight
            val requiredScale = maxOf(widthScale, heightScale)

            // 应用缩放并保持偶数
            finalWidth = makeEven((evenWidth * requiredScale).roundToInt())
            finalHeight = makeEven((evenHeight * requiredScale).roundToInt())

            AppLog.w("分辨率过小，应用硬件编码器最小分辨率限制")
            AppLog.w("  原始计算: ${evenWidth}x${evenHeight}")
            AppLog.w("  硬件最小要求: ${hardwareMinWidth}x${hardwareMinHeight}")
            AppLog.w("  调整后分辨率: ${finalWidth}x${finalHeight}")
            AppLog.w("  缩放比例: ${String.format("%.2f", requiredScale)}")
        } else {
            finalWidth = evenWidth
            finalHeight = evenHeight
            AppLog.d("分辨率满足硬件编码器要求: ${finalWidth}x${finalHeight}")
        }

        // 验证最终分辨率的宽高比是否与原始分辨率接近
        val originalRatio = originalWidth.toDouble() / originalHeight
        val finalRatio = finalWidth.toDouble() / finalHeight
        val ratioDifference = kotlin.math.abs(originalRatio - finalRatio) / originalRatio

        if (ratioDifference > 0.05) { // 如果宽高比差异超过5%
            AppLog.w("警告：缩放后宽高比发生较大变化")
            AppLog.w("  原始宽高比: ${String.format("%.3f", originalRatio)}")
            AppLog.w("  缩放后宽高比: ${String.format("%.3f", finalRatio)}")
            AppLog.w("  差异: ${String.format("%.1f", ratioDifference * 100)}%")
        }

        AppLog.d("计算得到的捕获分辨率: ${finalWidth}x${finalHeight} (缩放比例: $scalePercent%)")
        AppLog.d("  原始: ${originalWidth}x${originalHeight} → 缩放: ${finalWidth}x${finalHeight}")
        AppLog.d("  已应用硬件编码器最小分辨率限制 (128x128)")
        return Pair(finalWidth, finalHeight)
    }

    /**
     * 确保数值为偶数（H.264编码要求）
     */
    private fun makeEven(value: Int): Int {
        return if (value % 2 == 0) value else value - 1
    }
}
