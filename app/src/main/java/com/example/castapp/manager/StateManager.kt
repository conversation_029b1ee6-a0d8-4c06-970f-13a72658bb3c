package com.example.castapp.manager

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import android.os.Looper
import androidx.core.content.edit
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.example.castapp.model.Connection
import com.example.castapp.utils.AppLog

import kotlinx.coroutines.*
import org.json.JSONArray
import org.json.JSONObject
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 状态管理器 - 解决多层状态管理导致的状态不一致问题
 */
class StateManager private constructor(context: Application) {
    
    companion object {
        private const val PREFS_NAME = "state_prefs"
        private const val KEY_CONNECTIONS = "connections"
        private const val KEY_SERVICE_STATES = "service_states"
        private const val KEY_DEVICE_NAMES = "device_names"
        
        @Volatile
        private var INSTANCE: StateManager? = null
        
        fun getInstance(context: Application): StateManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: StateManager(context).also { INSTANCE = it }
            }
        }
    }
    
    // 协程作用域
    private val managerScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    // SharedPreferences
    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    // === 简化状态数据 ===

    // 连接列表 - 使用线程安全的CopyOnWriteArrayList（集成状态管理）
    private val connectionsList = CopyOnWriteArrayList<Connection>()

    // 设备名称映射 - 存储连接ID到设备名称的映射
    private val deviceNames = ConcurrentHashMap<String, String>()

    // 服务状态
    private val serviceStates = ConcurrentHashMap<String, Boolean>()
    
    // === LiveData 暴露给UI ===
    
    private val _connections = MutableLiveData<List<Connection>>()
    val connections: LiveData<List<Connection>> = _connections
    
    private val _castingConnections = MutableLiveData<Set<String>>()
    val castingConnections: LiveData<Set<String>> = _castingConnections
    
    private val _isReceivingServiceRunning = MutableLiveData<Boolean>()
    val isReceivingServiceRunning: LiveData<Boolean> = _isReceivingServiceRunning
    
    private val _receivingPort = MutableLiveData<Int>()
    val receivingPort: LiveData<Int> = _receivingPort
    
    // === 状态变化监听器 ===

    private val connectionChangeListeners = ConcurrentHashMap<String, (Connection) -> Unit>()
    private val serviceStateListeners = ConcurrentHashMap<String, (String, Boolean) -> Unit>()

    // 🚀 新增：精准状态变化监听器，用于局部UI更新
    private val preciseStateChangeListeners = ConcurrentHashMap<String, (String, Connection) -> Unit>()
    
    init {
        AppLog.state("状态管理器初始化")
        loadState()
    }

    /**
     * 添加连接 - 原子性操作（同步版本，避免阻塞主线程）
     */
    fun addConnection(ipAddress: String, port: Int): Connection {
        val connection = Connection.create(ipAddress, port)
        return addExistingConnection(connection)
    }

    /**
     * 添加已存在的连接对象到StateManager - 简化版
     */
    fun addExistingConnection(connection: Connection): Connection {
        // 确保连接处于初始状态
        val initialConnection = connection.copy(
            isConnected = true,
            isCasting = false,
            isMediaAudioEnabled = false,
            isMicAudioEnabled = false,
            webSocketConnected = false,
            lastUpdateTime = System.currentTimeMillis()
        )

        // 原子性添加
        connectionsList.add(initialConnection)

        // 通知UI更新
        updateLiveData()

        // 异步保存状态，避免阻塞主线程
        saveState()

        AppLog.state("添加连接: ${initialConnection.getDisplayText()}, 连接ID: ${initialConnection.connectionId}")
        return initialConnection
    }

    /**
     * 更新已存在的连接对象
     */
    fun updateExistingConnection(connection: Connection) {
        val connectionIndex = connectionsList.indexOfFirst { it.connectionId == connection.connectionId }
        if (connectionIndex >= 0) {
            // 原子性更新
            connectionsList[connectionIndex] = connection

            // 通知UI更新
            updateLiveData()

            // 异步保存状态，避免阻塞主线程
            saveState()

            AppLog.state("更新连接: ${connection.getDisplayText()}, 连接ID: ${connection.connectionId}")
        } else {
            AppLog.w("尝试更新不存在的连接: ${connection.connectionId}")
        }
    }

    
    /**
     * 移除连接 - 原子性操作
     */
    fun removeConnection(connection: Connection) {
        removeConnectionById(connection.connectionId)
    }

    /**
     * 通过connectionId移除连接 - 简化版
     */
    fun removeConnectionById(connectionId: String) {
        managerScope.launch {
            // 查找要移除的连接
            val connectionToRemove = connectionsList.find { it.connectionId == connectionId }

            // 原子性移除
            connectionsList.removeAll { it.connectionId == connectionId }

            // 通知UI更新
            updateLiveData()
            saveState()

            if (connectionToRemove != null) {
                AppLog.state("移除连接: ${connectionToRemove.getDisplayText()}")
            } else {
                AppLog.state("移除连接: $connectionId (连接不存在)")
            }
        }
    }
    
    /**
     * 更新连接状态 - 简化版（直接操作Connection对象）
     */
    fun updateConnection(
        connectionId: String,
        updateBlock: (Connection) -> Connection
    ) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            updateConnectionInternal(connectionId, updateBlock)
        } else {
            managerScope.launch(Dispatchers.Main) {
                updateConnectionInternal(connectionId, updateBlock)
            }
        }
    }
    
    /**
     * 内部连接更新方法 - 🚀 优化：添加精准状态变化通知
     */
    private fun updateConnectionInternal(
        connectionId: String,
        updateBlock: (Connection) -> Connection
    ) {
        val connectionIndex = connectionsList.indexOfFirst { it.connectionId == connectionId }
        if (connectionIndex >= 0) {
            val currentConnection = connectionsList[connectionIndex]
            val updatedConnection = updateBlock(currentConnection)

            // 原子性更新
            connectionsList[connectionIndex] = updatedConnection

            // 🚀 新增：精准状态变化通知，只通知具体变化的连接
            preciseStateChangeListeners[connectionId]?.invoke(connectionId, updatedConnection)

            // 通知监听器
            connectionChangeListeners[connectionId]?.invoke(updatedConnection)

            // 通知UI更新
            updateLiveData()
            saveState()

            AppLog.state("更新连接: $connectionId -> ${updatedConnection.getStatusSummary()}")
        } else {
            AppLog.w("连接不存在: $connectionId")
        }
    }
    


    /**
     * 统一的连接断开处理 - 🚀 根源优化：移除防重复机制，从源头避免重复调用
     */
    fun handleConnectionDisconnected(connectionId: String, reason: String = "未知") {
        AppLog.state("统一处理连接断开: $connectionId, 原因: $reason")

        updateConnection(connectionId) { currentConnection ->
            currentConnection
                .withConnection(false)
                .withCasting(false)
                .withMediaAudio(false)
                .withMicAudio(false)
                .withWebSocketConnection(false)
                .withError("连接已断开: $reason")
        }

        // 通知所有监听器连接已断开
        connectionChangeListeners[connectionId]?.invoke(
            findConnectionById(connectionId) ?: return
        )

        AppLog.state("连接断开处理完成: $connectionId")
    }

    /**
     * 处理WebSocket断开连接事件 - 简化版（调用统一处理）
     */
    fun handleWebSocketDisconnected(connectionId: String) {
        handleConnectionDisconnected(connectionId, "WebSocket断开")
    }



    /**
     * 获取连接（替代getConnectionState）
     */
    fun getConnection(connectionId: String): Connection? {
        return findConnectionById(connectionId)
    }
    
    // 简化的查询方法

    /**
     * 通过连接ID查找连接
     */
    fun findConnectionById(connectionId: String): Connection? {
        return connectionsList.find { it.connectionId == connectionId }
    }

    /**
     * 获取所有连接
     */
    fun getAllConnections(): List<Connection> {
        return connectionsList.toList()
    }

    /**
     * 更新连接的设备名称
     */
    fun updateConnectionDeviceName(connectionId: String, deviceName: String) {
        deviceNames[connectionId] = deviceName
        saveState()
        AppLog.state("更新设备名称: $connectionId -> $deviceName")
    }

    /**
     * 获取连接的设备名称
     */
    fun getConnectionDeviceName(connectionId: String): String? {
        return deviceNames[connectionId]
    }

    /**
     * 更新服务状态
     */
    fun updateServiceState(serviceName: String, isRunning: Boolean, port: Int? = null) {
        serviceStates[serviceName] = isRunning
        
        when (serviceName) {
            "receiving" -> {
                _isReceivingServiceRunning.value = isRunning
                port?.let { _receivingPort.value = it }
            }
        }
        
        // 通知监听器
        serviceStateListeners.values.forEach { listener ->
            listener(serviceName, isRunning)
        }
        
        saveState()
        AppLog.state("更新服务状态: $serviceName = $isRunning")
    }
    
    /**
     * 更新LiveData（线程安全版本）
     */
    private fun updateLiveData() {
        // 使用postValue确保线程安全，可以在任何线程中调用
        _connections.postValue(connectionsList.toList())

        // 更新投屏连接集合（简化版：直接从Connection获取）
        val castingIds = connectionsList
            .filter { it.isCasting }
            .map { it.connectionId }
            .toSet()
        _castingConnections.postValue(castingIds)
    }
    


    /**
     * 保存状态到SharedPreferences
     */
    private fun saveState() {
        managerScope.launch(Dispatchers.IO) {
            try {
                sharedPreferences.edit {
                    // 保存连接列表（统一ID架构）
                    val connectionsArray = JSONArray()
                    connectionsList.forEach { connection ->
                        val connectionJson = JSONObject().apply {
                            put("connectionId", connection.connectionId)
                            put("ipAddress", connection.ipAddress)
                            put("port", connection.port)
                        }
                        connectionsArray.put(connectionJson)
                    }
                    putString(KEY_CONNECTIONS, connectionsArray.toString())

                    // 保存服务状态
                    val serviceStatesJson = JSONObject()
                    serviceStates.forEach { (key, value) ->
                        serviceStatesJson.put(key, value)
                    }
                    putString(KEY_SERVICE_STATES, serviceStatesJson.toString())

                    // 保存设备名称映射
                    val deviceNamesJson = JSONObject()
                    deviceNames.forEach { (connectionId, deviceName) ->
                        deviceNamesJson.put(connectionId, deviceName)
                    }
                    putString(KEY_DEVICE_NAMES, deviceNamesJson.toString())
                }
                AppLog.state("状态保存成功")

            } catch (e: Exception) {
                AppLog.e("保存状态失败", e)
            }
        }
    }

    /**
     * 从SharedPreferences加载状态
     */
    private fun loadState() {
        managerScope.launch(Dispatchers.IO) {
            try {
                // 加载连接列表
                val connectionsJson = sharedPreferences.getString(KEY_CONNECTIONS, null)
                if (!connectionsJson.isNullOrEmpty()) {
                    val connectionsArray = JSONArray(connectionsJson)
                    val loadedConnections = mutableListOf<Connection>()

                    for (i in 0 until connectionsArray.length()) {
                        val connectionJson = connectionsArray.getJSONObject(i)

                        // 统一ID架构：简化连接加载
                        val connection = Connection(
                            connectionId = connectionJson.getString("connectionId"),
                            ipAddress = connectionJson.getString("ipAddress"),
                            port = connectionJson.getInt("port")
                        )

                        loadedConnections.add(connection)

                        // Connection现在已经包含状态，无需单独创建ConnectionState
                    }

                    connectionsList.clear()
                    connectionsList.addAll(loadedConnections)
                }

                // 加载服务状态
                val serviceStatesJson = sharedPreferences.getString(KEY_SERVICE_STATES, null)
                if (!serviceStatesJson.isNullOrEmpty()) {
                    val serviceStatesObj = JSONObject(serviceStatesJson)
                    serviceStatesObj.keys().forEach { key ->
                        serviceStates[key] = serviceStatesObj.getBoolean(key)
                    }
                }

                // 加载设备名称映射
                val deviceNamesJson = sharedPreferences.getString(KEY_DEVICE_NAMES, null)
                if (!deviceNamesJson.isNullOrEmpty()) {
                    val deviceNamesObj = JSONObject(deviceNamesJson)
                    deviceNamesObj.keys().forEach { connectionId ->
                        deviceNames[connectionId] = deviceNamesObj.getString(connectionId)
                    }
                }

                // 在主线程更新UI
                withContext(Dispatchers.Main) {
                    updateLiveData()
                    AppLog.state("状态加载成功: ${connectionsList.size} 个连接, ${deviceNames.size} 个设备名称")
                }

            } catch (e: Exception) {
                AppLog.e("加载状态失败", e)
            }
        }
    }

    /**
     * 🚀 新增：注册精准状态变化监听器
     */
    fun registerPreciseStateChangeListener(connectionId: String, listener: (String, Connection) -> Unit) {
        preciseStateChangeListeners[connectionId] = listener
        AppLog.state("注册精准状态变化监听器: $connectionId")
    }

    /**
     * 🚀 新增：移除精准状态变化监听器
     */
    fun unregisterPreciseStateChangeListener(connectionId: String) {
        preciseStateChangeListeners.remove(connectionId)
        AppLog.state("移除精准状态变化监听器: $connectionId")
    }

    /**
     * 🚀 新增：注册服务状态监听器
     */
    fun addServiceStateListener(listenerId: String, listener: (String, Boolean) -> Unit) {
        serviceStateListeners[listenerId] = listener
        AppLog.state("注册服务状态监听器: $listenerId")
    }

    /**
     * 🚀 新增：移除服务状态监听器
     */
    fun removeServiceStateListener(listenerId: String) {
        serviceStateListeners.remove(listenerId)
        AppLog.state("移除服务状态监听器: $listenerId")
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        managerScope.cancel()
        connectionChangeListeners.clear()
        serviceStateListeners.clear()
        preciseStateChangeListeners.clear()
        AppLog.state("状态管理器资源清理完成")
    }
}
