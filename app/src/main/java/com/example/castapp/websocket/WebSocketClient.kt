package com.example.castapp.websocket

import org.java_websocket.client.WebSocketClient as JavaWebSocketClient
import org.java_websocket.handshake.ServerHandshake
import java.net.URI
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import com.example.castapp.utils.ResourceManager
import com.example.castapp.utils.ResourceManager.safeExecute
import com.example.castapp.utils.AppLog

/**
 * WebSocket客户端
 * 用于发送端连接到接收端的WebSocket服务器
 */
class WebSocketClient(
    serverUri: URI,
    private val connectionId: String,
    private val onMessageReceived: (ControlMessage) -> Unit = {},
    private val onConnectionStateChanged: (Boolean) -> Unit = {}
) : JavaWebSocketClient(serverUri) {

    companion object {        private const val CONNECTION_TIMEOUT = 10000L // 10秒连接超时
        private const val HEARTBEAT_INTERVAL = 30000L // 30秒心跳间隔
    }

    private val isConnected = AtomicBoolean(false)
    private val connectionLatch = CountDownLatch(1)
    private var heartbeatThread: Thread? = null
    private val shouldSendHeartbeat = AtomicBoolean(false)

    // 🔥 移除消息去重：允许快速切换开关时正常发送消息
    // private val messageDedupCache = ConcurrentHashMap<String, Long>()
    // private val DEDUP_WINDOW_MS = 1000L // 1秒去重窗口

    override fun onOpen(handshake: ServerHandshake) {
        AppLog.websocket("WebSocket连接已建立: ${uri.host}:${uri.port}")
        isConnected.set(true)
        connectionLatch.countDown()
        onConnectionStateChanged(true)

        // 发送连接请求
        AppLog.websocket("WebSocketClient发送连接请求:")
        AppLog.websocket("  - 使用的connectionId: $connectionId")
        AppLog.websocket("  - 服务器地址: ${uri.host}:${uri.port}")

        val deviceName = com.example.castapp.utils.DeviceUtils.getDeviceDisplayName()
        val connectionRequest = ControlMessage.createConnectionRequest(connectionId, deviceName)
        AppLog.websocket("  - 连接请求消息: ${connectionRequest.toJson()}")
        AppLog.websocket("  - 设备信息: $deviceName")
        sendMessageInternal(connectionRequest)

        // 启动心跳
        startHeartbeat()
    }

    override fun onMessage(message: String) {
        try {
            val controlMessage = ControlMessage.fromJson(message)
            if (controlMessage != null) {
                // 只记录非心跳消息，减少日志输出
                if (controlMessage.type != ControlMessage.TYPE_HEARTBEAT) {
                    AppLog.websocket("收到控制消息: ${controlMessage.type}")
                }
                onMessageReceived(controlMessage)
            } else {
                AppLog.w("无法解析控制消息: $message")
            }
        } catch (e: Exception) {
            AppLog.e("处理WebSocket消息失败", e)
        }
    }

    override fun onClose(code: Int, reason: String, remote: Boolean) {
        AppLog.websocket("WebSocket连接关闭: $reason, 远程关闭: $remote")
        isConnected.set(false)
        shouldSendHeartbeat.set(false)
        onConnectionStateChanged(false)

        // 🔥 移除消息去重：不再需要清理缓存
        // messageDedupCache.clear()

        // 停止心跳
        stopHeartbeat()
    }

    override fun onError(ex: Exception) {
        AppLog.e("WebSocket连接错误", ex)
        isConnected.set(false)
        connectionLatch.countDown()
        onConnectionStateChanged(false)
    }

    /**
     * 连接到服务器
     * 优化：减少连接超时时间，避免长时间阻塞
     */
    fun connectToServer(): Boolean {
        return safeExecute("连接WebSocket服务器") {
            AppLog.websocket("连接WebSocket: ${uri.host}:${uri.port}")
            connect()

            // 减少连接超时时间，避免长时间阻塞主线程
            val reducedTimeout = minOf(CONNECTION_TIMEOUT, 3000) // 最多3秒
            val connected = connectionLatch.await(reducedTimeout, TimeUnit.MILLISECONDS)
            if (connected && isConnected.get()) {
                AppLog.websocket("WebSocket连接成功")
                true
            } else {
                AppLog.w("WebSocket连接失败或超时")
                false
            }
        } ?: false
    }

    /**
     * 发送控制消息（公共方法）
     */
    fun sendMessage(message: ControlMessage): Boolean {
        return sendMessageInternal(message)
    }

    /**
     * 🔥 移除消息去重：不再生成去重键
     */
    /*
    private fun generateDedupKey(message: ControlMessage): String {
        return when (message.type) {
            ControlMessage.TYPE_FUNCTION_CONTROL -> {
                val functionType = message.getStringData("function_type") ?: ""
                val enabled = message.getBooleanData("enabled") ?: false
                "${message.type}_${message.connectionId}_${functionType}_$enabled"
            }
            ControlMessage.TYPE_AAC_CONFIG -> {
                val config = message.getStringData("config") ?: ""
                "${message.type}_${message.connectionId}_${config.hashCode()}"
            }
            ControlMessage.TYPE_MEDIA_AUDIO_CONTROL -> {
                val enabled = message.getBooleanData("enabled") ?: false
                "${message.type}_${message.connectionId}_$enabled"
            }
            else -> "${message.type}_${message.connectionId}"
        }
    }
    */

    /**
     * 🔥 移除消息去重：允许所有消息正常发送
     */
    /*
    private fun shouldDedupMessage(message: ControlMessage): Boolean {
        // 心跳消息不需要去重
        if (message.type == ControlMessage.TYPE_HEARTBEAT) {
            return false
        }

        val dedupKey = generateDedupKey(message)
        val currentTime = System.currentTimeMillis()
        val lastSentTime = messageDedupCache[dedupKey]

        return if (lastSentTime != null && (currentTime - lastSentTime) < DEDUP_WINDOW_MS) {
            AppLog.websocket("消息去重：跳过重复发送 ${message.type}")
            true
        } else {
            messageDedupCache[dedupKey] = currentTime
            // 清理过期的缓存条目
            cleanupExpiredDedupCache(currentTime)
            false
        }
    }
    */

    /**
     * 🔥 移除消息去重：不再需要清理缓存
     */
    /*
    private fun cleanupExpiredDedupCache(currentTime: Long) {
        val iterator = messageDedupCache.entries.iterator()
        while (iterator.hasNext()) {
            val entry = iterator.next()
            if (currentTime - entry.value > DEDUP_WINDOW_MS * 2) {
                iterator.remove()
            }
        }
    }
    */

    /**
     * 发送控制消息（内部方法）
     */
    private fun sendMessageInternal(message: ControlMessage): Boolean {
        // 🔥 移除消息去重：直接发送所有消息
        // if (shouldDedupMessage(message)) {
        //     return true // 返回true表示"成功"跳过了重复消息
        // }

        return if (isConnected.get() && isOpen) {
            safeExecute("发送WebSocket消息: ${message.type}") {
                send(message.toJson())
                // 只记录非心跳消息，减少日志输出
                if (message.type != ControlMessage.TYPE_HEARTBEAT) {
                    AppLog.websocket("发送控制消息: ${message.type}")
                }
                true
            } ?: false
        } else {
            if (message.type != ControlMessage.TYPE_HEARTBEAT) {
                AppLog.w("WebSocket未连接，无法发送消息: ${message.type}")
            }
            false
        }
    }

    /**
     * 发送屏幕分辨率信息（统一ID架构）
     */
    fun sendScreenResolution(width: Int, height: Int): Boolean {
        val message = ControlMessage.createScreenResolution(connectionId, width, height)
        return sendMessageInternal(message)
    }



    /**
     * 发送断开连接消息（统一ID架构）
     */
    fun sendDisconnect(): Boolean {
        val message = ControlMessage.createDisconnect(connectionId)
        return sendMessageInternal(message)
    }

    /**
     * 发送H.264配置数据（统一ID架构）
     */
    fun sendH264Config(spsData: ByteArray?, ppsData: ByteArray?): Boolean {
        val message = ControlMessage.createH264Config(connectionId, spsData, ppsData)
        return sendMessageInternal(message)
    }

    /**
     * 🚀 新增：发送包含分辨率信息的H.264配置数据（统一ID架构）
     * 提供WebSocket分辨率传递机制，作为SPS解析的补充验证
     */
    fun sendH264ConfigWithResolution(spsData: ByteArray?, ppsData: ByteArray?, width: Int, height: Int): Boolean {
        val message = ControlMessage.createH264ConfigWithResolution(connectionId, spsData, ppsData, width, height)
        return sendMessageInternal(message)
    }

    /**
     * 🎯 横竖屏适配：发送包含分辨率和方向信息的H.264配置数据
     */
    fun sendH264ConfigWithResolutionAndOrientation(spsData: ByteArray?, ppsData: ByteArray?, width: Int, height: Int, orientation: Int): Boolean {
        val message = ControlMessage.createH264ConfigWithResolutionAndOrientation(connectionId, spsData, ppsData, width, height, orientation)
        return sendMessageInternal(message)
    }

    /**
     * 发送AAC配置数据（统一ID架构）
     */
    fun sendAacConfig(configData: ByteArray?): Boolean {
        val message = ControlMessage.createAacConfig(connectionId, configData)
        return sendMessageInternal(message)
    }

    /**
     * 发送SSRC映射信息（统一ID架构）
     */
    fun sendSsrcMapping(ssrc: Long): Boolean {
        val message = ControlMessage.createSsrcMapping(connectionId, ssrc)
        return sendMessageInternal(message)
    }

    /**
     * 发送投屏状态同步消息（统一ID架构）
     */
    fun sendCastingState(isCasting: Boolean, reason: String = ""): Boolean {
        val message = ControlMessage.createCastingState(connectionId, isCasting, reason)
        return sendMessageInternal(message)
    }

    /**
     * 发送视频流停止消息（统一ID架构）
     */
    fun sendVideoStreamStop(): Boolean {
        val message = ControlMessage.createVideoStreamStop(connectionId)
        return sendMessageInternal(message)
    }

    /**
     * 发送码率控制消息（统一ID架构）
     */
    fun sendBitrateControl(bitrate: Int, targetConnectionId: String? = null): Boolean {
        val message = ControlMessage.createBitrateControl(connectionId, bitrate, targetConnectionId)
        val success = sendMessageInternal(message)

        if (!success) {
            AppLog.w("码率控制消息发送失败，连接状态: connected=${isConnected.get()}, open=$isOpen")
            // 如果发送失败，可以考虑重试或缓存消息
        } else {
            AppLog.websocket("码率控制消息发送成功: ${bitrate / 1_000_000}Mbps")
        }

        return success
    }

    /**
     * 启动心跳
     */
    private fun startHeartbeat() {
        shouldSendHeartbeat.set(true)
        heartbeatThread = Thread {
            while (shouldSendHeartbeat.get() && isConnected.get()) {
                try {
                    Thread.sleep(HEARTBEAT_INTERVAL)
                    if (shouldSendHeartbeat.get() && isConnected.get()) {
                        val heartbeat = ControlMessage.createHeartbeat(connectionId)
                        sendMessageInternal(heartbeat)
                    }
                } catch (_: InterruptedException) {
                    break
                } catch (e: Exception) {
                    AppLog.e("发送心跳失败", e)
                }
            }
        }.apply {
            name = "WebSocketHeartbeat-$connectionId"
            start()
        }
    }

    /**
     * 停止心跳
     */
    private fun stopHeartbeat() {
        shouldSendHeartbeat.set(false)
        ResourceManager.safeStopThread(heartbeatThread, "WebSocket心跳线程", 3000)
        heartbeatThread = null
    }

    /**
     * 断开连接
     */
    fun disconnect() {
        try {
            // 停止心跳
            shouldSendHeartbeat.set(false)
            stopHeartbeat()

            // 安全关闭WebSocket连接
            safeExecute("关闭WebSocket连接") {
                if (isOpen) {
                    // 不在这里发送断开连接消息，应该由调用方决定是否发送
                    // 避免重复发送断开连接消息
                    close()
                    AppLog.websocket("WebSocket连接已关闭")
                }
            }

            // 重置连接状态
            isConnected.set(false)
            AppLog.websocket("WebSocket客户端已断开连接")
        } catch (e: Exception) {
            AppLog.e("断开WebSocket连接时发生异常", e)
        }
    }
}
