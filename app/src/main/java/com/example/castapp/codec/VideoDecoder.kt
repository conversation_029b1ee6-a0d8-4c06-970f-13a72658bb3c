package com.example.castapp.codec

import android.media.MediaCodec
import android.media.MediaCodecInfo
import android.media.MediaCodecList
import android.media.MediaFormat
import android.os.Handler
import android.os.HandlerThread
import android.view.Surface
import com.example.castapp.utils.ResourceManager
import com.example.castapp.utils.ResourceManager.safeExecute
import java.nio.ByteBuffer
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.CountDownLatch
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong
import com.example.castapp.utils.AppLog

/**
 * 🚀 MediaCodec错误类型枚举
 */
enum class MediaCodecErrorType {
    PROCESS_KILLED,      // 进程被强制结束
    RESOURCE_EXHAUSTED,  // 系统资源耗尽
    CODEC_INTERNAL,      // 编解码器内部错误
    UNKNOWN             // 未知错误
}

/**
 * 🚀 MediaCodec错误信息数据类
 */
data class MediaCodecErrorInfo(
    val errorCode: String,
    val errorType: MediaCodecErrorType,
    val description: String,
    val isRecoverable: Boolean,
    val recommendedAction: String
)

/**
 * H.264视频解码器
 */
class VideoDecoder(
    private val outputSurface: Surface
) {
    companion object {
        private const val MIME_TYPE = MediaFormat.MIMETYPE_VIDEO_AVC
    }

    private var mediaCodec: MediaCodec? = null
    private val isRunning = AtomicBoolean(false)
    private val inputQueue = ConcurrentLinkedQueue<com.example.castapp.rtp.PayloadView>()
    private val isConfigured = AtomicBoolean(false)
    private var spsData: ByteArray? = null
    private var ppsData: ByteArray? = null

    // 🚀 增强状态同步机制
    private val resolutionUpdateLock = Any()
    private val codecStateLock = Any()
    private var codecInstanceId = AtomicLong(0)
    private val isUpdatingResolution = AtomicBoolean(false)
    private val isReleasingOldResources = AtomicBoolean(false)

    // 🚀 修复重复配置：添加配置进行中标志
    private val isConfiguring = AtomicBoolean(false)

    // 🚀 异步回调专用线程和Handler
    private val callbackThread = HandlerThread("VideoDecoder-AsyncCallback").apply {
        start()
    }
    private val callbackHandler = Handler(callbackThread.looper)

    // 🚀 SIGSEGV保护：进程结束时的最后清理
    private val shutdownHook = Thread {
        try {
            AppLog.video("进程结束，执行VideoDecoder紧急清理...")
            emergencyCleanup()
        } catch (e: Exception) {
            // 在shutdown hook中不能使用AppLog，因为可能已经被清理
            System.err.println("VideoDecoder紧急清理失败: ${e.message}")
        }
    }

    init {
        // 注册shutdown hook，确保进程结束时清理资源
        try {
            Runtime.getRuntime().addShutdownHook(shutdownHook)
        } catch (e: Exception) {
            AppLog.w("注册shutdown hook失败", e)
        }
    }

    // 🚀 跟踪可用的输入缓冲区
    private val availableInputBuffers = mutableListOf<Int>()
    private val inputBufferLock = Any()

    /**
     * MediaCodec异步回调处理器
     * 🚀 异步模式：系统驱动的高效解码处理
     */
    private inner class MediaCodecCallback : MediaCodec.Callback() {

        override fun onInputBufferAvailable(codec: MediaCodec, index: Int) {
            // 🚀 增强状态检查：确保解码器仍然有效且未在更新分辨率
            if (!isRunning.get() || isUpdatingResolution.get()) return

            // 🚀 实例验证：确保回调对应的是当前有效的MediaCodec实例
            if (!isValidCodecInstance(codec)) return

            try {
                // 🚀 CPU优化：移除高频详细日志，只保留错误处理

                // 尝试处理数据
                if (!processInputData(codec, index)) {
                    // 如果没有数据，将缓冲区索引保存起来
                    synchronized(inputBufferLock) {
                        // 🚀 再次检查状态，防止在同步块中状态发生变化
                        if (isRunning.get() && !isUpdatingResolution.get()) {
                            availableInputBuffers.add(index)
                        }
                        // 🚀 CPU优化：移除缓存日志，减少CPU占用
                    }
                }
            } catch (e: Exception) {
                if (isRunning.get() && !isUpdatingResolution.get()) {
                    AppLog.e("异步输入缓冲区处理出错", e)
                }
            }
        }

        /**
         * 处理输入数据
         */
        private fun processInputData(codec: MediaCodec, index: Int): Boolean {
            // 🚀 状态检查：确保在处理数据时状态仍然有效
            if (!isRunning.get() || isUpdatingResolution.get() || !isValidCodecInstance(codec)) {
                return false
            }

            val payloadView = inputQueue.poll()

            if (payloadView != null) {
                try {
                    // 🚀 CPU优化：移除高频输入数据处理日志
                    val inputBuffer = codec.getInputBuffer(index)
                    inputBuffer?.let { buffer ->
                        // 🚀 零拷贝优化：直接从PayloadView写入，无需ByteArray转换
                        optimizedVideoInputBufferWriteFromPayloadView(buffer, payloadView)

                        val presentationTimeUs = System.nanoTime() / 1000
                        codec.queueInputBuffer(
                            index,
                            0,
                            payloadView.size(),
                            presentationTimeUs,
                            0
                        )
                        // 🚀 CPU优化：移除高频提交确认日志
                    }
                    return true
                } catch (e: IllegalStateException) {
                    // 🚀 捕获MediaCodec状态异常，避免崩溃
                    if (isRunning.get() && !isUpdatingResolution.get()) {
                        AppLog.w("MediaCodec状态异常，跳过此次输入处理: ${e.message}")

                        // 🚀 检查是否是进程强制结束导致的异常
                        if (isProcessBeingKilled()) {
                            AppLog.w("检测到进程强制结束，停止输入处理")
                            handleProcessKilledError()
                        }
                    }
                    return false
                } catch (e: MediaCodec.CodecException) {
                    // 🚀 捕获MediaCodec编解码器异常
                    if (isRunning.get() && !isUpdatingResolution.get()) {
                        val errorInfo = analyzeMediaCodecError(e)
                        AppLog.w("MediaCodec编解码器异常: ${errorInfo.errorCode} - ${errorInfo.description}")

                        // 根据错误类型处理
                        when (errorInfo.errorType) {
                            MediaCodecErrorType.PROCESS_KILLED -> handleProcessKilledError()
                            else -> AppLog.w("输入处理中的编解码器异常，跳过此次处理")
                        }
                    }
                    return false
                }
            } else {
                // 🚀 减少日志输出，避免日志洪水
                return false
            }
        }

        override fun onOutputBufferAvailable(codec: MediaCodec, index: Int, info: MediaCodec.BufferInfo) {
            // 🚀 增强状态检查：确保解码器仍然有效且未在更新分辨率
            if (!isRunning.get() || isUpdatingResolution.get()) return

            // 🚀 实例验证：确保回调对应的是当前有效的MediaCodec实例
            if (!isValidCodecInstance(codec)) return

            try {
                // 🚀 CPU优化：移除高频输出缓冲区日志

                // 🚀 CPU优化：使用缓存时间戳减少系统调用
                val currentTime = getCachedCurrentTime()

                // 使用bufferInfo中的presentationTimeUs来计算实际延迟
                val inputTime = info.presentationTimeUs * 1000 // 转换为纳秒
                val actualDecodeDelay = (currentTime - inputTime) / 1_000_000

                if (actualDecodeDelay in 1..999) { // 过滤异常值
                    totalDecodeDelay += actualDecodeDelay
                    decodeCount++

                    // 🚀 CPU优化：减少监控频率到每1800帧（30秒@60fps），进一步降低CPU开销
                    if (decodeCount % 1800 == 0L) {
                        val avgDecodeDelay = totalDecodeDelay / decodeCount
                        // 只在严重延迟时警告
                        if (avgDecodeDelay > 20) {
                            AppLog.w("解码延迟过高: ${avgDecodeDelay}ms")
                        }
                    }

                    // 只在极端延迟时警告
                    if (actualDecodeDelay > 30) {
                        AppLog.w("单帧解码延迟过高: ${actualDecodeDelay}ms")
                    }
                }

                lastDecodeTime = currentTime

                // 🚀 CPU优化：减少渲染时间戳计算，复用缓存时间
                val presentationTimeUs = currentTime / 1000 // 转换为微秒

                // 🚀 强制结束进程保护：在释放缓冲区前进行最终状态检查
                try {
                    // 再次验证MediaCodec实例有效性，防止强制结束进程时的状态异常
                    if (!isMediaCodecValid(codec)) {
                        AppLog.w("MediaCodec状态无效，跳过输出缓冲区释放")
                        return
                    }
                    codec.releaseOutputBuffer(index, presentationTimeUs)
                } catch (e: IllegalStateException) {
                    // 🚀 捕获强制结束进程时的状态异常
                    AppLog.w("MediaCodec状态异常，无法释放输出缓冲区: ${e.message}")

                    // 🚀 检查是否是进程强制结束导致的异常
                    if (isProcessBeingKilled()) {
                        AppLog.w("检测到进程强制结束，停止输出缓冲区处理")
                        handleProcessKilledError()
                    }
                    return
                } catch (e: MediaCodec.CodecException) {
                    // 🚀 捕获MediaCodec内部错误
                    val errorInfo = analyzeMediaCodecError(e)
                    AppLog.w("MediaCodec内部错误，无法释放输出缓冲区: ${errorInfo.errorCode} - ${errorInfo.description}")

                    // 根据错误类型处理
                    when (errorInfo.errorType) {
                        MediaCodecErrorType.PROCESS_KILLED -> {
                            AppLog.w("输出缓冲区处理中检测到进程强制结束")
                            handleProcessKilledError()
                        }
                        MediaCodecErrorType.RESOURCE_EXHAUSTED -> {
                            AppLog.w("输出缓冲区处理中检测到资源耗尽")
                            // 对于输出缓冲区的资源问题，通常只需要跳过当前帧
                        }
                        else -> {
                            AppLog.w("输出缓冲区处理中的其他编解码器错误")
                        }
                    }
                    return
                }
                // 🚀 CPU优化：简化渲染时间计算，避免额外的系统调用
                // 移除未使用的renderTime变量，直接进行性能统计

                // 🚀 CPU优化：移除每帧渲染日志

                // 🚀 VSync绕过效果监控 - 移除永远为false的条件检查和未使用的变量
                // 🚀 CPU优化：移除超快渲染的详细日志

                // 每30秒输出一次详细的性能统计
                val currentTimeMs = System.currentTimeMillis()
                if (currentTimeMs - lastPerformanceLogTime > 30000) {
                    val avgDecodeDelay = if (decodeCount > 0) totalDecodeDelay / decodeCount else 0

                    // 只在有问题时输出统计信息
                    if (avgDecodeDelay > 10 || droppedFrames > 0) {
                        AppLog.w("异步解码性能: 平均延迟: ${avgDecodeDelay}ms, 丢帧: $droppedFrames")
                    }

                    lastPerformanceLogTime = currentTimeMs
                }

            } catch (e: Exception) {
                if (isRunning.get()) {
                    AppLog.e("异步输出缓冲区处理出错", e)
                }
            }
        }

        override fun onError(codec: MediaCodec, e: MediaCodec.CodecException) {
            // 🚀 强制结束进程保护：增强错误处理和状态管理
            val errorInfo = analyzeMediaCodecError(e)

            AppLog.e("MediaCodec异步解码出错: ${errorInfo.errorCode} - ${errorInfo.description}", e)
            AppLog.e("错误详情: 类型=${errorInfo.errorType}, 是否可恢复=${errorInfo.isRecoverable}, 建议操作=${errorInfo.recommendedAction}")

            // 根据错误类型采取不同的处理策略
            when (errorInfo.errorType) {
                MediaCodecErrorType.PROCESS_KILLED -> {
                    AppLog.w("检测到进程强制结束错误，执行紧急清理")
                    handleProcessKilledError()
                }
                MediaCodecErrorType.RESOURCE_EXHAUSTED -> {
                    AppLog.w("检测到资源耗尽错误，尝试优雅恢复")
                    handleResourceExhaustedError()
                }
                MediaCodecErrorType.CODEC_INTERNAL -> {
                    AppLog.w("检测到编解码器内部错误，执行安全停止")
                    handleCodecInternalError()
                }
                MediaCodecErrorType.UNKNOWN -> {
                    AppLog.w("检测到未知错误，执行默认处理")
                    handleUnknownError()
                }
            }
        }

        /**
         * 🚀 分析MediaCodec错误类型和严重程度
         */
        private fun analyzeMediaCodecError(e: MediaCodec.CodecException): MediaCodecErrorInfo {
            val errorCode = "Error 0x${Integer.toHexString(e.errorCode).uppercase()}"

            return when (e.errorCode) {
                MediaCodec.CodecException.ERROR_INSUFFICIENT_RESOURCE -> {
                    MediaCodecErrorInfo(
                        errorCode = errorCode,
                        errorType = MediaCodecErrorType.RESOURCE_EXHAUSTED,
                        description = "系统资源不足",
                        isRecoverable = true,
                        recommendedAction = "等待资源释放后重试"
                    )
                }
                0x80000000.toInt() -> { // 你遇到的具体错误码
                    MediaCodecErrorInfo(
                        errorCode = errorCode,
                        errorType = MediaCodecErrorType.PROCESS_KILLED,
                        description = "进程被强制结束或MediaCodec状态异常",
                        isRecoverable = false,
                        recommendedAction = "立即停止所有操作并清理资源"
                    )
                }
                MediaCodec.CodecException.ERROR_RECLAIMED -> {
                    MediaCodecErrorInfo(
                        errorCode = errorCode,
                        errorType = MediaCodecErrorType.RESOURCE_EXHAUSTED,
                        description = "MediaCodec资源被系统回收",
                        isRecoverable = true,
                        recommendedAction = "重新创建MediaCodec实例"
                    )
                }
                else -> {
                    MediaCodecErrorInfo(
                        errorCode = errorCode,
                        errorType = MediaCodecErrorType.CODEC_INTERNAL,
                        description = "编解码器内部错误: ${e.message}",
                        isRecoverable = false,
                        recommendedAction = "停止当前解码器并尝试重新初始化"
                    )
                }
            }
        }

        override fun onOutputFormatChanged(codec: MediaCodec, format: MediaFormat) {
            AppLog.video("异步解码器输出格式改变: $format")
        }
    }

    /**
     * 🚀 关键方法：尝试处理待处理的数据
     * 当新数据加入队列时，检查是否有可用的输入缓冲区并立即处理
     */
    private fun tryProcessPendingData() {
        // 🚀 增强状态检查：确保不在分辨率更新过程中
        if (!isRunning.get() || !isConfigured.get() || isUpdatingResolution.get()) return

        val codec = mediaCodec ?: return

        // 🚀 实例验证：确保MediaCodec实例仍然有效
        if (!isValidCodecInstance(codec)) return

        synchronized(inputBufferLock) {
            while (availableInputBuffers.isNotEmpty() && inputQueue.isNotEmpty()) {
                // 🚀 在循环中再次检查状态，防止状态在处理过程中发生变化
                if (!isRunning.get() || isUpdatingResolution.get()) break

                val bufferIndex = availableInputBuffers.removeAt(0)
                // 🚀 CPU优化：移除缓存缓冲区处理日志

                // 在回调线程中处理数据
                callbackHandler.post {
                    try {
                        processInputDataDirect(codec, bufferIndex)
                    } catch (e: Exception) {
                        if (isRunning.get() && !isUpdatingResolution.get()) {
                            AppLog.e("处理缓存输入缓冲区时出错", e)
                        }
                    }
                }
            }
        }
    }

    /**
     * 直接处理输入数据（用于缓存的缓冲区）
     */
    private fun processInputDataDirect(codec: MediaCodec, index: Int) {
        // 🚀 状态检查：确保在处理数据时状态仍然有效
        if (!isRunning.get() || isUpdatingResolution.get() || !isValidCodecInstance(codec)) {
            // 状态无效时，不处理数据，也不重新缓存缓冲区索引
            return
        }

        val payloadView = inputQueue.poll()

        if (payloadView != null) {
            try {
                // 🚀 CPU优化：移除缓存缓冲区数据处理日志
                val inputBuffer = codec.getInputBuffer(index)
                inputBuffer?.let { buffer ->
                    // 🚀 零拷贝优化：直接从PayloadView写入，无需ByteArray转换
                    optimizedVideoInputBufferWriteFromPayloadView(buffer, payloadView)

                    val presentationTimeUs = System.nanoTime() / 1000
                    codec.queueInputBuffer(
                        index,
                        0,
                        payloadView.size(),
                        presentationTimeUs,
                        0
                    )
                    // 🚀 CPU优化：移除缓存缓冲区数据提交日志
                }
            } catch (e: IllegalStateException) {
                // 🚀 捕获MediaCodec状态异常，避免崩溃
                if (isRunning.get() && !isUpdatingResolution.get()) {
                    AppLog.w("MediaCodec状态异常，跳过缓存数据处理: ${e.message}")
                }
            }
        } else {
            // 🚀 CPU优化：移除队列空时的重新缓存日志
            synchronized(inputBufferLock) {
                // 🚀 再次检查状态，只有在有效状态下才重新缓存
                if (isRunning.get() && !isUpdatingResolution.get()) {
                    availableInputBuffers.add(index)
                }
                // 🚀 CPU优化：移除缓冲区数量检查日志
            }
        }
    }

    /**
     * 🚀 验证MediaCodec实例是否仍然有效
     * 防止在分辨率更新过程中操作已失效的实例
     */
    private fun isValidCodecInstance(codec: MediaCodec): Boolean {
        synchronized(codecStateLock) {
            return codec == mediaCodec
        }
    }

    /**
     * 🚀 检查MediaCodec是否处于有效状态
     * 用于强制结束进程时的额外保护
     */
    private fun isMediaCodecValid(codec: MediaCodec?): Boolean {
        if (codec == null) return false

        try {
            // 🚀 增强状态检查：多重验证确保MediaCodec真正有效

            // 第一层检查：尝试获取MediaCodec的名称
            codec.name // 调用name属性进行状态检查，但不需要存储结果

            // 第二层检查：验证MediaCodec实例是否与当前实例匹配
            synchronized(codecStateLock) {
                if (codec != mediaCodec) {
                    AppLog.w("MediaCodec实例不匹配，可能是旧实例")
                    return false
                }
            }

            // 第三层检查：验证解码器运行状态
            if (!isRunning.get()) {
                AppLog.w("解码器已停止运行，MediaCodec状态无效")
                return false
            }

            // 第四层检查：检测进程强制结束的特殊情况
            if (isProcessBeingKilled()) {
                AppLog.w("检测到进程正在被强制结束，MediaCodec状态无效")
                return false
            }

            return true
        } catch (e: IllegalStateException) {
            AppLog.w("MediaCodec状态检查失败: ${e.message}")
            return false
        } catch (e: RuntimeException) {
            AppLog.w("MediaCodec运行时异常: ${e.message}")
            return false
        } catch (e: Exception) {
            AppLog.w("MediaCodec状态检查异常: ${e.message}")
            return false
        }
    }

    /**
     * 🚀 检测进程是否正在被强制结束
     * 通过多种指标判断进程状态
     */
    private fun isProcessBeingKilled(): Boolean {
        try {
            // 检查1：验证主线程是否还在正常运行
            if (Thread.currentThread().isInterrupted) {
                return true
            }

            // 检查2：验证应用上下文是否还有效
            // 注意：这里需要一个安全的方式获取应用上下文
            // 如果上下文无效，说明应用正在被销毁

            // 检查3：检查是否在更新分辨率过程中（可能是正常停止）
            if (isUpdatingResolution.get()) {
                return false // 这是正常的更新过程，不是强制结束
            }

            return false
        } catch (e: Exception) {
            // 如果检测过程中出现异常，认为进程可能正在被结束
            AppLog.w("检测进程状态时发生异常: ${e.message}")
            return true
        }
    }

    /**
     * 🚀 安全停止所有异步操作
     * 确保在分辨率更新前所有回调都已停止
     */
    private fun stopAllAsyncOperations() {
        // 设置更新标志，阻止新的异步操作
        isUpdatingResolution.set(true)

        // 清空输入缓冲区队列，防止新的处理任务
        synchronized(inputBufferLock) {
            availableInputBuffers.clear()
        }

        // 清空输入数据队列
        inputQueue.clear()

        // 🚀 强制结束进程保护：增加等待时间确保所有异步回调完全停止
        try {
            // 增加等待时间到50ms，确保在强制结束进程的情况下有足够时间让回调检查状态并退出
            Thread.sleep(50)
            AppLog.video("已等待异步操作停止")
        } catch (_: InterruptedException) {
            Thread.currentThread().interrupt()
            AppLog.w("等待异步操作停止时被中断")
        }
    }

    // 后台资源释放线程池
    private val backgroundReleaseExecutor = java.util.concurrent.Executors.newSingleThreadExecutor { r ->
        Thread(r, "VideoDecoder-BackgroundRelease").apply {
            priority = Thread.MIN_PRIORITY // 最低优先级，不影响主要功能
        }
    }

    // 选中的硬件解码器信息
    private var selectedDecoderName: String? = null
    private var selectedDecoderType: String? = null

    // 🚀 CPU优化：延迟监控 - 减少时间戳计算频率
    private var lastDecodeTime = 0L
    private var decodeCount = 0L
    private var totalDecodeDelay = 0L
    private var lastTimestampUpdate = 0L
    private var cachedCurrentTime = 0L
    private val timestampCacheIntervalNs = 16_000_000L // 16ms缓存间隔，约60fps

    // 性能监控
    private var droppedFrames = 0L
    private var lastPerformanceLogTime = 0L

    // 🚀 平衡优化：低延迟与数据流平滑性的最佳平衡
    private var currentQueueSize = 12 // 动态队列大小，初始12帧（平衡延迟和流畅性）
    private var maxQueueSize = 30 // 最大队列大小30帧（1秒@30fps，保证流畅性）
    private var minQueueSize = 6 // 最小队列大小6帧（0.2秒@30fps，保证最低缓冲）
    private var lastQueueAdjustment = 0L
    private var averageDecodeTime = 0L

    // 帧类型统计
    private var iFrameCount = 0L
    private var pFrameCount = 0L
    private var droppedIFrames = 0L

    // 🚀 零拷贝优化：输入缓冲区重用统计
    private var inputBufferReuseHits = 0L
    private var inputBufferReuseMisses = 0L

    // 事件驱动状态管理
    private val configurationLatch = CountDownLatch(1)

    // 配置状态监听器
    private val configurationListeners = mutableListOf<() -> Unit>()
    private val configurationLock = Any()



    /**
     * 通知配置完成（事件驱动）
     */
    private fun notifyConfigurationComplete() {
        synchronized(configurationLock) {
            configurationListeners.forEach { listener ->
                try {
                    listener()
                } catch (e: Exception) {
                    AppLog.w("配置完成监听器执行异常", e)
                }
            }
            configurationListeners.clear()
            configurationLatch.countDown()
        }
        AppLog.video("配置完成事件已通知")
    }

    /**
     * 检测并获取可用的硬件H.264解码器列表
     */
    private fun getAvailableHardwareDecoders(): List<MediaCodecInfo> {
        val hardwareDecoders = mutableListOf<MediaCodecInfo>()
        val codecList = MediaCodecList(MediaCodecList.ALL_CODECS)

        for (codecInfo in codecList.codecInfos) {
            if (codecInfo.isEncoder) continue

            val supportedTypes = codecInfo.supportedTypes
            if (!supportedTypes.contains(MIME_TYPE)) continue

            // 检查是否为硬件解码器
            if (isHardwareDecoder(codecInfo)) {
                hardwareDecoders.add(codecInfo)

                // 只在获取详细信息失败时输出警告
                try {
                    codecInfo.getCapabilitiesForType(MIME_TYPE)
                } catch (e: Exception) {
                    AppLog.w("无法获取解码器 ${codecInfo.name} 详细信息: ${e.message}")
                }
            }
        }

        return hardwareDecoders
    }

    /**
     * 判断解码器是否为硬件解码器
     */
    private fun isHardwareDecoder(codecInfo: MediaCodecInfo): Boolean {
        val name = codecInfo.name.lowercase()

        // 软件解码器通常包含这些关键词
        val softwareKeywords = listOf(
            "sw", "software", "google", "ffmpeg", "c2.android"
        )

        // 检查是否包含软件解码器关键词
        for (keyword in softwareKeywords) {
            if (name.contains(keyword)) {
                return false
            }
        }

        // 硬件解码器通常包含这些厂商关键词
        val hardwareKeywords = listOf(
            "qcom", "qualcomm", "qti", "mtk", "mediatek", "exynos",
            "samsung", "hisi", "kirin", "nvidia", "intel", "amd",
            "omx", "hantro", "rk", "rockchip", "amlogic"
        )

        for (keyword in hardwareKeywords) {
            if (name.contains(keyword)) {
                return true
            }
        }

        // 如果没有明确的关键词，但不在软件解码器列表中，则认为是硬件解码器
        return true
    }

    /**
     * 选择最佳的硬件解码器
     */
    private fun selectBestHardwareDecoder(hardwareDecoders: List<MediaCodecInfo>): MediaCodecInfo? {
        if (hardwareDecoders.isEmpty()) {
            return null
        }

        // 优先级排序：高通 > 联发科 > 三星 > 其他
        val priorityOrder = listOf("qcom", "qualcomm", "qti", "mtk", "mediatek", "exynos", "samsung")

        for (priority in priorityOrder) {
            val decoder = hardwareDecoders.find {
                it.name.lowercase().contains(priority)
            }
            if (decoder != null) {
                return decoder
            }
        }

        // 如果没有匹配优先级的，选择第一个
        return hardwareDecoders.first()
    }

    /**
     * 🚀 CPU优化：获取缓存的当前时间，减少系统调用
     */
    private fun getCachedCurrentTime(): Long {
        val now = System.nanoTime()
        if (now - lastTimestampUpdate > timestampCacheIntervalNs) {
            cachedCurrentTime = now
            lastTimestampUpdate = now
        }
        return cachedCurrentTime
    }

    /**
     * 启动解码器
     */
    fun start(): Boolean {
        return safeExecute("启动H.264异步硬件解码器") {
            AppLog.video("开始启动H.264异步硬件解码器...")

            // 检测可用的硬件解码器
            val hardwareDecoders = getAvailableHardwareDecoders()

            if (hardwareDecoders.isEmpty()) {
                val errorMsg = "未检测到任何H.264硬件解码器，无法启动解码器"
                AppLog.e(errorMsg)
                throw IllegalStateException(errorMsg)
            }

            // 选择最佳的硬件解码器
            val selectedDecoder = selectBestHardwareDecoder(hardwareDecoders)
                ?: throw IllegalStateException("无法选择合适的硬件解码器")

            selectedDecoderName = selectedDecoder.name
            selectedDecoderType = "硬件解码器"

            // 创建选定的硬件解码器
            val codec = MediaCodec.createByCodecName(selectedDecoder.name)

            // 🚀 设置异步回调 - 在configure之前设置，使用专用Handler
            codec.setCallback(MediaCodecCallback(), callbackHandler)
            AppLog.video("🚀 异步回调已设置，使用专用Handler线程")

            // 🚀 线程安全地设置新的MediaCodec实例
            synchronized(codecStateLock) {
                mediaCodec = codec
                codecInstanceId.incrementAndGet() // 更新实例ID
            }
            isRunning.set(true)

            AppLog.video("异步硬件解码器创建成功，等待配置数据")
            true
        } ?: false
    }

    /**
     * 🚀 停止解码器 - 增强状态同步和Native层保护
     */
    fun stop() {
        AppLog.video("开始停止VideoDecoder...")

        // 🚀 首先停止所有异步操作
        stopAllAsyncOperations()

        isRunning.set(false)
        isConfigured.set(false)
        // 🚀 修复重复配置：重置配置标志
        isConfiguring.set(false)

        // 🚀 线程安全地释放MediaCodec资源
        val codecToRelease: MediaCodec?
        synchronized(codecStateLock) {
            codecToRelease = mediaCodec
            mediaCodec = null
            codecInstanceId.incrementAndGet() // 更新实例ID，使旧回调失效
        }

        // 🚀 SIGSEGV保护：大幅增加等待时间，确保native层完全停止
        try {
            Thread.sleep(500) // 增加到500ms，确保MediaCodec native层完全停止
            AppLog.video("已等待MediaCodec native层完全停止")
        } catch (_: InterruptedException) {
            Thread.currentThread().interrupt()
            AppLog.w("等待native层停止时被中断")
        }

        // 🚀 SIGSEGV保护：在后台线程中释放MediaCodec，避免阻塞主线程
        codecToRelease?.let { codec ->
            AppLog.video("开始在后台线程释放MediaCodec资源...")

            // 使用后台线程释放，避免native层阻塞
            backgroundReleaseExecutor.execute {
                try {
                    // 🚀 再次等待，确保所有native回调完全停止
                    Thread.sleep(200)
                    AppLog.video("后台线程开始释放MediaCodec...")
                    safeReleaseDecoder(codec)
                    AppLog.video("后台线程MediaCodec资源释放完成")
                } catch (e: Exception) {
                    AppLog.e("后台释放MediaCodec时发生异常", e)
                }
            }
        }

        // 清理其他资源
        inputQueue.clear()
        synchronized(inputBufferLock) {
            availableInputBuffers.clear()
        }
        spsData = null
        ppsData = null
        selectedDecoderName = null
        selectedDecoderType = null

        // 重置性能计数器
        droppedFrames = 0L
        decodeCount = 0L
        totalDecodeDelay = 0L
        lastDecodeTime = 0L
        lastPerformanceLogTime = 0L

        // 🚀 重置所有状态标志
        isUpdatingResolution.set(false)
        isReleasingOldResources.set(false)

        AppLog.video("🚀 异步硬件解码器已安全停止，性能统计已重置")
    }

    /**
     * 完全清理解码器资源，包括线程池 - 增强Native层保护
     * 在解码器不再使用时调用，确保没有内存泄露和SIGSEGV
     */
    fun cleanup() {
        AppLog.video("开始清理VideoDecoder资源...")

        // 🚀 SIGSEGV保护：设置清理标志，阻止新的操作
        isRunning.set(false)
        isConfigured.set(false)
        isUpdatingResolution.set(true) // 阻止所有异步操作

        // 🚀 SIGSEGV保护：大幅增加等待时间，确保所有native操作完全停止
        try {
            Thread.sleep(1000) // 等待1秒，确保所有native操作完全停止
            AppLog.video("已等待所有native操作停止")
        } catch (_: InterruptedException) {
            Thread.currentThread().interrupt()
        }

        // 先停止解码器
        stop()

        // 🚀 SIGSEGV保护：再次等待，确保stop操作完全完成
        try {
            Thread.sleep(500) // 额外等待500ms
            AppLog.video("stop操作已完全完成")
        } catch (_: InterruptedException) {
            Thread.currentThread().interrupt()
        }

        // 关闭后台资源释放线程池
        try {
            backgroundReleaseExecutor.shutdown()
            if (!backgroundReleaseExecutor.awaitTermination(5000, java.util.concurrent.TimeUnit.MILLISECONDS)) {
                AppLog.w("后台线程池未在5秒内关闭，强制关闭")
                backgroundReleaseExecutor.shutdownNow()
                if (!backgroundReleaseExecutor.awaitTermination(2000, java.util.concurrent.TimeUnit.MILLISECONDS)) {
                    AppLog.e("后台线程池强制关闭失败")
                }
            }
            AppLog.video("后台资源释放线程池已关闭")
        } catch (e: InterruptedException) {
            AppLog.w("等待线程池关闭时被中断", e)
            backgroundReleaseExecutor.shutdownNow()
            Thread.currentThread().interrupt()
        } catch (e: Exception) {
            AppLog.e("关闭后台线程池时发生异常", e)
        }

        // 🚀 清理异步回调线程 - 增强等待时间
        try {
            callbackThread.quitSafely()
            callbackThread.join(3000) // 增加到3秒等待时间
            AppLog.video("异步回调线程已关闭")
        } catch (e: Exception) {
            AppLog.e("关闭异步回调线程时发生异常", e)
        }

        // 🚀 SIGSEGV保护：最终等待，确保所有资源完全清理
        try {
            Thread.sleep(200)
            AppLog.video("所有资源清理等待完成")
        } catch (_: InterruptedException) {
            Thread.currentThread().interrupt()
        }

        // 🚀 移除shutdown hook，因为已经正常清理了
        try {
            Runtime.getRuntime().removeShutdownHook(shutdownHook)
            AppLog.video("已移除shutdown hook")
        } catch (e: Exception) {
            AppLog.w("移除shutdown hook失败", e)
        }

        AppLog.video("VideoDecoder资源清理完成")
    }

    /**
     * 🚀 紧急清理方法 - 用于进程强制结束时的最后保护
     */
    private fun emergencyCleanup() {
        try {
            // 立即设置所有状态为停止
            isRunning.set(false)
            isConfigured.set(false)
            isUpdatingResolution.set(true)

            // 获取MediaCodec引用并立即清空
            val codecToRelease: MediaCodec?
            synchronized(codecStateLock) {
                codecToRelease = mediaCodec
                mediaCodec = null
            }

            // 尝试立即停止MediaCodec，不等待
            codecToRelease?.let { codec ->
                try {
                    codec.stop()
                } catch (_: Exception) {
                    // 忽略异常，继续清理
                }
                try {
                    codec.release()
                } catch (_: Exception) {
                    // 忽略异常
                }
            }

            // 清理队列
            inputQueue.clear()
            synchronized(inputBufferLock) {
                availableInputBuffers.clear()
            }

            // 尝试移除shutdown hook
            try {
                Runtime.getRuntime().removeShutdownHook(shutdownHook)
            } catch (_: Exception) {
                // 忽略异常
            }

        } catch (_: Exception) {
            // 紧急清理中忽略所有异常
        }
    }

    /**
     * 安全释放MediaCodec解码器资源
     */
    private fun safeReleaseDecoder(codec: MediaCodec) {
        ResourceManager.safeReleaseMediaCodec(codec, "视频解码器")
    }



    /**
     * 🚀 零拷贝解码：统一入口，移除向后兼容
     * 只支持PayloadView输入，性能最优
     */
    fun decode(payloadView: com.example.castapp.rtp.PayloadView) {
        // 🚀 增强状态检查：确保不在分辨率更新过程中
        if (isRunning.get() && !isUpdatingResolution.get() && payloadView.isNotEmpty()) {
            // 🚀 零拷贝优化：只在必要时转换为ByteArray（配置数据检查）
            val h264Data = payloadView.toByteArray()

            if (isConfigurationData(h264Data)) {
                handleConfigurationData(h264Data)
                return
            }

            // 🚀 普通帧数据处理：直接使用PayloadView，避免重复转换
            processFrameDataZeroCopy(payloadView, h264Data)
        }
    }

    /**
     * 🚀 零拷贝帧数据处理：统一处理逻辑，避免代码重复
     */
    private fun processFrameDataZeroCopy(payloadView: com.example.castapp.rtp.PayloadView, h264Data: ByteArray) {
        // 帧类型分析和统计
        val frameType = getFrameType(h264Data)
        updateFrameStatistics(frameType)

        // 动态调整队列大小
        adjustQueueSizeBasedOnPerformance()

        // 智能丢帧策略
        if (inputQueue.size >= currentQueueSize) {
            val droppedData = smartFrameDrop()
            if (droppedData != null) {
                droppedFrames++
                val droppedFrameType = getFrameType(droppedData)
                if (droppedFrameType == FrameType.I_FRAME) {
                    droppedIFrames++
                    AppLog.w("警告：丢弃了I帧，可能影响解码质量 (总丢弃I帧: $droppedIFrames)")
                }

                if (droppedFrames % 30 == 0L) {
                    AppLog.w("已丢弃 $droppedFrames 帧以保持实时性 (队列大小: $currentQueueSize)")
                }
            }
        }

        // 🚀 零拷贝核心：直接将PayloadView加入队列
        inputQueue.offer(payloadView)
        // 🚀 CPU优化：移除高频入队日志，减少CPU占用

        // 立即处理待处理数据
        tryProcessPendingData()
    }

    /**
     * 🚀 动态更新视频分辨率并重新配置解码器
     * 采用"安全停止 + 创建新资源 + 后台释放旧资源"策略，确保状态同步
     */
    fun updateResolution(newWidth: Int, newHeight: Int) {
        synchronized(resolutionUpdateLock) {
            if (!isRunning.get()) {
                AppLog.w("解码器未运行，无法更新分辨率")
                return
            }

            AppLog.video("🚀 开始安全更新解码器分辨率: ${newWidth}x${newHeight}")

            try {
                // 🚀 第一步：安全停止所有异步操作
                AppLog.video("🚀 步骤1：停止所有异步操作")
                stopAllAsyncOperations()

                // 保存当前资源引用
                val oldMediaCodec: MediaCodec?

                // 🚀 第二步：线程安全地保存旧资源引用并清空当前引用
                synchronized(codecStateLock) {
                    oldMediaCodec = mediaCodec
                    mediaCodec = null
                }

                // 设置停止标志
                isRunning.set(false)
                isConfigured.set(false)

                AppLog.video("🚀 步骤2：已安全保存旧资源引用并清空当前状态")

                // 🚀 第三步：立即创建新的解码器（不等待旧资源释放）
                AppLog.video("🚀 步骤3：创建新解码器")
                if (start()) {
                    AppLog.video("🚀 新异步解码器创建成功，开始后台释放旧资源")

                    // 🚀 第四步：重置更新标志，允许新的异步操作
                    isUpdatingResolution.set(false)

                    // 🚀 设置资源释放标志并在后台异步释放旧资源
                    isReleasingOldResources.set(true)
                    backgroundReleaseExecutor.execute {
                        releaseOldResourcesInBackground(oldMediaCodec)
                    }

                    // 🚀 优化策略：不恢复旧的SPS/PPS数据，完全依赖WebSocket发送新的配置数据
                    // 这样可以确保配置数据与实际分辨率完全匹配
                    AppLog.video("🚀 解码器已重新创建，等待WebSocket发送新的H.264配置数据")

                    AppLog.video("🚀 异步解码器分辨率安全更新成功: ${newWidth}x${newHeight}")
                } else {
                    AppLog.e("🚀 创建新异步解码器失败，执行安全回滚")
                    // 🚀 安全回滚：恢复旧资源引用
                    synchronized(codecStateLock) {
                        mediaCodec = oldMediaCodec
                    }
                    isRunning.set(true)
                    isConfigured.set(false) // 重置为未配置状态，等待新的配置数据
                    isUpdatingResolution.set(false) // 重置更新标志
                }

            } catch (e: Exception) {
                AppLog.e("🚀 安全更新解码器分辨率失败", e)
                // 🚀 异常情况下也要重置更新标志
                isUpdatingResolution.set(false)
            }
        }
    }

    /**
     * 在后台释放旧资源
     */
    private fun releaseOldResourcesInBackground(oldCodec: MediaCodec?) {
        try {
            AppLog.video("开始后台释放旧异步解码器资源...")

            // 🚀 异步模式：无需等待线程结束，系统自动管理回调停止
            // 直接释放旧的MediaCodec
            oldCodec?.let { codec ->
                safeReleaseDecoder(codec)
            }

            AppLog.video("旧异步解码器资源后台释放完成")

        } catch (e: Exception) {
            AppLog.e("后台释放旧异步资源时发生异常", e)
        } finally {
            // 🚀 重置资源释放标志，允许新的配置操作
            isReleasingOldResources.set(false)
            AppLog.video("🚀 资源释放标志已重置，允许新的配置操作")
        }
    }

    /**
     * 验证分辨率是否适合解码
     */
    private fun validateResolutionForDecoding(width: Int, height: Int) {
        AppLog.video("验证解码分辨率: ${width}x${height}")

        // 检查基本的分辨率要求
        if (width <= 0 || height <= 0) {
            throw IllegalArgumentException("分辨率必须为正数: ${width}x${height}")
        }

        // 检查H.264编码要求（偶数分辨率）
        if (width % 2 != 0 || height % 2 != 0) {
            AppLog.w("警告：分辨率不是偶数，可能导致解码问题: ${width}x${height}")
        }

        // 检查是否过小（可能导致解码器问题）
        if (width < 32 || height < 32) {
            AppLog.w("警告：分辨率过小，可能导致硬件解码器不支持: ${width}x${height}")
            AppLog.w("  建议最小分辨率: 32x32")
        }

        // 检查宽高比是否合理
        val aspectRatio = width.toDouble() / height
        if (aspectRatio < 0.1 || aspectRatio > 10.0) {
            AppLog.w("警告：宽高比异常，可能导致解码问题: ${String.format(java.util.Locale.US, "%.3f", aspectRatio)}")
        }

        AppLog.video("分辨率验证完成: ${width}x${height}")
    }

    /**
     * 检查是否是配置数据（SPS/PPS）
     */
    private fun isConfigurationData(data: ByteArray): Boolean {
        if (data.size < 3) return false

        // 查找NAL单元类型
        val startIndex = when {
            data.size >= 4 && data[0] == 0x00.toByte() && data[1] == 0x00.toByte() &&
                    data[2] == 0x00.toByte() && data[3] == 0x01.toByte() -> 4
            data[0] == 0x00.toByte() && data[1] == 0x00.toByte() &&
                    data[2] == 0x01.toByte() -> 3
            else -> return false
        }

        if (startIndex < data.size) {
            val nalType = data[startIndex].toInt() and 0x1F
            return nalType == 7 || nalType == 8 // SPS=7, PPS=8
        }

        return false
    }

    /**
     * 处理配置数据
     */
    private fun handleConfigurationData(data: ByteArray) {
        when (getNalType(data)) {
            7 -> { // SPS
                spsData = data.clone()
                AppLog.video("收到SPS数据: ${data.size} bytes")
            }
            8 -> { // PPS
                ppsData = data.clone()
                AppLog.video("收到PPS数据: ${data.size} bytes")
            }
        }

        // 🚀 增强配置条件：确保不在资源释放期间配置，避免重复配置
        if (spsData != null && ppsData != null && !isConfigured.get() && !isConfiguring.get() &&
            !isUpdatingResolution.get() && !isReleasingOldResources.get()) {
            configureDecoder()
        } else if (spsData != null && ppsData != null && !isConfigured.get() && !isConfiguring.get()) {
            // 如果正在释放资源，延迟配置
            AppLog.video("🚀 正在释放旧资源，延迟配置解码器")
            scheduleDelayedConfiguration()
        } else if (isConfiguring.get()) {
            AppLog.video("🚀 解码器配置已在进行中，跳过重复配置")
        }
    }

    /**
     * 🚀 延迟配置解码器
     * 在旧资源释放完成后再配置
     */
    private fun scheduleDelayedConfiguration() {
        callbackHandler.postDelayed({
            // 检查是否仍需要配置且资源释放已完成，避免重复配置
            if (spsData != null && ppsData != null && !isConfigured.get() && !isConfiguring.get() &&
                !isUpdatingResolution.get() && !isReleasingOldResources.get()) {
                AppLog.video("🚀 延迟配置解码器执行")
                configureDecoder()
            } else if (isReleasingOldResources.get()) {
                // 如果仍在释放资源，继续延迟
                AppLog.video("🚀 仍在释放资源，继续延迟配置")
                scheduleDelayedConfiguration()
            } else if (isConfiguring.get()) {
                AppLog.video("🚀 解码器配置已在进行中，取消延迟配置")
            }
        }, 50) // 50ms延迟
    }

    /**
     * 获取NAL单元类型
     */
    private fun getNalType(data: ByteArray): Int {
        val startIndex = when {
            data.size >= 4 && data[0] == 0x00.toByte() && data[1] == 0x00.toByte() &&
                    data[2] == 0x00.toByte() && data[3] == 0x01.toByte() -> 4
            data.size >= 3 && data[0] == 0x00.toByte() && data[1] == 0x00.toByte() &&
                    data[2] == 0x01.toByte() -> 3
            else -> 0
        }

        return if (startIndex < data.size) {
            data[startIndex].toInt() and 0x1F
        } else {
            -1
        }
    }

    /**
     * 配置解码器
     */
    private fun configureDecoder() {
        safeExecute("配置解码器") {
            val sps = spsData ?: return@safeExecute
            val pps = ppsData ?: return@safeExecute

            // 解析SPS获取视频尺寸
            val (width, height) = parseSpsForDimensions(sps)

            // 验证分辨率是否合理
            validateResolutionForDecoding(width, height)

            AppLog.video("配置解码器: ${width}x${height}")

            val format = MediaFormat.createVideoFormat(MIME_TYPE, width, height).apply {
                setByteBuffer("csd-0", ByteBuffer.wrap(sps))
                setByteBuffer("csd-1", ByteBuffer.wrap(pps))

                safeExecute("设置解码器参数") {
                    // KEY_LOW_LATENCY 需要 API 30+
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
                        setInteger(MediaFormat.KEY_LOW_LATENCY, 1)
                    }
                    setInteger(MediaFormat.KEY_PRIORITY, 0)
                    setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, 2 * 1024 * 1024)
                    setInteger("max-bframes", 0)
                    setInteger("max-reorder-depth", 0)
                    setInteger("operating-rate", 30)
                    setInteger("latency", 0)

                    // 修复slice-height参数，确保最小值为16（H.264宏块大小）
                    val sliceHeight = maxOf(height / 8, 16)
                    setInteger("slice-height", sliceHeight)
                    AppLog.video("设置slice-height: $sliceHeight (原始高度: $height)")
                }
            }

            // 🚀 修复音频卡顿：异步配置解码器，避免阻塞音频播放
            // 🚀 修复重复配置：检查是否已经在配置中
            if (!isConfiguring.compareAndSet(false, true)) {
                AppLog.video("解码器配置已在进行中，跳过重复配置")
                return@safeExecute
            }

            Thread {
                try {
                    // 🚀 再次检查状态，确保MediaCodec仍然有效且未配置
                    if (!isRunning.get() || isConfigured.get()) {
                        AppLog.video("解码器状态已变化，取消配置")
                        return@Thread
                    }

                    mediaCodec?.apply {
                        configure(format, outputSurface, null, 0)
                        // 🚀 启动异步解码器 - 系统将自动调用回调
                        start()
                    }

                    isConfigured.set(true)
                    AppLog.video("异步解码器配置成功，避免音频卡顿: ${width}x${height}")

                    // 通知配置完成事件
                    notifyConfigurationComplete()
                } catch (e: Exception) {
                    AppLog.e("异步配置解码器失败", e)
                    isConfigured.set(false)
                } finally {
                    // 🚀 重置配置标志
                    isConfiguring.set(false)
                }
            }.apply {
                name = "VideoDecoder-AsyncConfig"
                priority = Thread.NORM_PRIORITY // 普通优先级，不影响音频
                start()
            }
        }
    }

    /**
     * 从SPS中解析视频尺寸
     * 🚀 简化版：优先使用WebSocket分辨率，无需SPS解析验证
     */
    private fun parseSpsForDimensions(sps: ByteArray): Pair<Int, Int> {
        // 🚀 优先使用WebSocket传递的分辨率信息
        if (webSocketWidth != null && webSocketHeight != null) {
            val webSocketResolution = Pair(webSocketWidth!!, webSocketHeight!!)
            AppLog.video("🚀 使用WebSocket分辨率: ${webSocketResolution.first}x${webSocketResolution.second}")
            return webSocketResolution
        }

        // 🚀 回退到SPS解析（仅在WebSocket分辨率不可用时）
        return try {
            val result = parseH264SPS(sps)
            AppLog.video("从SPS解析得到分辨率: ${result.first}x${result.second}")
            result
        } catch (e: Exception) {
            AppLog.w("SPS解析失败，使用默认分辨率: ${e.message}")
            // 回退到默认分辨率
            Pair(1080, 2400)
        }
    }

    /**
     * 解析H.264 SPS (Sequence Parameter Set) 获取视频分辨率
     */
    private fun parseH264SPS(sps: ByteArray): Pair<Int, Int> {
        // 检查最小SPS长度
        if (sps.size < 3) {
            throw IllegalArgumentException("SPS数据太短")
        }

        // 查找SPS NAL单元的开始
        var startIndex = 0
        when {
            sps.size >= 4 && sps[0] == 0x00.toByte() && sps[1] == 0x00.toByte() &&
            sps[2] == 0x00.toByte() && sps[3] == 0x01.toByte() -> {
                startIndex = 4
            }
            sps[0] == 0x00.toByte() && sps[1] == 0x00.toByte() && sps[2] == 0x01.toByte() -> {
                startIndex = 3
            }
        }

        // 确保有足够的数据来读取NAL类型
        if (startIndex >= sps.size) {
            throw IllegalArgumentException("无效的SPS格式：起始码后没有数据")
        }

        // 检查NAL单元类型是否为SPS (7)
        val nalType = sps[startIndex].toInt() and 0x1F
        if (nalType != 7) {
            throw IllegalArgumentException("不是SPS NAL单元，类型: $nalType")
        }

        // 简化的SPS解析 - 跳过复杂的字段，直接查找分辨率信息
        // 这是一个基本实现，可能不适用于所有SPS格式
        try {
            // 使用MediaFormat来解析SPS
            val format = MediaFormat.createVideoFormat(MIME_TYPE, 1920, 1080) // 临时格式
            format.setByteBuffer("csd-0", ByteBuffer.wrap(sps))

            // 尝试从格式中获取分辨率信息
            // 注意：这种方法可能不总是有效，因为MediaFormat主要用于配置而不是解析

            // 回退到启发式方法：基于SPS数据的常见模式
            return parseSpsDimensionsHeuristic(sps, startIndex)

        } catch (e: Exception) {
            AppLog.w("MediaFormat解析SPS失败，使用启发式方法: ${e.message}")
            return parseSpsDimensionsHeuristic(sps, startIndex)
        }
    }

    /**
     * 启发式SPS分辨率解析
     * 基于常见的H.264编码器输出模式
     */
    private fun parseSpsDimensionsHeuristic(sps: ByteArray, startIndex: Int): Pair<Int, Int> {
        // 这是一个简化的启发式方法
        // 在实际应用中，应该实现完整的SPS解析

        // 常见分辨率列表，按使用频率排序
        val commonResolutions = listOf(
            Pair(1080, 2400), // 原始分辨率
            Pair(1080, 1920), // 标准1080p
            Pair(720, 1280),  // 720p
            Pair(540, 1200),  // 50%缩放
            Pair(604, 1344),  // 56%缩放
            Pair(810, 1800),  // 75%缩放
            Pair(270, 600),   // 25%缩放
            Pair(432, 960),   // 40%缩放
            Pair(648, 1440),  // 60%缩放
            Pair(864, 1920),  // 80%缩放
        )

        // 基于SPS数据长度和内容的启发式判断
        val spsLength = sps.size
        val spsContent = sps.sliceArray(startIndex until minOf(startIndex + 20, sps.size))

        // 根据SPS特征选择最可能的分辨率
        // 这是一个简化的实现，实际应该解析SPS的具体字段

        AppLog.video("SPS启发式解析: 长度=$spsLength, 内容前20字节=${spsContent.joinToString { "%02x".format(it) }}")

        // 默认返回最常见的分辨率
        // 在实际应用中，这里应该实现真正的SPS解析算法
        return commonResolutions[0] // 返回1080x2400作为默认值
    }

    // 🚀 异步模式：输入处理已移至 MediaCodecCallback.onInputBufferAvailable()

    // 🚀 异步模式：输出处理已移至 MediaCodecCallback.onOutputBufferAvailable()

    // 🚀 WebSocket分辨率信息存储
    private var webSocketWidth: Int? = null
    private var webSocketHeight: Int? = null

    /**
     * 🚀 设置WebSocket传递的分辨率信息
     * 作为SPS解析的补充验证机制
     */
    fun setWebSocketResolution(width: Int, height: Int) {
        webSocketWidth = width
        webSocketHeight = height
        AppLog.video("🚀 设置WebSocket分辨率: ${width}x${height}")
    }

    /**
     * 获取当前使用的解码器信息
     */
    fun getDecoderInfo(): Pair<String?, String?> {
        return Pair(selectedDecoderName, selectedDecoderType)
    }

    /**
     * 检查解码器是否正在使用硬件加速
     */
    fun isUsingHardwareAcceleration(): Boolean {
        return selectedDecoderName != null && selectedDecoderType == "硬件解码器"
    }

    // 帧类型枚举
    private enum class FrameType {
        I_FRAME,  // 关键帧
        P_FRAME,  // 预测帧
        UNKNOWN   // 未知类型
    }

    /**
     * 获取帧类型
     */
    private fun getFrameType(h264Data: ByteArray): FrameType {
        if (h264Data.isEmpty()) return FrameType.UNKNOWN

        // 查找NAL单元类型
        val startIndex = when {
            h264Data.size >= 4 && h264Data[0] == 0x00.toByte() && h264Data[1] == 0x00.toByte() &&
                    h264Data[2] == 0x00.toByte() && h264Data[3] == 0x01.toByte() -> 4
            h264Data.size >= 3 && h264Data[0] == 0x00.toByte() && h264Data[1] == 0x00.toByte() &&
                    h264Data[2] == 0x01.toByte() -> 3
            else -> return FrameType.UNKNOWN
        }

        if (startIndex < h264Data.size) {
            val nalType = h264Data[startIndex].toInt() and 0x1F
            return when (nalType) {
                5 -> FrameType.I_FRAME  // IDR帧
                1 -> FrameType.P_FRAME  // 简化处理，假设大部分是P帧
                else -> FrameType.UNKNOWN
            }
        }

        return FrameType.UNKNOWN
    }

    /**
     * 更新帧类型统计
     */
    private fun updateFrameStatistics(frameType: FrameType) {
        when (frameType) {
            FrameType.I_FRAME -> iFrameCount++
            FrameType.P_FRAME -> pFrameCount++
            FrameType.UNKNOWN -> { /* 不统计 */ }
        }
    }

    /**
     * 智能帧丢弃策略
     */
    private fun smartFrameDrop(): ByteArray? {
        val queueList = inputQueue.toList()
        if (queueList.isEmpty()) return null

        // 优先级：P帧 > I帧（I帧最重要，最后丢弃）

        // 首先尝试丢弃最旧的P帧
        for (i in queueList.indices) {
            val payloadView = queueList[i]
            val frameData = payloadView.toByteArray()
            val frameType = getFrameType(frameData)
            if (frameType == FrameType.P_FRAME) {
                inputQueue.remove(payloadView)
                return frameData
            }
        }

        // 如果没有P帧，最后才丢弃I帧（最不希望的情况）
        val droppedPayloadView = inputQueue.poll()
        return droppedPayloadView?.toByteArray()
    }

    /**
     * 根据性能动态调整队列大小
     */
    private fun adjustQueueSizeBasedOnPerformance() {
        val currentTime = System.currentTimeMillis()

        // 每5秒调整一次队列大小
        if (currentTime - lastQueueAdjustment < 5000) {
            return
        }

        // 计算平均解码时间
        if (decodeCount > 0) {
            averageDecodeTime = totalDecodeDelay / decodeCount
        }

        // 根据解码性能和丢帧情况调整队列大小
        val targetQueueSize = when {
            // 解码很快且很少丢帧 - 减小队列以降低延迟
            averageDecodeTime < 10 && droppedFrames < 10 -> {
                maxOf(minQueueSize, currentQueueSize - 5)
            }
            // 解码较慢或丢帧较多 - 增大队列以提高稳定性
            averageDecodeTime > 30 || droppedFrames > 50 -> {
                minOf(maxQueueSize, currentQueueSize + 10)
            }
            // 中等性能 - 适度调整
            averageDecodeTime > 20 || droppedFrames > 20 -> {
                minOf(maxQueueSize, currentQueueSize + 5)
            }
            else -> currentQueueSize
        }

        if (targetQueueSize != currentQueueSize) {
            AppLog.video("动态调整解码队列大小: $currentQueueSize -> $targetQueueSize " +
                    "(平均解码时间: ${averageDecodeTime}ms, 丢帧: $droppedFrames)")
            currentQueueSize = targetQueueSize
        }

        lastQueueAdjustment = currentTime

        // 重置统计计数器
        if (currentTime - lastPerformanceLogTime > 30000) {
            decodeCount = 0
            totalDecodeDelay = 0
            droppedFrames = 0
            droppedIFrames = 0
            iFrameCount = 0
            pFrameCount = 0
            inputBufferReuseHits = 0
            inputBufferReuseMisses = 0
        }
    }



    /**
     * 🚀 终极零拷贝优化：从PayloadView直接写入输入缓冲区
     */
    private fun optimizedVideoInputBufferWriteFromPayloadView(
        inputBuffer: ByteBuffer,
        payloadView: com.example.castapp.rtp.PayloadView
    ) {
        // 检查缓冲区容量是否足够
        if (inputBuffer.remaining() >= payloadView.size()) {
            // 🎯 零拷贝优化：避免不必要的clear()操作
            if (inputBuffer.position() != 0 || inputBuffer.remaining() != inputBuffer.capacity()) {
                inputBuffer.clear()
            }
            // 🚀 真正的零拷贝：直接从PayloadView写入，无需ByteArray中转
            payloadView.writeTo(inputBuffer)
        } else {
            // 缓冲区容量不足，需要清理后写入
            inputBuffer.clear()
            payloadView.writeTo(inputBuffer)
        }
    }





    /**
     * 🚀 零拷贝ByteArray包装器 - 避免数据拷贝的DataView实现
     * 直接引用ByteArray，无需额外的缓冲区管理
     */
    class ByteArrayDataView(
        private val data: ByteArray,
        override val offset: Int = 0,
        override val size: Int = data.size
    ) : com.example.castapp.network.DataView {

        override fun toByteArray(): ByteArray {
            // 🚀 零拷贝优化：如果是完整数组，直接返回引用
            return if (offset == 0 && size == data.size) {
                data // 直接返回原始数组引用，零拷贝
            } else {
                // 只有在需要子数组时才拷贝
                data.copyOfRange(offset, offset + size)
            }
        }

        override fun copyTo(dest: ByteArray, destOffset: Int, length: Int) {
            val copyLength = minOf(length, size, dest.size - destOffset)
            data.copyInto(dest, destOffset, offset, offset + copyLength)
        }

        override fun getByte(index: Int): Byte {
            require(index in 0 until size) { "Index $index out of bounds [0, $size)" }
            return data[offset + index]
        }

        // 🚀 零拷贝优化：支持源偏移量的拷贝方法
        override fun copyTo(dest: ByteArray, destOffset: Int, srcOffset: Int, length: Int) {
            require(srcOffset >= 0) { "srcOffset不能为负数: $srcOffset" }
            require(srcOffset + length <= size) { "源偏移量超出范围: srcOffset=$srcOffset, length=$length, size=$size" }

            val copyLength = minOf(length, dest.size - destOffset)
            data.copyInto(dest, destOffset, offset + srcOffset, offset + srcOffset + copyLength)
        }

        override fun copyTo(dest: ByteBuffer, srcOffset: Int, length: Int) {
            require(srcOffset >= 0) { "srcOffset不能为负数: $srcOffset" }
            require(srcOffset + length <= size) { "源偏移量超出范围: srcOffset=$srcOffset, length=$length, size=$size" }

            // 🚀 零拷贝优化：直接从底层数组写入ByteBuffer
            dest.put(data, offset + srcOffset, length)
        }

        /**
         * 🚀 零拷贝优化：获取直接ByteBuffer视图
         */
        fun getDirectByteBuffer(): ByteBuffer {
            return ByteBuffer.wrap(data, offset, size).asReadOnlyBuffer()
        }
    }

    // ==================== 🚀 错误处理方法 ====================

    /**
     * 🚀 处理进程被强制结束的错误
     */
    private fun handleProcessKilledError() {
        try {
            AppLog.w("执行进程强制结束错误处理...")

            // 立即设置所有状态为停止，阻止任何新的操作
            isRunning.set(false)
            isConfigured.set(false)
            isUpdatingResolution.set(true) // 阻止所有异步操作

            // 执行紧急清理，不等待任何操作完成
            emergencyCleanup()

            AppLog.w("进程强制结束错误处理完成")
        } catch (e: Exception) {
            // 在紧急情况下忽略所有异常
            AppLog.w("进程强制结束错误处理中发生异常: ${e.message}")
        }
    }

    /**
     * 🚀 处理资源耗尽错误
     */
    private fun handleResourceExhaustedError() {
        try {
            AppLog.w("执行资源耗尽错误处理...")

            // 优雅地停止当前操作
            if (isRunning.get()) {
                isRunning.set(false)
                isConfigured.set(false)

                // 清理异步操作
                stopAllAsyncOperations()

                // 延迟一段时间后尝试重新启动（如果仍然需要）
                backgroundReleaseExecutor.execute {
                    try {
                        Thread.sleep(2000) // 等待2秒让系统释放资源
                        AppLog.w("资源耗尽错误处理完成，可以尝试重新启动解码器")
                    } catch (_: InterruptedException) {
                        Thread.currentThread().interrupt()
                    }
                }
            }
        } catch (e: Exception) {
            AppLog.e("资源耗尽错误处理中发生异常", e)
        }
    }

    /**
     * 🚀 处理编解码器内部错误
     */
    private fun handleCodecInternalError() {
        try {
            AppLog.w("执行编解码器内部错误处理...")

            if (isRunning.get()) {
                // 安全停止解码器
                isRunning.set(false)
                isConfigured.set(false)

                // 清理异步操作
                stopAllAsyncOperations()

                AppLog.w("编解码器内部错误处理完成")
            }
        } catch (e: Exception) {
            AppLog.e("编解码器内部错误处理中发生异常", e)
        }
    }

    /**
     * 🚀 处理未知错误
     */
    private fun handleUnknownError() {
        try {
            AppLog.w("执行未知错误处理...")

            if (isRunning.get()) {
                // 采用保守的处理策略：安全停止
                isRunning.set(false)
                isConfigured.set(false)

                // 清理异步操作
                stopAllAsyncOperations()

                AppLog.w("未知错误处理完成")
            }
        } catch (e: Exception) {
            AppLog.e("未知错误处理中发生异常", e)
        }
    }
}
