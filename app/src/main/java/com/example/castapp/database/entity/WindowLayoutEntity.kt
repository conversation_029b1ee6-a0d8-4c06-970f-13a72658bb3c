package com.example.castapp.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

/**
 * 窗口布局实体类
 * 用于存储保存的窗口布局信息
 */
@Entity(tableName = "window_layouts")
data class WindowLayoutEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,

    /**
     * 布局名称（用户输入的名称，如"会议室布局1"）
     */
    val layoutName: String,

    /**
     * 布局描述（可选）
     */
    val description: String? = null,

    /**
     * 创建时间
     */
    val createdAt: Date,

    /**
     * 最后修改时间
     */
    val updatedAt: Date,

    /**
     * 布局中包含的窗口数量
     */
    val windowCount: Int,

    /**
     * 排序顺序（用于拖拽排序，数值越小越靠前）
     */
    val sortOrder: Int = 0,

    /**
     * 🐾 是否为当前应用的布局（用于状态持久化）
     */
    val isApplied: Boolean = false
) {
    /**
     * 获取格式化的创建时间字符串
     */
    fun getFormattedCreatedTime(): String {
        val formatter = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault())
        return formatter.format(createdAt)
    }
}
