package com.example.castapp.database.dao

import androidx.room.*
import com.example.castapp.database.entity.WindowLayoutEntity
import com.example.castapp.database.entity.WindowLayoutItemEntity
import kotlinx.coroutines.flow.Flow

/**
 * 窗口布局数据访问对象
 * 提供布局和布局项的数据库操作方法
 */
@Dao
interface WindowLayoutDao {
    
    // ==================== 布局操作 ====================
    
    /**
     * 插入新的布局
     * @return 新插入布局的ID
     */
    @Insert
    suspend fun insertLayout(layout: WindowLayoutEntity): Long
    
    /**
     * 更新布局信息
     */
    @Update
    suspend fun updateLayout(layout: WindowLayoutEntity)
    
    /**
     * 删除指定布局（会级联删除相关的布局项）
     */
    @Delete
    suspend fun deleteLayout(layout: WindowLayoutEntity)
    
    /**
     * 根据ID删除布局
     */
    @Query("DELETE FROM window_layouts WHERE id = :layoutId")
    suspend fun deleteLayoutById(layoutId: Long)
    
    /**
     * 获取所有布局，按排序顺序排列，然后按创建时间倒序排列
     */
    @Query("SELECT * FROM window_layouts ORDER BY sortOrder ASC, createdAt DESC")
    fun getAllLayouts(): Flow<List<WindowLayoutEntity>>
    
    /**
     * 根据ID获取布局
     */
    @Query("SELECT * FROM window_layouts WHERE id = :layoutId")
    suspend fun getLayoutById(layoutId: Long): WindowLayoutEntity?
    
    /**
     * 根据名称查找布局
     */
    @Query("SELECT * FROM window_layouts WHERE layoutName = :name")
    suspend fun getLayoutByName(name: String): WindowLayoutEntity?
    
    /**
     * 检查布局名称是否已存在
     */
    @Query("SELECT COUNT(*) FROM window_layouts WHERE layoutName = :name")
    suspend fun isLayoutNameExists(name: String): Int

    /**
     * 更新布局的排序顺序
     */
    @Query("UPDATE window_layouts SET sortOrder = :sortOrder WHERE id = :layoutId")
    suspend fun updateLayoutSortOrder(layoutId: Long, sortOrder: Int)

    /**
     * 获取最大的排序顺序值
     */
    @Query("SELECT MAX(sortOrder) FROM window_layouts")
    suspend fun getMaxSortOrder(): Int?

    /**
     * 🐾 设置布局的应用状态
     */
    @Query("UPDATE window_layouts SET isApplied = :isApplied WHERE id = :layoutId")
    suspend fun updateLayoutAppliedStatus(layoutId: Long, isApplied: Boolean)

    /**
     * 🐾 清除所有布局的应用状态
     */
    @Query("UPDATE window_layouts SET isApplied = 0")
    suspend fun clearAllAppliedStatus()

    /**
     * 🐾 获取当前应用的布局
     */
    @Query("SELECT * FROM window_layouts WHERE isApplied = 1 LIMIT 1")
    suspend fun getCurrentAppliedLayout(): WindowLayoutEntity?
    
    // ==================== 布局项操作 ====================
    
    /**
     * 插入布局项列表
     */
    @Insert
    suspend fun insertLayoutItems(items: List<WindowLayoutItemEntity>)
    
    /**
     * 插入单个布局项
     */
    @Insert
    suspend fun insertLayoutItem(item: WindowLayoutItemEntity): Long
    
    /**
     * 更新布局项
     */
    @Update
    suspend fun updateLayoutItem(item: WindowLayoutItemEntity)
    
    /**
     * 删除指定布局的所有布局项
     */
    @Query("DELETE FROM window_layout_items WHERE layoutId = :layoutId")
    suspend fun deleteLayoutItemsByLayoutId(layoutId: Long)
    
    /**
     * 获取指定布局的所有布局项，按顺序排列
     */
    @Query("SELECT * FROM window_layout_items WHERE layoutId = :layoutId ORDER BY orderIndex ASC")
    suspend fun getLayoutItemsByLayoutId(layoutId: Long): List<WindowLayoutItemEntity>
    
    /**
     * 获取指定布局的所有布局项，按顺序排列（Flow版本）
     */
    @Query("SELECT * FROM window_layout_items WHERE layoutId = :layoutId ORDER BY orderIndex ASC")
    fun getLayoutItemsByLayoutIdFlow(layoutId: Long): Flow<List<WindowLayoutItemEntity>>
    
    // ==================== 复合查询 ====================
    
    /**
     * 获取布局及其包含的所有窗口项
     */
    @Transaction
    @Query("SELECT * FROM window_layouts WHERE id = :layoutId")
    suspend fun getLayoutWithItems(layoutId: Long): LayoutWithItems?
    
    /**
     * 获取所有布局及其包含的窗口项数量统计
     */
    @Query("""
        SELECT l.*, COUNT(i.id) as actualWindowCount 
        FROM window_layouts l 
        LEFT JOIN window_layout_items i ON l.id = i.layoutId 
        GROUP BY l.id 
        ORDER BY l.createdAt DESC
    """)
    suspend fun getLayoutsWithItemCount(): List<LayoutWithItemCount>
    
    // ==================== 统计查询 ====================
    
    /**
     * 获取布局总数
     */
    @Query("SELECT COUNT(*) FROM window_layouts")
    suspend fun getLayoutCount(): Int
    
    /**
     * 获取指定布局的窗口项数量
     */
    @Query("SELECT COUNT(*) FROM window_layout_items WHERE layoutId = :layoutId")
    suspend fun getLayoutItemCount(layoutId: Long): Int
    
    // ==================== 数据类定义 ====================
    
    /**
     * 布局及其包含的窗口项
     */
    data class LayoutWithItems(
        @Embedded val layout: WindowLayoutEntity,
        @Relation(
            parentColumn = "id",
            entityColumn = "layoutId"
        )
        val items: List<WindowLayoutItemEntity>
    )
    
    /**
     * 布局及其窗口项数量统计
     */
    data class LayoutWithItemCount(
        @Embedded val layout: WindowLayoutEntity,
        val actualWindowCount: Int
    )
}
