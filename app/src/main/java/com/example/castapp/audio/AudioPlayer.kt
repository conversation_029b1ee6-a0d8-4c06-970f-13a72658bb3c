package com.example.castapp.audio

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioTrack
import com.example.castapp.network.DataView
import com.example.castapp.network.SmartDataView
import java.util.concurrent.BlockingQueue
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.atomic.AtomicBoolean
import com.example.castapp.utils.AppLog

/**
 * PCM音频播放器 - 优化版本，支持低延迟播放
 */
class AudioPlayer(
    context: Context, // 只在初始化时使用，不需要存储为属性
    private val sampleRate: Int = 48000,
    private val channelCount: Int = 1, // 1=单声道, 2=立体声
    private val audioFormat: Int = AudioFormat.ENCODING_PCM_16BIT,
    private val lowLatencyMode: Boolean = true, // 启用低延迟模式
    private var isSpeakerMode: Boolean = true // 播放模式：true=扬声器，false=听筒
) {
    companion object {
        // 🚀 CPU优化：增加日志统计间隔，减少日志频率
        private const val LOG_INTERVAL_MS = 60000L // 60秒

        // 🚀 修复：调整队列大小配置，确保延迟拷贝策略正常工作
        private const val LOW_LATENCY_QUEUE_SIZE = 8 // 恢复原始值，通过线程优先级解决卡顿
        private const val NORMAL_QUEUE_SIZE = 16
    }

    /**
     * 🚀 零拷贝优化：音频缓冲区包装类 - 支持DataView延迟拷贝
     */
    private sealed class AudioBuffer {
        abstract val size: Int
        abstract fun getByteArray(): ByteArray
        abstract fun release()

        /**
         * 基于ByteArray的音频缓冲区（池化）
         */
        data class ByteArrayBuffer(
            val data: ByteArray,
            override val size: Int,
            val isPooled: Boolean = false
        ) : AudioBuffer() {

            override fun getByteArray(): ByteArray = data

            override fun release() {
                if (isPooled) {
                    AudioBufferPool.getInstance().returnBuffer(data)
                }
            }

            override fun equals(other: Any?): Boolean {
                if (this === other) return true
                if (javaClass != other?.javaClass) return false
                other as ByteArrayBuffer
                return data.contentEquals(other.data) && size == other.size && isPooled == other.isPooled
            }

            override fun hashCode(): Int {
                var result = data.contentHashCode()
                result = 31 * result + size
                result = 31 * result + isPooled.hashCode()
                return result
            }
        }

        /**
         * 🚀 零拷贝优化：基于DataView的音频缓冲区（延迟拷贝）
         */
        data class DataViewBuffer(
            val dataView: DataView,
            override val size: Int
        ) : AudioBuffer() {

            override fun getByteArray(): ByteArray {
                // 🚀 延迟拷贝：只在真正需要时才转换为ByteArray，添加安全检查
                return try {
                    if (dataView is SmartDataView && !dataView.isValid()) {
                        AppLog.w("DataView已失效，返回空数组")
                        ByteArray(0)
                    } else {
                        dataView.toByteArray()
                    }
                } catch (e: Exception) {
                    AppLog.e("DataView转换失败", e)
                    ByteArray(0)
                }
            }

            override fun release() {
                // DataView的生命周期由SmartBuffer管理，这里不需要特殊处理
            }
        }
    }

    private var audioTrack: AudioTrack? = null
    private val isPlaying = AtomicBoolean(false)
    private val playQueue: BlockingQueue<AudioBuffer> = LinkedBlockingQueue()
    private val maxQueueSize = if (lowLatencyMode) LOW_LATENCY_QUEUE_SIZE else NORMAL_QUEUE_SIZE

    private var playThread: Thread? = null

    // 缓冲区池支持
    private val bufferPool = AudioBufferPool.getInstance()

    // 音频管理器
    private val audioManager: AudioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

    // 音频焦点请求 - 确保音量键控制正确
    private var audioFocusRequest: AudioFocusRequest? = null

    // 移除AudioTrack音量控制，改为纯系统音量控制

    // 统计信息
    private var lastLogTime = 0L
    private var framesPlayed = 0
    private var bytesPlayed = 0L
    private var droppedFrames = 0 // 丢弃的帧数
    private var pooledBuffersUsed = 0L // 使用池化缓冲区的次数

    /**
     * 启动音频播放器
     */
    fun start(): Boolean {
        if (isPlaying.get()) {
            AppLog.w("音频播放器已在运行")
            return true
        }

        try {
            // 使用统一的AudioTrack创建方法
            if (!createAudioTrack()) {
                return false
            }

            isPlaying.set(true)

            // 设置音频路由
            setAudioRouting()

            // 请求音频焦点
            requestAudioFocus()

            // 不再设置AudioTrack音量，改为纯系统音量控制
            // AudioTrack将以默认音量播放，音量由系统控制

            // 启动播放
            audioTrack?.play()

            // 启动播放线程
            startPlaybackThread()

            AppLog.audio("音频播放器启动成功: ${sampleRate}Hz, ${channelCount}ch")
            return true

        } catch (e: Exception) {
            AppLog.e("启动音频播放器失败", e)
            stop()
            return false
        }
    }

    /**
     * 停止音频播放器
     */
    fun stop() {
        isPlaying.set(false)

        try {
            // 停止播放线程
            playThread?.interrupt()
            playThread?.join(1000)
            playThread = null

            // 停止并释放AudioTrack
            audioTrack?.stop()
            audioTrack?.release()
            audioTrack = null

            // 释放音频焦点
            abandonAudioFocus()

            // 清空队列
            playQueue.clear()

            AppLog.audio("音频播放器已停止")

        } catch (e: Exception) {
            AppLog.e("停止音频播放器失败", e)
        }
    }

    /**
     * 检查播放器是否正在运行
     */
    fun isPlaying(): Boolean {
        return isPlaying.get()
    }



    /**
     * 🚀 零拷贝优化：播放PCM音频数据视图
     */
    fun playDataView(dataView: DataView, size: Int) {
        if (isPlaying.get() && size > 0) {
            // 低延迟模式下，如果队列满了就丢弃最旧的数据
            if (lowLatencyMode && playQueue.size >= maxQueueSize) {
                val droppedBuffer = playQueue.poll()
                if (droppedBuffer != null) {
                    droppedBuffer.release() // 释放缓冲区回池
                    droppedFrames++
                    AppLog.v("低延迟模式：丢弃旧音频数据以减少延迟")
                }
            }

            // 🚀 零拷贝优化：创建音频缓冲区，智能决策是否拷贝
            val audioBuffer = createAudioBufferFromDataView(dataView, size)

            if (!playQueue.offer(audioBuffer)) {
                AppLog.w("播放队列已满，丢弃音频数据")
                audioBuffer.release() // 释放缓冲区回池
                droppedFrames++
            } else {
                // 更新统计信息
                framesPlayed++
                bytesPlayed += size
                when (audioBuffer) {
                    is AudioBuffer.ByteArrayBuffer -> {
                        if (audioBuffer.isPooled) {
                            pooledBuffersUsed++
                        }
                    }
                    is AudioBuffer.DataViewBuffer -> {
                        // DataView缓冲区统计（零拷贝优化）
                    }
                }
                logStatisticsIfNeeded()
            }
        }
    }

    /**
     * 🚀 零拷贝优化：从DataView创建音频缓冲区 - 延迟拷贝策略
     */
    private fun createAudioBufferFromDataView(dataView: DataView, size: Int): AudioBuffer {
        val queueSize = playQueue.size

        return when {
            // 🚀 修复：更保守的延迟拷贝策略，只在极低负载时使用
            queueSize <= 2 && dataView is SmartDataView && dataView.isValid() -> {
                AudioBuffer.DataViewBuffer(dataView, size)
            }
            // 🚀 中等负载：小缓冲区使用池化，减少GC压力
            size <= 4096 && queueSize < maxQueueSize * 0.7 -> {
                val pooledBuffer = bufferPool.getBuffer(size)
                dataView.copyTo(pooledBuffer, 0, size)
                AudioBuffer.ByteArrayBuffer(pooledBuffer, size, isPooled = true)
            }
            // 🚀 默认策略：直接拷贝，确保播放稳定性
            else -> {
                val pcmData = dataView.toByteArray()
                AudioBuffer.ByteArrayBuffer(pcmData, size, isPooled = false)
            }
        }
    }





    /**
     * 清空播放队列
     */
    fun clearQueue() {
        try {
            val clearedCount = playQueue.size
            // 释放所有缓冲区回池
            while (playQueue.isNotEmpty()) {
                val buffer = playQueue.poll()
                buffer?.release()
            }
            AppLog.audio("已清空音频播放队列，清除 $clearedCount 个数据包")
        } catch (e: Exception) {
            AppLog.e("清空音频播放队列失败", e)
        }
    }

    /**
     * 音频播放线程
     */
    private fun playAudio() {
        AppLog.audio("音频播放线程启动")

        while (isPlaying.get() && !Thread.currentThread().isInterrupted) {
            var audioBuffer: AudioBuffer? = null
            try {
                audioBuffer = playQueue.take()
                val track = audioTrack ?: break

                // 🚀 零拷贝优化：延迟到真正需要时才获取ByteArray，添加异常处理
                val audioData = try {
                    audioBuffer.getByteArray()
                } catch (e: Exception) {
                    AppLog.e("获取音频数据失败，跳过此帧", e)
                    continue
                }

                if (audioData.isEmpty()) {
                    AppLog.w("音频数据为空，跳过此帧")
                    continue
                }

                var offset = 0
                while (offset < audioBuffer.size && isPlaying.get() && !Thread.currentThread().isInterrupted) {
                    try {
                        val bytesWritten = track.write(
                            audioData,
                            offset,
                            audioBuffer.size - offset
                        )

                        if (bytesWritten < 0) {
                            AppLog.w("音频写入错误: $bytesWritten")
                            break
                        }

                        offset += bytesWritten
                    } catch (e: IllegalStateException) {
                        AppLog.w("AudioTrack状态异常，停止写入: ${e.message}")
                        break
                    }
                }

            } catch (_: InterruptedException) {
                AppLog.audio("音频播放线程被中断")
                break
            } catch (e: Exception) {
                AppLog.e("音频播放异常", e)
                break
            } finally {
                // 释放缓冲区回池
                audioBuffer?.release()
            }
        }

        AppLog.audio("音频播放线程结束")
    }

    /**
     * 定期输出统计信息 - 包含延迟优化信息
     */
    private fun logStatisticsIfNeeded() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastLogTime >= LOG_INTERVAL_MS) {
            if (framesPlayed > 0 || droppedFrames > 0) {
                val queueSize = playQueue.size
                val mode = if (lowLatencyMode) "低延迟" else "普通"
                AppLog.audio("音频播放统计 [$mode]: 播放=${framesPlayed}帧, 丢弃=${droppedFrames}帧, " +
                        "队列=${queueSize}/${maxQueueSize}, 数据量=${bytesPlayed}字节")

                // 重置统计计数器
                framesPlayed = 0
                droppedFrames = 0
                bytesPlayed = 0L
            }
            lastLogTime = currentTime
        }
    }

    /**
     * 设置音频路由 - 🔥 根本性修复：简化路由设置，主要依赖AudioAttributes自动路由
     */
    private fun setAudioRouting() {
        try {
            // 🔥 关键：始终使用MODE_NORMAL，确保音量键控制系统音量(STREAM_MUSIC)
            audioManager.mode = AudioManager.MODE_NORMAL
            AppLog.audio("设置音频模式: MODE_NORMAL - 音频路由由AudioAttributes自动处理")
        } catch (e: Exception) {
            AppLog.e("设置音频路由失败", e)
        }
    }

    /**
     * 切换播放模式 - 线程安全版：确保播放线程安全停止后再重新创建
     */
    fun setPlaybackMode(isSpeakerMode: Boolean) {
        if (this.isSpeakerMode != isSpeakerMode) {
            val wasPlaying = isPlaying.get()
            this.isSpeakerMode = isSpeakerMode

            if (wasPlaying) {
                // 🔥 关键修复：线程安全地重新创建AudioTrack
                AppLog.audio("播放模式切换: ${if (isSpeakerMode) "扬声器" else "听筒"} - 安全重新创建AudioTrack")

                // 1. 先停止播放状态，防止新的写入
                isPlaying.set(false)

                // 2. 安全停止播放线程和AudioTrack
                stopAudioTrackSafely()

                // 3. 重新创建AudioTrack
                if (createAudioTrack()) {
                    // 4. 恢复播放状态
                    isPlaying.set(true)

                    // 5. 设置音频路由
                    setAudioRouting()

                    // 6. 请求音频焦点
                    requestAudioFocus()

                    // 7. 启动播放
                    audioTrack?.play()

                    // 8. 重新启动播放线程
                    startPlaybackThread()

                    AppLog.audio("AudioTrack安全重新创建成功，播放模式已切换")
                } else {
                    AppLog.e("AudioTrack重新创建失败")
                    isPlaying.set(false)
                }
            } else {
                AppLog.audio("切换播放模式: ${if (isSpeakerMode) "扬声器" else "听筒"} (未播放状态)")
            }
        }
    }

    /**
     * 安全停止AudioTrack和播放线程
     */
    private fun stopAudioTrackSafely() {
        try {
            // 1. 中断播放线程
            playThread?.interrupt()

            // 2. 等待播放线程结束（最多等待500ms）
            playThread?.join(500)
            playThread = null

            // 3. 停止并释放AudioTrack
            audioTrack?.stop()
            audioTrack?.release()
            audioTrack = null

            AppLog.audio("AudioTrack和播放线程已安全停止")
        } catch (e: Exception) {
            AppLog.e("安全停止AudioTrack失败", e)
        }
    }

    /**
     * 启动播放线程
     */
    private fun startPlaybackThread() {
        try {
            playThread = Thread {
                android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_URGENT_AUDIO)
                playAudio()
            }.apply {
                name = "AudioPlayerThread"
                priority = Thread.MAX_PRIORITY
                start()
            }
            AppLog.audio("播放线程已启动")
        } catch (e: Exception) {
            AppLog.e("启动播放线程失败", e)
        }
    }

    /**
     * 创建AudioTrack（提取为独立方法）
     */
    private fun createAudioTrack(): Boolean {
        try {
            val channelConfig = if (channelCount == 1) {
                AudioFormat.CHANNEL_OUT_MONO
            } else {
                AudioFormat.CHANNEL_OUT_STEREO
            }

            val bufferSize = AudioTrack.getMinBufferSize(sampleRate, channelConfig, audioFormat)
            if (bufferSize == AudioTrack.ERROR || bufferSize == AudioTrack.ERROR_BAD_VALUE) {
                AppLog.e("无法获取音频播放缓冲区大小")
                return false
            }

            // 🔥 根本性修复：根据播放模式动态设置AudioAttributes
            val audioAttributes = if (isSpeakerMode) {
                // 扬声器模式：使用媒体播放属性，系统自动路由到扬声器
                AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_MEDIA)
                    .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                    .build()
            } else {
                // 听筒模式：使用语音通话属性，系统自动路由到听筒
                AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
                    .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                    .build()
            }

            AppLog.audio("创建AudioTrack - 播放模式: ${if (isSpeakerMode) "扬声器(USAGE_MEDIA)" else "听筒(USAGE_VOICE_COMMUNICATION)"}")

            val audioTrackBuilder = AudioTrack.Builder()
                .setAudioAttributes(audioAttributes)
                .setAudioFormat(
                    AudioFormat.Builder()
                        .setEncoding(audioFormat)
                        .setSampleRate(sampleRate)
                        .setChannelMask(channelConfig)
                        .build()
                )
                .setTransferMode(AudioTrack.MODE_STREAM)

            // 低延迟模式配置
            if (lowLatencyMode) {
                audioTrackBuilder.setBufferSizeInBytes(bufferSize)
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                    audioTrackBuilder.setPerformanceMode(AudioTrack.PERFORMANCE_MODE_LOW_LATENCY)
                }
            } else {
                audioTrackBuilder.setBufferSizeInBytes(bufferSize * 2)
            }

            audioTrack = audioTrackBuilder.build()

            if (audioTrack?.state != AudioTrack.STATE_INITIALIZED) {
                AppLog.e("AudioTrack初始化失败")
                audioTrack?.release()
                audioTrack = null
                return false
            }

            // 🔥 根本性修复：AudioAttributes会自动处理音频路由，无需手动设置设备路由
            AppLog.audio("AudioTrack创建成功，系统将根据AudioAttributes自动路由音频")
            return true

        } catch (e: Exception) {
            AppLog.e("创建AudioTrack失败", e)
            return false
        }
    }

    /**
     * 请求音频焦点 - 确保音量键控制系统音量
     */
    private fun requestAudioFocus() {
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                val audioAttributes = AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_MEDIA)
                    .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                    .build()

                audioFocusRequest = AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
                    .setAudioAttributes(audioAttributes)
                    .setOnAudioFocusChangeListener { focusChange ->
                        AppLog.audio("音频焦点变化: $focusChange")
                    }
                    .build()

                val result = audioManager.requestAudioFocus(audioFocusRequest!!)
                AppLog.audio("请求音频焦点: ${if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) "成功" else "失败"}")
            } else {
                @Suppress("DEPRECATION")
                val result = audioManager.requestAudioFocus(
                    { focusChange -> AppLog.audio("音频焦点变化: $focusChange") },
                    AudioManager.STREAM_MUSIC,
                    AudioManager.AUDIOFOCUS_GAIN
                )
                AppLog.audio("请求音频焦点(旧API): ${if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) "成功" else "失败"}")
            }
        } catch (e: Exception) {
            AppLog.e("请求音频焦点失败", e)
        }
    }

    /**
     * 释放音频焦点
     */
    private fun abandonAudioFocus() {
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                audioFocusRequest?.let { request ->
                    audioManager.abandonAudioFocusRequest(request)
                    audioFocusRequest = null
                    AppLog.audio("释放音频焦点")
                }
            } else {
                @Suppress("DEPRECATION")
                audioManager.abandonAudioFocus { focusChange ->
                    AppLog.audio("音频焦点变化: $focusChange")
                }
                AppLog.audio("释放音频焦点(旧API)")
            }
        } catch (e: Exception) {
            AppLog.e("释放音频焦点失败", e)
        }
    }

    // 移除AudioTrack音量控制方法，改为纯系统音量控制
    // setVolume() 和 getVolume() 方法已移除
    // 音量现在完全由系统AudioManager控制
}
