package com.example.castapp.audio

import android.media.MediaCodec
import android.media.MediaFormat
import com.example.castapp.utils.ResourceManager
import com.example.castapp.network.DataView
import java.nio.ByteBuffer
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import com.example.castapp.utils.AppLog

/**
 * AAC音频解码器 - 异步模式
 * 🚀 基于MediaCodec.Callback的高性能异步解码实现
 */
class AudioDecoder(
    private val sampleRate: Int = 48000,
    private val channelCount: Int = 1, // 1=单声道, 2=立体声
    private val onDecodedData: (DataView, Int) -> Unit, // 🚀 零拷贝：数据视图和大小
    private val lowLatencyMode: Boolean = true // 启用低延迟模式
) {
    companion object {
        private const val MIME_TYPE = MediaFormat.MIMETYPE_AUDIO_AAC

        // 🚀 CPU优化：增加日志统计间隔，减少日志频率
        private const val LOG_INTERVAL_MS = 60000L // 60秒
    }

    /**
     * 🚀 零拷贝核心：音频解码缓冲区引用管理
     * 延迟释放MediaCodec缓冲区直到数据处理完成
     */
    private class AudioDecoderBufferReference(
        private val outputBuffer: ByteBuffer,
        private val codec: MediaCodec,
        private val bufferIndex: Int,
        private val dataSize: Int
    ) {
        private val refCount = AtomicInteger(1)
        private var isReleased = false

        /**
         * 获取只读数据视图，完全避免数据拷贝
         */
        val dataView: DataView = object : DataView {
            override val size: Int = dataSize
            override val offset: Int = 0

            override fun getByte(index: Int): Byte {
                return outputBuffer.get(outputBuffer.position() + index)
            }

            override fun toByteArray(): ByteArray {
                val data = ByteArray(dataSize)
                val originalPosition = outputBuffer.position()
                outputBuffer.get(data)
                outputBuffer.position(originalPosition)
                return data
            }

            override fun copyTo(dest: ByteArray, destOffset: Int, length: Int) {
                val copyLength = minOf(length, dataSize, dest.size - destOffset)
                val originalPosition = outputBuffer.position()
                outputBuffer.get(dest, destOffset, copyLength)
                outputBuffer.position(originalPosition)
            }

            // 🚀 零拷贝优化：支持源偏移量的拷贝方法
            override fun copyTo(dest: ByteArray, destOffset: Int, srcOffset: Int, length: Int) {
                require(srcOffset >= 0) { "srcOffset不能为负数: $srcOffset" }
                require(srcOffset + length <= dataSize) { "源偏移量超出范围: srcOffset=$srcOffset, length=$length, size=$dataSize" }

                val copyLength = minOf(length, dest.size - destOffset)
                val originalPosition = outputBuffer.position()
                outputBuffer.position(outputBuffer.position() + srcOffset)
                outputBuffer.get(dest, destOffset, copyLength)
                outputBuffer.position(originalPosition)
            }

            override fun copyTo(dest: ByteBuffer, srcOffset: Int, length: Int) {
                require(srcOffset >= 0) { "srcOffset不能为负数: $srcOffset" }
                require(srcOffset + length <= dataSize) { "源偏移量超出范围: srcOffset=$srcOffset, length=$length, size=$dataSize" }

                val originalPosition = outputBuffer.position()
                val originalLimit = outputBuffer.limit()

                outputBuffer.position(outputBuffer.position() + srcOffset)
                outputBuffer.limit(outputBuffer.position() + length)
                dest.put(outputBuffer)

                outputBuffer.position(originalPosition)
                outputBuffer.limit(originalLimit)
            }
        }

        /**
         * 减少引用计数，当计数为0时释放MediaCodec缓冲区
         */
        fun release() {
            if (isReleased) return

            val count = refCount.decrementAndGet()
            if (count == 0) {
                isReleased = true
                try {
                    codec.releaseOutputBuffer(bufferIndex, false)
                } catch (e: Exception) {
                    AppLog.w("释放音频解码MediaCodec缓冲区时出错: ${e.message}")
                }
            } else if (count < 0) {
                AppLog.w("音频解码缓冲区引用计数异常: count=$count")
            }
        }
    }

    private var mediaCodec: MediaCodec? = null
    private val isRunning = AtomicBoolean(false)
    private val isConfigured = AtomicBoolean(false)

    // 🚀 零拷贝优化：使用DataView队列，避免数据拷贝
    private val inputQueue = ConcurrentLinkedQueue<DataView>()
    private var configData: ByteArray? = null

    // 🚀 跟踪可用的输入缓冲区
    private val availableInputBuffers = ConcurrentLinkedQueue<Int>()

    // 统计信息
    private var lastLogTime = 0L
    private var decodedFrames = 0
    private var decodedBytes = 0L

    // 🚀 零拷贝优化：输出缓冲区重用统计
    private var bufferReuseHits = 0L
    private var bufferReuseMisses = 0L

    /**
     * MediaCodec异步回调处理器
     * 🚀 异步模式：系统驱动的高效解码处理
     */
    private inner class MediaCodecCallback : MediaCodec.Callback() {

        override fun onInputBufferAvailable(codec: MediaCodec, index: Int) {
            if (!isRunning.get()) return

            try {
                // 🚀 异步模式：立即尝试处理输入数据
                if (!processInputData(codec, index)) {
                    // 如果没有数据，将缓冲区索引保存起来
                    availableInputBuffers.offer(index)
                }
            } catch (e: Exception) {
                if (isRunning.get()) {
                    AppLog.e("异步音频解码输入处理出错", e)
                }
            }
        }

        override fun onOutputBufferAvailable(codec: MediaCodec, index: Int, info: MediaCodec.BufferInfo) {
            if (!isRunning.get()) return

            try {
                when {
                    info.flags and MediaCodec.BUFFER_FLAG_CODEC_CONFIG != 0 -> {
                        // 处理配置数据（通常不会在解码器中出现）
                        codec.releaseOutputBuffer(index, false)
                    }

                    info.size > 0 -> {
                        // 处理解码后的PCM数据
                        handleDecodedData(codec, index, info)
                    }

                    else -> {
                        // 释放空缓冲区
                        codec.releaseOutputBuffer(index, false)
                    }
                }

                // 检查是否结束
                if (info.flags and MediaCodec.BUFFER_FLAG_END_OF_STREAM != 0) {
                    AppLog.audio("异步音频解码结束")
                }

            } catch (e: Exception) {
                if (isRunning.get()) {
                    AppLog.e("异步音频解码输出处理出错", e)
                }
                // 确保在异常情况下也释放缓冲区
                try {
                    codec.releaseOutputBuffer(index, false)
                } catch (releaseException: Exception) {
                    AppLog.w("释放异常缓冲区时出错: ${releaseException.message}")
                }
            }
        }

        override fun onError(codec: MediaCodec, e: MediaCodec.CodecException) {
            AppLog.e("MediaCodec异步音频解码出错: ${e.message}", e)
            // 在错误情况下停止解码器
            if (isRunning.get()) {
                isRunning.set(false)
            }
        }

        override fun onOutputFormatChanged(codec: MediaCodec, format: MediaFormat) {
            AppLog.audio("异步音频解码器输出格式改变: $format")
        }
    }

    /**
     * 设置配置数据（AudioSpecificConfig）
     */
    fun setConfigData(config: ByteArray) {
        configData = config
        AppLog.audio("设置AAC配置数据: ${config.size}字节")

        if (!isConfigured.get()) {
            configureDecoder()
        }
    }

    /**
     * 配置异步解码器
     */
    private fun configureDecoder() {
        try {
            val config = configData
            if (config == null) {
                AppLog.w("配置数据为空，无法配置解码器")
                return
            }

            AppLog.audio("配置AAC异步音频解码器: ${sampleRate}Hz, ${channelCount}ch")

            val format = MediaFormat.createAudioFormat(MIME_TYPE, sampleRate, channelCount).apply {
                setByteBuffer("csd-0", ByteBuffer.wrap(config))
                setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, 16384)

                // 低延迟模式配置
                if (lowLatencyMode) {
                    // 启用低延迟解码（最小SDK 24已支持优先级设置）
                    setInteger(MediaFormat.KEY_PRIORITY, 0) // 最高优先级

                    // 低延迟标志需要API 30+，使用字符串常量避免API检查警告
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
                        setInteger("low-latency", 1) // 启用低延迟
                    }
                }
            }

            val codec = MediaCodec.createDecoderByType(MIME_TYPE)

            try {
                // 🚀 设置异步回调 - 在configure之前设置
                codec.setCallback(MediaCodecCallback())
                codec.configure(format, null, null, 0)

                // 只有在成功配置后才赋值给成员变量
                mediaCodec = codec
                isConfigured.set(true)

                // 🚀 启动异步解码器 - 系统将自动调用回调
                codec.start()

                AppLog.audio("AAC异步音频解码器配置成功")
            } catch (e: Exception) {
                // 确保在异常情况下释放资源
                ResourceManager.safeReleaseMediaCodec(codec, "音频解码器")
                throw e
            }

        } catch (e: Exception) {
            AppLog.e("配置AAC异步音频解码器失败", e)
            isConfigured.set(false)
        }
    }

    /**
     * 启动异步解码器
     */
    fun start(): Boolean {
        if (isRunning.get()) {
            AppLog.w("解码器已在运行")
            return true
        }

        try {
            isRunning.set(true)
            AppLog.audio("AAC异步音频解码器启动成功，等待配置数据")
            return true

        } catch (e: Exception) {
            AppLog.e("启动AAC异步音频解码器失败", e)
            stop()
            return false
        }
    }

    /**
     * 停止异步解码器
     */
    fun stop() {
        isRunning.set(false)
        isConfigured.set(false)
        configData = null

        // 使用资源管理器安全释放MediaCodec资源
        ResourceManager.safeReleaseMediaCodec(mediaCodec, "异步音频解码器")
        mediaCodec = null

        // 清空队列
        inputQueue.clear()
        availableInputBuffers.clear()

        AppLog.audio("AAC异步音频解码器已停止")
    }

    /**
     * 🚀 零拷贝优化：输入AAC DataView进行解码 - 异步模式
     */
    fun decode(aacDataView: DataView) {
        if (aacDataView.size > 0) {
            // 🚀 真正的零拷贝：直接存储DataView，延迟到MediaCodec输入时才转换
            inputQueue.offer(aacDataView)

            // 🚀 立即尝试处理数据，如果有可用的输入缓冲区
            tryProcessPendingData()
        }
    }

    /**
     * 🚀 关键方法：尝试处理待处理的数据
     * 当新数据加入队列时，检查是否有可用的输入缓冲区并立即处理
     */
    private fun tryProcessPendingData() {
        if (!isRunning.get() || !isConfigured.get()) return

        val codec = mediaCodec ?: return

        // 尝试处理所有可用的输入缓冲区
        while (true) {
            val bufferIndex = availableInputBuffers.poll() ?: break
            if (!processInputData(codec, bufferIndex)) {
                // 如果没有数据处理，将缓冲区放回
                availableInputBuffers.offer(bufferIndex)
                break
            }
        }
    }

    /**
     * 🚀 零拷贝优化：处理输入数据 - 支持DataView
     * @return true 如果成功处理了数据，false 如果没有数据可处理
     */
    private fun processInputData(codec: MediaCodec, bufferIndex: Int): Boolean {
        val aacDataView = inputQueue.poll() ?: return false

        try {
            val inputBuffer = codec.getInputBuffer(bufferIndex)
            if (inputBuffer != null) {
                // 🚀 零拷贝优化：直接从DataView写入MediaCodec缓冲区
                optimizedInputBufferWriteFromDataView(inputBuffer, aacDataView)

                codec.queueInputBuffer(
                    bufferIndex,
                    0,
                    aacDataView.size,
                    System.nanoTime() / 1000,
                    0
                )
                return true
            }
        } catch (e: Exception) {
            AppLog.e("处理音频解码输入数据失败", e)
        }

        return false
    }

    /**
     * 处理解码后的数据 - 🚀 零拷贝优化版本
     */
    private fun handleDecodedData(codec: MediaCodec, index: Int, info: MediaCodec.BufferInfo) {
        try {
            val outputBuffer = codec.getOutputBuffer(index)
            if (outputBuffer != null && info.size > 0) {
                // 🚀 零拷贝核心优化：使用引用计数管理缓冲区生命周期
                // 创建缓冲区引用，延迟释放MediaCodec缓冲区直到数据处理完成
                val bufferRef = AudioDecoderBufferReference(outputBuffer, codec, index, info.size)

                // 更新统计信息
                decodedFrames++
                decodedBytes += info.size
                logStatisticsIfNeeded()

                try {
                    // 直接传递只读缓冲区视图，完全避免数据拷贝
                    onDecodedData(bufferRef.dataView, info.size)
                } finally {
                    // 数据处理完成后释放缓冲区引用
                    bufferRef.release()
                }
            } else {
                // 如果没有有效数据，直接释放缓冲区
                codec.releaseOutputBuffer(index, false)
            }
        } catch (e: Exception) {
            AppLog.e("处理解码音频数据失败", e)
            // 确保在异常情况下也释放缓冲区
            try {
                codec.releaseOutputBuffer(index, false)
            } catch (releaseException: Exception) {
                AppLog.w("释放异常音频解码缓冲区时出错: ${releaseException.message}")
            }
        }
    }



    /**
     * 🚀 零拷贝优化：直接从DataView写入MediaCodec缓冲区
     */
    private fun optimizedInputBufferWriteFromDataView(inputBuffer: ByteBuffer, aacDataView: DataView) {
        // 检查缓冲区容量是否足够
        if (inputBuffer.remaining() >= aacDataView.size) {
            // 🎯 零拷贝优化：避免不必要的clear()操作
            if (inputBuffer.position() != 0 || inputBuffer.remaining() != inputBuffer.capacity()) {
                inputBuffer.clear()
            }
            // 🚀 真正的零拷贝：直接从DataView拷贝到ByteBuffer
            aacDataView.copyTo(inputBuffer, 0, aacDataView.size)
            bufferReuseHits++
        } else {
            // 缓冲区容量不足，需要清理后写入
            inputBuffer.clear()
            aacDataView.copyTo(inputBuffer, 0, aacDataView.size)
            bufferReuseMisses++
        }
    }

    /**
     * 🚀 CPU优化：简化统计信息计算，减少浮点运算
     */
    private fun logStatisticsIfNeeded() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastLogTime >= LOG_INTERVAL_MS) {
            if (decodedFrames > 0) {
                // 🚀 CPU优化：简化重用率计算，避免浮点除法
                val totalBufferOps = bufferReuseHits + bufferReuseMisses
                val reusePercent = if (totalBufferOps > 0) {
                    (bufferReuseHits * 100 / totalBufferOps).toInt()
                } else 0

                AppLog.audio("AAC异步解码统计: 解码帧数=${decodedFrames}帧, PCM数据量=${decodedBytes}字节")
                AppLog.audio("缓冲区优化: 重用率=${reusePercent}% (命中=${bufferReuseHits}, 未命中=${bufferReuseMisses})")

                // 重置统计计数器
                decodedFrames = 0
                decodedBytes = 0L
                bufferReuseHits = 0L
                bufferReuseMisses = 0L
            }
            lastLogTime = currentTime
        }
    }

    /**
     * 获取解码器状态
     */
    fun isRunning(): Boolean = isRunning.get()
}
