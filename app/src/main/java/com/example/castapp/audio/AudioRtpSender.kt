package com.example.castapp.audio

import com.example.castapp.network.UdpSender
import com.example.castapp.network.DataView
import java.nio.ByteBuffer
import java.util.concurrent.atomic.AtomicInteger
import com.example.castapp.utils.AppLog

/**
 * 音频RTP发送器
 * 专门用于发送AAC音频数据
 */
class AudioRtpSender(
    targetIp: String,
    targetPort: Int,
    private val ssrc: Long,
    private val payloadType: Int, // 97=媒体音频, 98=麦克风音频
    private val sampleRate: Int = 48000
) {
    companion object {        private const val RTP_VERSION = 2
        private const val MAX_PACKET_SIZE = 1200 // 音频包通常较小

        // AAC相关常量
        private const val AAC_SAMPLES_PER_FRAME = 1024 // AAC每帧样本数

        // 🚀 CPU优化：增加日志统计间隔，减少日志频率
        private const val LOG_INTERVAL_MS = 60000L // 60秒
    }

    private val udpSender = UdpSender(targetIp, targetPort)
    private val sequenceNumber = AtomicInteger(0)
    private var timestampOffset = 0L
    private var lastTimestamp = 0L

    // 统计信息
    private var lastLogTime = 0L
    private var packetsSent = 0
    private var bytesSent = 0L
    private var packetsFailedToSend = 0

    // 零拷贝优化统计
    private var zeroCopyPacketsSent = 0
    private var fragmentedPacketsSent = 0

    init {
        timestampOffset = System.currentTimeMillis() and 0xFFFFFFFFL
    }

    /**
     * 启动RTP发送器
     */
    fun start(): Boolean {
        return udpSender.start()
    }

    /**
     * 停止RTP发送器
     */
    fun stop() {
        udpSender.stop()
    }

    /**
     * 完全清理RTP发送器资源，包括协程作用域
     * 在发送器不再使用时调用
     */
    fun cleanup() {
        AppLog.audio("开始清理AudioRtpSender资源...")
        udpSender.cleanup()
        AppLog.audio("AudioRtpSender资源清理完成")
    }

    /**
     * 🚀 零拷贝优化：发送AAC音频数据视图
     */
    fun sendAacDataView(dataView: DataView, size: Int) {
        if (size <= 0) {
            return
        }

        try {
            // 计算时间戳（基于采样率）
            val timestamp = calculateTimestamp()

            // 如果AAC数据较小，可以放在一个RTP包中
            if (size <= MAX_PACKET_SIZE - 12 - 4) { // 12字节RTP头 + 4字节AAC头
                sendSinglePacketFromDataView(dataView, size, timestamp)
            } else {
                // 大的AAC帧需要分片
                sendFragmentedPacketsFromDataView(dataView, size, timestamp)
            }

        } catch (e: Exception) {
            AppLog.e("发送AAC数据视图失败", e)
        }
    }







    /**
     * 定期输出统计信息
     */
    private fun logStatisticsIfNeeded() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastLogTime >= LOG_INTERVAL_MS) {
            if (packetsSent > 0 || packetsFailedToSend > 0) {
                val successRate = if (packetsSent + packetsFailedToSend > 0) {
                    (packetsSent * 100.0 / (packetsSent + packetsFailedToSend))
                } else 0.0

                val zeroCopyRate = if (packetsSent > 0) {
                    (zeroCopyPacketsSent * 100.0 / packetsSent)
                } else 0.0

                AppLog.audio("AAC发送统计 [SSRC=$ssrc]: 成功=${packetsSent}包, 失败=${packetsFailedToSend}包, " +
                        "成功率=${String.format("%.1f%%", successRate)}, 数据量=${bytesSent}字节, " +
                        "零拷贝=${zeroCopyPacketsSent}包(${String.format("%.1f%%", zeroCopyRate)}), " +
                        "分片=${fragmentedPacketsSent}包")

                // 重置统计计数器
                packetsSent = 0
                packetsFailedToSend = 0
                bytesSent = 0L
                zeroCopyPacketsSent = 0
                fragmentedPacketsSent = 0
            }
            lastLogTime = currentTime
        }
    }

    /**
     * 🚀 零拷贝优化：从DataView发送单个RTP包
     */
    private fun sendSinglePacketFromDataView(dataView: DataView, size: Int, timestamp: Long) {
        val seqNum = sequenceNumber.getAndIncrement() and 0xFFFF
        val rtpPacket = createRtpPacketFromDataView(dataView, 0, size, seqNum, timestamp, marker = true)

        if (udpSender.send(rtpPacket)) {
            // 更新统计信息
            packetsSent++
            zeroCopyPacketsSent++
            bytesSent += size
            logStatisticsIfNeeded()
        } else {
            packetsFailedToSend++
            AppLog.w("发送AAC RTP包失败: seq=$seqNum")
        }
    }

    /**
     * 🚀 零拷贝优化：从DataView发送分片RTP包
     */
    private fun sendFragmentedPacketsFromDataView(dataView: DataView, size: Int, timestamp: Long) {
        val maxPayloadSize = MAX_PACKET_SIZE - 12 - 4 // RTP头 + AAC头
        val totalFragments = (size + maxPayloadSize - 1) / maxPayloadSize

        for (i in 0 until totalFragments) {
            val start = i * maxPayloadSize
            val fragmentSize = minOf(maxPayloadSize, size - start)

            val seqNum = sequenceNumber.getAndIncrement() and 0xFFFF
            val isLastFragment = (i == totalFragments - 1)

            // 使用零拷贝视图创建RTP包
            val rtpPacket = createRtpPacketFromDataView(dataView, start, fragmentSize, seqNum, timestamp, marker = isLastFragment)

            if (udpSender.send(rtpPacket)) {
                // 更新统计信息
                packetsSent++
                fragmentedPacketsSent++
                bytesSent += fragmentSize
                logStatisticsIfNeeded()
            } else {
                packetsFailedToSend++
                AppLog.w("发送AAC分片失败: seq=$seqNum, frag=${i+1}/$totalFragments")
            }
        }
    }

    /**
     * 🚀 零拷贝优化：从DataView创建RTP包
     */
    private fun createRtpPacketFromDataView(
        dataView: DataView,
        offset: Int,
        length: Int,
        sequenceNumber: Int,
        timestamp: Long,
        marker: Boolean
    ): ByteArray {
        val rtpHeaderSize = 12
        val totalSize = rtpHeaderSize + length

        val packet = ByteArray(totalSize)
        val buffer = ByteBuffer.wrap(packet)

        // RTP固定头部 (12字节)
        val firstByte = (RTP_VERSION shl 6) // V=2, P=0, X=0, CC=0
        buffer.put(firstByte.toByte())

        // 第二个字节：M位 + PT
        val secondByte = if (marker) {
            (1 shl 7) or payloadType // M=1, PT=payloadType
        } else {
            payloadType // M=0, PT=payloadType
        }
        buffer.put(secondByte.toByte())
        buffer.putShort(sequenceNumber.toShort()) // 序列号
        buffer.putInt(timestamp.toInt()) // 时间戳
        buffer.putInt(ssrc.toInt()) // SSRC

        // 🚀 真正的零拷贝：直接从DataView指定偏移量拷贝到RTP包
        // 使用新的支持源偏移量的copyTo方法，避免完整数组拷贝
        dataView.copyTo(buffer, offset, length)

        return packet
    }



    /**
     * 计算RTP时间戳 - 优化版本，基于实际时间
     */
    private fun calculateTimestamp(): Long {
        // 使用实际时间计算时间戳，确保音频同步
        val currentTimeMs = System.currentTimeMillis()

        if (lastTimestamp == 0L) {
            // 首次计算，记录起始时间
            timestampOffset = currentTimeMs and 0xFFFFFFFFL
            lastTimestamp = timestampOffset
        } else {
            // 基于实际时间差计算时间戳
            val elapsedMs = currentTimeMs - (timestampOffset + (lastTimestamp - timestampOffset) * 1000 / sampleRate)
            val timestampIncrement = elapsedMs * sampleRate / 1000

            // 确保时间戳单调递增，计算实际增量
            val actualIncrement = if (timestampIncrement > 0) {
                timestampIncrement
            } else {
                // 如果时间差太小，使用固定增量避免时间戳倒退
                AAC_SAMPLES_PER_FRAME.toLong()
            }
            lastTimestamp += actualIncrement
        }

        return lastTimestamp and 0xFFFFFFFFL
    }


}
