package com.example.castapp.audio

import android.media.MediaCodec
import android.media.MediaCodecInfo
import android.media.MediaFormat
import com.example.castapp.utils.ResourceManager
import com.example.castapp.utils.ResourceManager.safeExecute
import com.example.castapp.network.DataView
import java.nio.ByteBuffer
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.Executors
import com.example.castapp.utils.AppLog

/**
 * AAC音频编码器 - 异步模式
 * 🚀 基于MediaCodec.Callback的高性能异步编码实现
 */
class AudioEncoder(
    private val sampleRate: Int = 48000,
    private val channelCount: Int = 1, // 1=单声道, 2=立体声
    private val bitRate: Int = 64000, // 64kbps for mono, 128kbps for stereo
    private val onEncodedData: (DataView, Int, Boolean) -> Unit, // 🚀 零拷贝：数据视图、大小、是否关键帧
    private val onConfigurationDataViaWebSocket: ((ByteArray) -> Unit)? = null, // WebSocket配置数据回调
    private val lowLatencyMode: Boolean = true // 启用低延迟模式
) {
    companion object {
        private const val MIME_TYPE = MediaFormat.MIMETYPE_AUDIO_AAC
    }

    /**
     * 🚀 零拷贝核心：音频缓冲区引用管理
     * 延迟释放MediaCodec缓冲区直到网络发送完成
     */
    private class AudioBufferReference(
        private val outputBuffer: ByteBuffer,
        private val codec: MediaCodec,
        private val bufferIndex: Int,
        private val dataSize: Int
    ) {
        private val refCount = AtomicInteger(1)
        private var isReleased = false

        /**
         * 获取只读数据视图，完全避免数据拷贝
         */
        val dataView: DataView = object : DataView {
            override val size: Int = dataSize
            override val offset: Int = 0

            override fun getByte(index: Int): Byte {
                return outputBuffer.get(outputBuffer.position() + index)
            }

            override fun toByteArray(): ByteArray {
                val data = ByteArray(dataSize)
                val originalPosition = outputBuffer.position()
                outputBuffer.get(data)
                outputBuffer.position(originalPosition)
                return data
            }

            override fun copyTo(dest: ByteArray, destOffset: Int, length: Int) {
                val copyLength = minOf(length, dataSize, dest.size - destOffset)
                val originalPosition = outputBuffer.position()
                outputBuffer.get(dest, destOffset, copyLength)
                outputBuffer.position(originalPosition)
            }

            // 🚀 零拷贝优化：支持源偏移量的拷贝方法
            override fun copyTo(dest: ByteArray, destOffset: Int, srcOffset: Int, length: Int) {
                require(srcOffset >= 0) { "srcOffset不能为负数: $srcOffset" }
                require(srcOffset + length <= dataSize) { "源偏移量超出范围: srcOffset=$srcOffset, length=$length, size=$dataSize" }

                val copyLength = minOf(length, dest.size - destOffset)
                val originalPosition = outputBuffer.position()
                outputBuffer.position(outputBuffer.position() + srcOffset)
                outputBuffer.get(dest, destOffset, copyLength)
                outputBuffer.position(originalPosition)
            }

            override fun copyTo(dest: ByteBuffer, srcOffset: Int, length: Int) {
                require(srcOffset >= 0) { "srcOffset不能为负数: $srcOffset" }
                require(srcOffset + length <= dataSize) { "源偏移量超出范围: srcOffset=$srcOffset, length=$length, size=$dataSize" }

                val originalPosition = outputBuffer.position()
                val originalLimit = outputBuffer.limit()

                outputBuffer.position(outputBuffer.position() + srcOffset)
                outputBuffer.limit(outputBuffer.position() + length)
                dest.put(outputBuffer)

                outputBuffer.position(originalPosition)
                outputBuffer.limit(originalLimit)
            }
        }

        /**
         * 减少引用计数，当计数为0时释放MediaCodec缓冲区
         */
        fun release() {
            if (isReleased) return

            val count = refCount.decrementAndGet()
            if (count == 0) {
                isReleased = true
                try {
                    codec.releaseOutputBuffer(bufferIndex, false)
                } catch (e: Exception) {
                    AppLog.w("释放音频MediaCodec缓冲区时出错: ${e.message}")
                }
            } else if (count < 0) {
                AppLog.w("音频缓冲区引用计数异常: count=$count")
            }
        }
    }

    private var mediaCodec: MediaCodec? = null
    private val isRunning = AtomicBoolean(false)

    // 🚀 异步模式：使用无锁队列替代阻塞队列
    private val inputQueue = ConcurrentLinkedQueue<ByteArray>()

    // 🚀 跟踪可用的输入缓冲区
    private val availableInputBuffers = ConcurrentLinkedQueue<Int>()

    // 缓存配置数据，用于新连接
    private var cachedConfigData: ByteArray? = null
    private var configurationSent = false

    // 🐾 根源修复：配置数据发送状态跟踪，防止重复发送
    private val configDataSentToConnections = ConcurrentHashMap<String, Boolean>()

    // 音量控制
    @Volatile
    private var volumeMultiplier: Float = 1.0f

    // 🚀 异步网络发送线程池 - 避免编码线程阻塞，修复投屏时音频卡顿
    private val networkExecutor = Executors.newSingleThreadExecutor { r ->
        Thread(r, "AudioEncoder-AsyncNetwork").apply {
            // 🚀 关键修复：设置音频编码网络线程为高优先级，避免被投屏启动影响
            priority = Thread.MAX_PRIORITY
            isDaemon = true
        }
    }

    /**
     * MediaCodec异步回调处理器
     * 🚀 异步模式：系统驱动的高效编码处理
     */
    private inner class MediaCodecCallback : MediaCodec.Callback() {

        override fun onInputBufferAvailable(codec: MediaCodec, index: Int) {
            if (!isRunning.get()) return

            try {
                // 🚀 异步模式：立即尝试处理输入数据
                if (!processInputData(codec, index)) {
                    // 如果没有数据，将缓冲区索引保存起来
                    availableInputBuffers.offer(index)
                }
            } catch (e: Exception) {
                if (isRunning.get()) {
                    AppLog.e("异步音频编码输入处理出错", e)
                }
            }
        }

        override fun onOutputBufferAvailable(codec: MediaCodec, index: Int, info: MediaCodec.BufferInfo) {
            if (!isRunning.get()) return

            try {
                when {
                    info.flags and MediaCodec.BUFFER_FLAG_CODEC_CONFIG != 0 -> {
                        // 处理配置数据
                        handleConfigurationData(codec, index, info)
                    }

                    info.size > 0 -> {
                        // 处理编码后的AAC数据
                        handleEncodedData(codec, index, info)
                    }

                    else -> {
                        // 释放空缓冲区
                        codec.releaseOutputBuffer(index, false)
                    }
                }

                // 检查是否结束
                if (info.flags and MediaCodec.BUFFER_FLAG_END_OF_STREAM != 0) {
                    AppLog.audio("异步音频编码结束")
                }

            } catch (e: Exception) {
                if (isRunning.get()) {
                    AppLog.e("异步音频编码输出处理出错", e)
                }
                // 确保在异常情况下也释放缓冲区
                try {
                    codec.releaseOutputBuffer(index, false)
                } catch (releaseException: Exception) {
                    AppLog.w("释放异常缓冲区时出错: ${releaseException.message}")
                }
            }
        }

        override fun onError(codec: MediaCodec, e: MediaCodec.CodecException) {
            AppLog.e("MediaCodec异步音频编码出错: ${e.message}", e)
            // 在错误情况下停止编码器
            if (isRunning.get()) {
                isRunning.set(false)
            }
        }

        override fun onOutputFormatChanged(codec: MediaCodec, format: MediaFormat) {
            AppLog.audio("异步音频编码器输出格式改变: $format")

            // 发送AAC配置数据
            if (!configurationSent) {
                sendConfigurationData(format)
                configurationSent = true
            }
        }
    }

    /**
     * 启动异步编码器
     */
    fun start(): Boolean {
        return safeExecute("启动AAC异步音频编码器") {
            AppLog.audio("启动AAC异步音频编码器: ${sampleRate}Hz, ${channelCount}ch, ${bitRate}bps")

            val format = MediaFormat.createAudioFormat(MIME_TYPE, sampleRate, channelCount).apply {
                setInteger(MediaFormat.KEY_BIT_RATE, bitRate)
                setInteger(MediaFormat.KEY_AAC_PROFILE, MediaCodecInfo.CodecProfileLevel.AACObjectLC)
                setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, 16384)

                // 低延迟模式配置
                if (lowLatencyMode) {
                    // 启用低延迟编码（最小SDK 24已支持优先级设置）
                    setInteger(MediaFormat.KEY_PRIORITY, 0) // 最高优先级

                    // 低延迟标志需要API 30+，使用字符串常量避免API检查警告
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
                        setInteger("low-latency", 1) // 启用低延迟
                    }
                }
            }

            val codec = MediaCodec.createEncoderByType(MIME_TYPE)

            try {
                // 🚀 设置异步回调 - 在configure之前设置
                codec.setCallback(MediaCodecCallback())
                codec.configure(format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE)

                // 只有在成功配置后才赋值给成员变量
                mediaCodec = codec
                isRunning.set(true)
                configurationSent = false

                // 🚀 启动异步编码器 - 系统将自动调用回调
                codec.start()

                AppLog.audio("AAC异步音频编码器启动成功")
                true
            } catch (e: Exception) {
                // 确保在异常情况下释放资源
                ResourceManager.safeReleaseMediaCodec(codec, "音频编码器")
                throw e
            }
        } ?: run {
            stop()
            false
        }
    }

    /**
     * 停止异步编码器
     */
    fun stop() {
        isRunning.set(false)
        configurationSent = false
        cachedConfigData = null

        // 🚀 关闭异步网络发送线程池
        try {
            networkExecutor.shutdown()
            if (!networkExecutor.awaitTermination(2, java.util.concurrent.TimeUnit.SECONDS)) {
                networkExecutor.shutdownNow()
                AppLog.audio("强制关闭音频编码异步网络发送线程池")
            }
        } catch (e: Exception) {
            AppLog.w("关闭音频编码异步网络发送线程池时出错: ${e.message}")
            networkExecutor.shutdownNow()
        }

        // 使用资源管理器安全释放MediaCodec资源
        ResourceManager.safeReleaseMediaCodec(mediaCodec, "异步音频编码器")
        mediaCodec = null

        // 清空队列
        inputQueue.clear()
        availableInputBuffers.clear()

        AppLog.audio("AAC异步音频编码器已停止")
    }

    /**
     * 输入PCM音频数据进行编码 - 异步模式
     */
    fun encode(pcmData: ByteArray) {
        if (isRunning.get() && pcmData.isNotEmpty()) {
            // 就地应用音量调节，避免额外内存分配
            if (volumeMultiplier != 1.0f) {
                applyVolumeAdjustmentInPlace(pcmData)
            }

            // 🚀 异步模式：添加到无锁队列
            inputQueue.offer(pcmData)

            // 🚀 立即尝试处理数据，如果有可用的输入缓冲区
            tryProcessPendingData()
        }
    }

    /**
     * 🚀 关键方法：尝试处理待处理的数据
     * 当新数据加入队列时，检查是否有可用的输入缓冲区并立即处理
     */
    private fun tryProcessPendingData() {
        if (!isRunning.get()) return

        val codec = mediaCodec ?: return

        // 尝试处理所有可用的输入缓冲区
        while (true) {
            val bufferIndex = availableInputBuffers.poll() ?: break
            if (!processInputData(codec, bufferIndex)) {
                // 如果没有数据处理，将缓冲区放回
                availableInputBuffers.offer(bufferIndex)
                break
            }
        }
    }

    /**
     * 处理输入数据
     * @return true 如果成功处理了数据，false 如果没有数据可处理
     */
    private fun processInputData(codec: MediaCodec, bufferIndex: Int): Boolean {
        val pcmData = inputQueue.poll() ?: return false

        try {
            val inputBuffer = codec.getInputBuffer(bufferIndex)
            if (inputBuffer != null) {
                inputBuffer.clear()
                inputBuffer.put(pcmData)

                codec.queueInputBuffer(
                    bufferIndex,
                    0,
                    pcmData.size,
                    System.nanoTime() / 1000,
                    0
                )
                return true
            }
        } catch (e: Exception) {
            AppLog.e("处理音频输入数据失败", e)
        }

        return false
    }

    /**
     * 处理配置数据
     */
    private fun handleConfigurationData(codec: MediaCodec, index: Int, info: MediaCodec.BufferInfo) {
        try {
            val outputBuffer = codec.getOutputBuffer(index)
            if (outputBuffer != null && info.size > 0) {
                val configData = ByteArray(info.size)
                outputBuffer.get(configData)

                // 缓存配置数据
                cachedConfigData = configData.copyOf()

                // 🐾 根源修复：只在第一次生成配置数据时发送，避免重复
                if (!configurationSent) {
                    // 🚀 架构优化：通过WebSocket发送配置数据，RTP仅用于媒体流
                    onConfigurationDataViaWebSocket?.invoke(configData)
                    configurationSent = true
                    AppLog.audio("通过WebSocket发送AAC配置数据: ${configData.size}字节")
                } else {
                    AppLog.audio("AAC配置数据已发送，跳过重复发送")
                }
            }
        } catch (e: Exception) {
            AppLog.e("处理AAC配置数据失败", e)
        } finally {
            codec.releaseOutputBuffer(index, false)
        }
    }

    /**
     * 处理编码后的数据 - 🚀 零拷贝优化版本
     */
    private fun handleEncodedData(codec: MediaCodec, index: Int, info: MediaCodec.BufferInfo) {
        try {
            val outputBuffer = codec.getOutputBuffer(index)
            if (outputBuffer != null && info.size > 0) {
                val isKeyFrame = (info.flags and MediaCodec.BUFFER_FLAG_KEY_FRAME) != 0

                // 🚀 零拷贝核心优化：使用引用计数管理缓冲区生命周期
                // 创建缓冲区引用，延迟释放MediaCodec缓冲区直到所有网络发送完成
                val bufferRef = AudioBufferReference(outputBuffer, codec, index, info.size)

                // 在后台线程中执行网络发送，使用零拷贝缓冲区视图
                networkExecutor.execute {
                    try {
                        // 直接传递只读缓冲区视图，完全避免数据拷贝
                        onEncodedData(bufferRef.dataView, info.size, isKeyFrame)
                    } catch (e: Exception) {
                        if (isRunning.get()) {
                            AppLog.e("异步音频数据发送失败", e)
                        }
                    } finally {
                        // 网络发送完成后释放缓冲区引用
                        bufferRef.release()
                    }
                }
            } else {
                // 如果没有有效数据，直接释放缓冲区
                codec.releaseOutputBuffer(index, false)
            }
        } catch (e: Exception) {
            AppLog.e("处理编码音频数据失败", e)
            // 确保在异常情况下也释放缓冲区
            try {
                codec.releaseOutputBuffer(index, false)
            } catch (releaseException: Exception) {
                AppLog.w("释放异常音频缓冲区时出错: ${releaseException.message}")
            }
        }
    }

    /**
     * 发送配置数据（从输出格式变更中提取）
     */
    private fun sendConfigurationData(format: MediaFormat) {
        try {
            // 提取AudioSpecificConfig
            val csd0 = format.getByteBuffer("csd-0")
            if (csd0 != null) {
                val configData = ByteArray(csd0.remaining())
                csd0.get(configData)

                // 缓存配置数据
                cachedConfigData = configData.copyOf()

                // 🐾 根源修复：只在第一次生成配置数据时发送，避免重复
                if (!configurationSent) {
                    // 🚀 架构优化：通过WebSocket发送配置数据，RTP仅用于媒体流
                    onConfigurationDataViaWebSocket?.invoke(configData)
                    configurationSent = true
                    AppLog.audio("通过WebSocket发送AAC配置数据: ${configData.size}字节")
                } else {
                    AppLog.audio("AAC配置数据已发送，跳过重复发送")
                }
            }
        } catch (e: Exception) {
            AppLog.e("发送AAC配置数据失败", e)
        }
    }

    /**
     * 🐾 根源修复：为新连接通过WebSocket发送缓存的配置数据，带去重检查
     */
    fun sendCachedConfigurationDataViaWebSocket(connectionId: String, onConfigData: (ByteArray) -> Unit) {
        try {
            // 🐾 根源修复：检查是否已向该连接发送过配置数据
            if (configDataSentToConnections.containsKey(connectionId)) {
                AppLog.audio("连接 $connectionId 已发送过AAC配置数据，跳过重复发送")
                return
            }

            val configData = cachedConfigData
            if (configData != null) {
                onConfigData(configData)
                // 🐾 根源修复：记录已发送状态
                configDataSentToConnections[connectionId] = true
                AppLog.audio("通过WebSocket发送缓存的AAC配置数据到连接 $connectionId: ${configData.size}字节")
            } else {
                AppLog.w("没有缓存的配置数据可发送到连接: $connectionId")
            }
        } catch (e: Exception) {
            AppLog.e("通过WebSocket发送缓存配置数据失败: $connectionId", e)
        }
    }

    /**
     * 获取编码器状态
     */
    fun isRunning(): Boolean = isRunning.get()

    /**
     * 🐾 根源修复：清理连接的配置数据发送状态
     */
    fun clearConnectionConfigState(connectionId: String) {
        configDataSentToConnections.remove(connectionId)
        AppLog.audio("已清理连接 $connectionId 的AAC配置数据发送状态")
    }

    /**
     * 更新音量（0.0-1.0）
     */
    fun updateVolume(volume: Float) {
        volumeMultiplier = volume.coerceIn(0.0f, 1.0f)
        AppLog.audio("音频编码器音量已更新: ${(volumeMultiplier * 100).toInt()}%")
    }

    /**
     * 就地应用音量调节到PCM数据 - 零拷贝版本
     */
    private fun applyVolumeAdjustmentInPlace(pcmData: ByteArray) {
        if (volumeMultiplier == 1.0f) return

        // 假设是16位PCM数据（小端序）
        for (i in pcmData.indices step 2) {
            if (i + 1 < pcmData.size) {
                // 读取16位样本（小端序）
                val sample = (pcmData[i].toInt() and 0xFF) or
                           ((pcmData[i + 1].toInt() and 0xFF) shl 8)

                // 转换为有符号16位整数
                val signedSample = if (sample > 32767) sample - 65536 else sample

                // 应用音量调节
                val adjustedSample = (signedSample * volumeMultiplier).toInt()
                    .coerceIn(-32768, 32767)

                // 转换回无符号16位并就地写入原数组（小端序）
                val unsignedSample = if (adjustedSample < 0) adjustedSample + 65536 else adjustedSample
                pcmData[i] = (unsignedSample and 0xFF).toByte()
                pcmData[i + 1] = ((unsignedSample shr 8) and 0xFF).toByte()
            }
        }
    }
}
