package com.example.castapp.audio

import java.util.concurrent.ConcurrentHashMap
import kotlin.math.abs
import com.example.castapp.utils.AppLog

/**
 * 音频同步管理器
 * 负责管理音频流的时间同步和延迟补偿
 */
class AudioSyncManager {
    companion object {        
        // 目标延迟（毫秒）
        private const val TARGET_LATENCY_MS = 50L // 50ms目标延迟
        
        // 延迟调整阈值
        private const val LATENCY_ADJUSTMENT_THRESHOLD_MS = 20L // 20ms阈值
        
        // 最大允许延迟
        private const val MAX_ALLOWED_LATENCY_MS = 200L // 200ms最大延迟
        
        // 🚀 CPU优化：增加统计间隔，减少日志频率
        private const val STATS_INTERVAL_MS = 30000L // 30秒
    }
    
    // 每个连接的同步信息
    private val connectionSyncInfo = ConcurrentHashMap<String, SyncInfo>()
    

    // 统计信息
    private var lastStatsTime = 0L
    
    data class SyncInfo(
        val connectionId: String,
        var lastPacketTime: Long = 0L,
        var averageLatency: Long = 0L,
        var latencyVariance: Long = 0L,
        var packetsReceived: Int = 0,
        var packetsDropped: Int = 0,
        var lastAdjustmentTime: Long = 0L
    )
    
    /**
     * 记录音频包接收时间
     */
    fun recordPacketReceived(connectionId: String, receiveTime: Long = System.currentTimeMillis()) {
        val syncInfo = connectionSyncInfo.getOrPut(connectionId) { 
            SyncInfo(connectionId).also {
                AppLog.audio("为连接 $connectionId 创建同步信息")
            }
        }
        
        synchronized(syncInfo) {
            val previousTime = syncInfo.lastPacketTime
            syncInfo.lastPacketTime = receiveTime
            syncInfo.packetsReceived++

            // 计算延迟（简化版本，基于接收时间差）
            if (syncInfo.packetsReceived > 1 && previousTime > 0) {
                val expectedInterval = 23L // AAC帧间隔约23ms (1024样本/48kHz)
                val actualInterval = receiveTime - previousTime
                val latency = abs(actualInterval - expectedInterval)

                // 更新平均延迟
                syncInfo.averageLatency = if (syncInfo.packetsReceived == 2) {
                    latency
                } else {
                    (syncInfo.averageLatency * 0.9 + latency * 0.1).toLong()
                }

                // 检查是否需要调整
                checkAndAdjustLatency(syncInfo)
            }
        }
        
        logStatsIfNeeded()
    }
    
    /**
     * 检查并调整延迟
     */
    private fun checkAndAdjustLatency(syncInfo: SyncInfo) {
        val currentTime = System.currentTimeMillis()
        
        // 避免频繁调整
        if (currentTime - syncInfo.lastAdjustmentTime < 1000) {
            return
        }
        
        when {
            syncInfo.averageLatency > MAX_ALLOWED_LATENCY_MS -> {
                // 延迟严重超标，重置连接（优先级最高）
                AppLog.w("连接 ${syncInfo.connectionId} 延迟严重超标: ${syncInfo.averageLatency}ms，建议重置")
                resetConnectionSync(syncInfo.connectionId)
            }

            syncInfo.averageLatency > TARGET_LATENCY_MS + LATENCY_ADJUSTMENT_THRESHOLD_MS -> {
                // 🚀 CPU优化：移除高频延迟过高日志，减少CPU占用
                // AppLog.audio("连接 ${syncInfo.connectionId} 延迟过高: ${syncInfo.averageLatency}ms，建议丢弃包")
                syncInfo.lastAdjustmentTime = currentTime
            }

            syncInfo.averageLatency < TARGET_LATENCY_MS - LATENCY_ADJUSTMENT_THRESHOLD_MS -> {
                // 🚀 CPU优化：移除高频延迟调整日志，减少CPU占用
                // AppLog.audio("连接 ${syncInfo.connectionId} 延迟过低: ${syncInfo.averageLatency}ms，建议增加缓冲")
                syncInfo.lastAdjustmentTime = currentTime
            }
        }
    }
    
    /**
     * 判断是否应该丢弃音频包以减少延迟
     */
    fun shouldDropPacket(connectionId: String): Boolean {
        val syncInfo = connectionSyncInfo[connectionId] ?: return false
        
        synchronized(syncInfo) {
            // 如果延迟过高且最近没有丢包，则建议丢包
            val shouldDrop = syncInfo.averageLatency > TARGET_LATENCY_MS + LATENCY_ADJUSTMENT_THRESHOLD_MS &&
                    System.currentTimeMillis() - syncInfo.lastAdjustmentTime > 100 // 100ms内最多丢一次
            
            if (shouldDrop) {
                syncInfo.packetsDropped++
                syncInfo.lastAdjustmentTime = System.currentTimeMillis()
                // 🚀 CPU优化：移除高频丢包建议日志，减少CPU占用
            }
            
            return shouldDrop
        }
    }

    /**
     * 重置连接的同步信息
     */
    fun resetConnectionSync(connectionId: String) {
        connectionSyncInfo.remove(connectionId)
        AppLog.audio("重置连接 $connectionId 的同步信息")
    }
    
    /**
     * 清理所有同步信息
     */
    fun clearAll() {
        connectionSyncInfo.clear()
        AppLog.audio("清理所有音频同步信息")
    }
    
    /**
     * 定期输出统计信息
     */
    private fun logStatsIfNeeded() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastStatsTime >= STATS_INTERVAL_MS) {
            if (connectionSyncInfo.isNotEmpty()) {
                AppLog.audio("音频同步统计:")
                connectionSyncInfo.values.forEach { syncInfo ->
                    synchronized(syncInfo) {
                        val dropRate = if (syncInfo.packetsReceived > 0) {
                            (syncInfo.packetsDropped * 100.0 / syncInfo.packetsReceived)
                        } else 0.0
                        
                        AppLog.audio("  连接 ${syncInfo.connectionId}: 延迟=${syncInfo.averageLatency}ms, " +
                                "接收=${syncInfo.packetsReceived}包, 丢弃=${syncInfo.packetsDropped}包 " +
                                "(${String.format("%.1f%%", dropRate)})")
                    }
                }
            }
            lastStatsTime = currentTime
        }
    }
}
