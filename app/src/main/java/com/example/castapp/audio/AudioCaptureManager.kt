package com.example.castapp.audio

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.projection.MediaProjection
import android.os.Build
import androidx.core.app.ActivityCompat
import com.example.castapp.utils.ResourceManager
import java.util.concurrent.atomic.AtomicBoolean
import com.example.castapp.utils.AppLog

/**
 * 音频捕获管理器
 * 负责麦克风和媒体音频的捕获
 */
class AudioCaptureManager(private val context: Context) {
    companion object {
        // 音频参数
        const val SAMPLE_RATE = 48000 // 48kHz采样率
        const val MEDIA_CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_STEREO // 媒体音频立体声
        const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT // 16位PCM
    }

    // 麦克风录音器
    private var micAudioRecord: AudioRecord? = null
    private var mediaAudioRecord: AudioRecord? = null

    // 运行状态
    private val isMicCapturing = AtomicBoolean(false)
    private val isMediaCapturing = AtomicBoolean(false)

    // 捕获线程
    private var micCaptureThread: Thread? = null
    private var mediaCaptureThread: Thread? = null

    // 数据回调
    private var onMicDataCallback: ((ByteArray) -> Unit)? = null
    private var onMediaDataCallback: ((ByteArray) -> Unit)? = null

    // 🔥 新增：麦克风连接ID（用于MicrophoneManager）
    private var connectionId: String? = null

    /**
     * 开始麦克风音频捕获 - 🔥 修改：使用MicrophoneManager
     */
    fun startMicCapture(onDataCallback: (ByteArray) -> Unit): Boolean {
        if (isMicCapturing.get()) {
            AppLog.w("麦克风捕获已在运行")
            return true
        }

        // 检查权限
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO)
            != PackageManager.PERMISSION_GRANTED) {
            AppLog.e("缺少录音权限")
            return false
        }

        // 🔥 新方案：使用MicrophoneManager预创建的实例
        return startMicCaptureWithManager(onDataCallback)
    }

    /**
     * 🔥 新增：使用MicrophoneManager启动麦克风捕获
     */
    private fun startMicCaptureWithManager(onDataCallback: (ByteArray) -> Unit): Boolean {
        try {
            val microphoneManager = com.example.castapp.manager.MicrophoneManager.getInstance(context)

            if (!microphoneManager.isMicrophoneReady()) {
                AppLog.e("MicrophoneManager未准备就绪，无法启动麦克风捕获")
                return false
            }

            // 生成连接ID（用于标识这次捕获）
            connectionId = "AudioCaptureManager_${System.currentTimeMillis()}"
            onMicDataCallback = onDataCallback

            // 使用MicrophoneManager启动捕获
            val success = microphoneManager.startCapture(connectionId!!) { pcmData ->
                onMicDataCallback?.invoke(pcmData)
            }

            if (success) {
                isMicCapturing.set(true)
                AppLog.audio("麦克风音频捕获启动成功（使用MicrophoneManager）")
                return true
            } else {
                AppLog.e("MicrophoneManager启动捕获失败")
                connectionId = null
                onMicDataCallback = null
                return false
            }

        } catch (e: Exception) {
            AppLog.e("使用MicrophoneManager启动麦克风捕获失败", e)
            connectionId = null
            onMicDataCallback = null
            return false
        }
    }

    /**
     * 开始媒体音频捕获（需要MediaProjection）
     */
    fun startMediaCapture(mediaProjection: MediaProjection?, onDataCallback: (ByteArray) -> Unit): Boolean {
        if (isMediaCapturing.get()) {
            AppLog.w("媒体音频捕获已在运行")
            return true
        }

        if (mediaProjection == null) {
            AppLog.e("MediaProjection为空，无法捕获媒体音频")
            return false
        }

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            AppLog.e("媒体音频捕获需要Android 10+")
            return false
        }

        // 检查录音权限 - 媒体音频捕获也需要RECORD_AUDIO权限
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO)
            != PackageManager.PERMISSION_GRANTED) {
            AppLog.e("缺少录音权限，无法进行媒体音频捕获")
            return false
        }

        try {
            val bufferSize = AudioRecord.getMinBufferSize(
                SAMPLE_RATE,
                MEDIA_CHANNEL_CONFIG,
                AUDIO_FORMAT
            )

            if (bufferSize == AudioRecord.ERROR || bufferSize == AudioRecord.ERROR_BAD_VALUE) {
                AppLog.e("无法获取媒体音频缓冲区大小")
                return false
            }

            // 创建AudioPlaybackCaptureConfiguration（已确保Android 10+）
            val captureConfig = try {
                android.media.AudioPlaybackCaptureConfiguration.Builder(mediaProjection)
                    .addMatchingUsage(android.media.AudioAttributes.USAGE_MEDIA)
                    .addMatchingUsage(android.media.AudioAttributes.USAGE_GAME)
                    .addMatchingUsage(android.media.AudioAttributes.USAGE_UNKNOWN)
                    .build()
            } catch (e: Exception) {
                AppLog.e("无法创建AudioPlaybackCaptureConfiguration", e)
                return false
            }

            // 创建AudioRecord用于媒体音频捕获（已确保Android 10+）
            mediaAudioRecord = try {
                AudioRecord.Builder()
                    .setAudioFormat(
                        AudioFormat.Builder()
                            .setEncoding(AUDIO_FORMAT)
                            .setSampleRate(SAMPLE_RATE)
                            .setChannelMask(MEDIA_CHANNEL_CONFIG)
                            .build()
                    )
                    .setBufferSizeInBytes(bufferSize * 2)
                    .setAudioPlaybackCaptureConfig(captureConfig)
                    .build()
            } catch (e: SecurityException) {
                AppLog.e("创建媒体音频AudioRecord失败：权限被拒绝", e)
                return false
            } catch (e: Exception) {
                AppLog.e("创建媒体音频AudioRecord失败", e)
                return false
            }

            if (mediaAudioRecord?.state != AudioRecord.STATE_INITIALIZED) {
                AppLog.e("媒体音频AudioRecord初始化失败")
                mediaAudioRecord?.release()
                mediaAudioRecord = null
                return false
            }

            onMediaDataCallback = onDataCallback
            isMediaCapturing.set(true)

            // 启动录音
            try {
                mediaAudioRecord?.startRecording()
            } catch (e: SecurityException) {
                AppLog.e("启动媒体音频录音失败：权限被拒绝", e)
                stopMediaCapture()
                return false
            } catch (e: Exception) {
                AppLog.e("启动媒体音频录音失败", e)
                stopMediaCapture()
                return false
            }

            // 启动捕获线程 - 🚀 修复投屏时音频卡顿：设置高优先级
            mediaCaptureThread = Thread {
                // 🚀 关键修复：设置音频捕获线程为高优先级，避免被投屏启动影响
                android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_URGENT_AUDIO)
                captureMediaAudio(bufferSize)
            }.apply {
                name = "MediaCaptureThread"
                priority = Thread.MAX_PRIORITY // Java层面也设置最高优先级
                start()
            }

            AppLog.audio("媒体音频捕获启动成功")
            return true

        } catch (e: Exception) {
            AppLog.e("启动媒体音频捕获失败", e)
            stopMediaCapture()
            return false
        }
    }

    /**
     * 媒体音频捕获线程 - 🚀 零拷贝优化版本
     */
    private fun captureMediaAudio(bufferSize: Int) {
        val buffer = ByteArray(bufferSize)
        val bufferPool = AudioBufferPool.getInstance()

        while (isMediaCapturing.get()) {
            try {
                val bytesRead = mediaAudioRecord?.read(buffer, 0, buffer.size) ?: 0

                if (bytesRead > 0) {
                    // 🚀 零拷贝优化：智能缓冲区重用策略
                    val audioData = getOptimalCaptureBuffer(buffer, bytesRead, bufferPool)
                    onMediaDataCallback?.invoke(audioData)
                } else if (bytesRead < 0) {
                    AppLog.w("媒体音频读取错误: $bytesRead")
                    break
                }

            } catch (e: Exception) {
                AppLog.e("媒体音频捕获异常", e)
                break
            }
        }

        AppLog.audio("媒体音频捕获线程结束")
    }

    /**
     * 🚀 零拷贝音频缓冲区：智能捕获缓冲区重用策略
     */
    private fun getOptimalCaptureBuffer(sourceBuffer: ByteArray, actualSize: Int, bufferPool: AudioBufferPool): ByteArray {
        return when {
            // 完整缓冲区：直接使用原缓冲区，零拷贝
            actualSize == sourceBuffer.size -> sourceBuffer

            // 大部分数据（90%以上）：直接使用原缓冲区，避免拷贝
            actualSize >= sourceBuffer.size * 0.9 -> sourceBuffer

            // 中等大小数据：使用缓冲区池，减少GC压力
            actualSize >= 1024 -> {
                val pooledBuffer = bufferPool.getBuffer(actualSize)
                sourceBuffer.copyInto(pooledBuffer, 0, 0, actualSize)
                pooledBuffer
            }

            // 小数据：直接切片，避免池化开销
            else -> sourceBuffer.copyOfRange(0, actualSize)
        }
    }
    
    /**
     * 停止麦克风捕获 - 🔥 修改：使用MicrophoneManager
     */
    fun stopMicCapture() {
        isMicCapturing.set(false)

        // 🔥 新方案：使用MicrophoneManager停止捕获
        connectionId?.let { id ->
            try {
                val microphoneManager = com.example.castapp.manager.MicrophoneManager.getInstance(context)
                microphoneManager.stopCapture(id)
                AppLog.audio("麦克风捕获已停止（使用MicrophoneManager）")
            } catch (e: Exception) {
                AppLog.e("使用MicrophoneManager停止麦克风捕获失败", e)
            }
        }

        // 清理状态
        connectionId = null
        onMicDataCallback = null

        // 🔥 备用：清理原有AudioRecord资源（如果存在）
        stopMicCaptureWithAudioRecord()
    }

    /**
     * 🔥 备用方案：停止AudioRecord捕获
     */
    private fun stopMicCaptureWithAudioRecord() {
        // 安全停止捕获线程
        micCaptureThread?.let { thread ->
            try {
                thread.interrupt()
                thread.join(1000)
                AppLog.audio("麦克风捕获线程已停止")
            } catch (e: Exception) {
                AppLog.w("停止麦克风捕获线程时发生异常", e)
            }
        }
        micCaptureThread = null

        // 安全释放AudioRecord资源
        micAudioRecord?.let { audioRecord ->
            safeReleaseAudioRecord(audioRecord, "麦克风")
        }
        micAudioRecord = null
    }

    /**
     * 停止媒体音频捕获
     */
    fun stopMediaCapture() {
        isMediaCapturing.set(false)

        // 安全停止捕获线程
        mediaCaptureThread?.let { thread ->
            try {
                thread.interrupt()
                thread.join(1000)
                AppLog.audio("媒体音频捕获线程已停止")
            } catch (e: Exception) {
                AppLog.w("停止媒体音频捕获线程时发生异常", e)
            }
        }
        mediaCaptureThread = null

        // 安全释放AudioRecord资源
        mediaAudioRecord?.let { audioRecord ->
            safeReleaseAudioRecord(audioRecord, "媒体音频")
        }
        mediaAudioRecord = null

        onMediaDataCallback = null
        AppLog.audio("媒体音频捕获已停止")
    }

    /**
     * 安全释放AudioRecord资源
     */
    private fun safeReleaseAudioRecord(audioRecord: AudioRecord, type: String) {
        ResourceManager.safeReleaseAudioRecord(audioRecord, type)
    }

    /**
     * 停止所有音频捕获
     */
    fun stopAll() {
        stopMicCapture()
        stopMediaCapture()
    }

    /**
     * 检查是否正在捕获
     */
    fun isMicCapturing(): Boolean = isMicCapturing.get()
    fun isMediaCapturing(): Boolean = isMediaCapturing.get()
}
