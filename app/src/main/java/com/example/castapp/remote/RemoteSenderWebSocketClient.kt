package com.example.castapp.remote

import com.example.castapp.utils.AppLog
import com.example.castapp.websocket.ControlMessage
import com.example.castapp.websocket.WebSocketClient
import java.net.URI
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 远程WebSocket客户端
 * 用于连接到发送端的9999端口进行控制
 */
class RemoteSenderWebSocketClient(
    private val targetIp: String,
    private val targetPort: Int,
    private val deviceName: String,
    private val onMessageReceived: (ControlMessage) -> Unit,
    private val onConnectionStateChanged: (Boolean) -> Unit
) {

    private var webSocketClient: WebSocketClient? = null
    private val isConnected = AtomicBoolean(false)
    private val connectionId = "remote_control_${System.currentTimeMillis()}"

    // 🚀 新增：连接状态跟踪，确保消息发送顺序
    private val isConnectionResponseReceived = AtomicBoolean(false)
    private val shouldSendRemoteControlRequest = AtomicBoolean(false)
    
    /**
     * 连接到远程设备
     */
    fun connect(): Boolean {
        if (isConnected.get()) {
            AppLog.d("远程WebSocket客户端已连接")
            return true
        }
        
        return try {
            val uri = URI("ws://$targetIp:$targetPort")
            AppLog.d("连接到远程设备: $targetIp:$targetPort")
            
            webSocketClient = WebSocketClient(
                serverUri = uri,
                connectionId = connectionId,
                onMessageReceived = { message ->
                    AppLog.d("收到远程设备消息: ${message.type}")

                    // 🚀 优化：处理连接响应，确保消息发送顺序
                    if (message.type == ControlMessage.TYPE_CONNECTION_RESPONSE) {
                        handleConnectionResponse(message)
                    }

                    onMessageReceived(message)
                },
                onConnectionStateChanged = { connected ->
                    isConnected.set(connected)
                    AppLog.d("远程WebSocket连接状态变化: $connected")
                    onConnectionStateChanged(connected)

                    if (connected) {
                        // 🚀 优化：连接成功后标记需要发送远程控制请求，但等待connection_response
                        shouldSendRemoteControlRequest.set(true)
                        AppLog.d("WebSocket连接已建立，等待connection_response后发送remote_control_request")
                    } else {
                        // 连接断开时重置状态
                        isConnectionResponseReceived.set(false)
                        shouldSendRemoteControlRequest.set(false)
                    }
                }
            )
            
            webSocketClient?.connectToServer() ?: false
            
        } catch (e: Exception) {
            AppLog.e("连接远程设备失败", e)
            false
        }
    }
    
    /**
     * 断开连接
     */
    fun disconnect() {
        try {
            // 🔧 修复：使用WebSocketClient的disconnect方法，确保资源正确释放
            webSocketClient?.disconnect()
            webSocketClient = null
            isConnected.set(false)
            AppLog.d("已断开远程WebSocket连接")
        } catch (e: Exception) {
            AppLog.e("断开远程WebSocket连接失败", e)
        }
    }


    
    /**
     * 🚀 新增：处理连接响应，确保消息发送顺序
     */
    private fun handleConnectionResponse(message: ControlMessage) {
        val success = message.getBooleanData("success") ?: false
        if (success) {
            isConnectionResponseReceived.set(true)
            AppLog.d("收到connection_response成功响应，连接映射已建立")

            // 如果之前标记需要发送远程控制请求，现在发送
            if (shouldSendRemoteControlRequest.get()) {
                sendRemoteControlRequest()
                shouldSendRemoteControlRequest.set(false)

                // 📐 优化：连接建立后立即请求屏幕分辨率信息进行缓存
                sendScreenResolutionRequestForCache()
            }
        } else {
            AppLog.w("收到connection_response失败响应: ${message.getStringData("message")}")
        }
    }

    /**
     * 发送远程控制请求
     */
    private fun sendRemoteControlRequest() {
        AppLog.d("发送远程控制请求，连接映射已确认建立")
        val message = ControlMessage.createRemoteControlRequest(connectionId, deviceName)
        sendMessage(message)
    }
    
    /**
     * 发送消息
     */
    fun sendMessage(message: ControlMessage): Boolean {
        return webSocketClient?.sendMessage(message) ?: false
    }
    
    /**
     * 发送远程码率变更
     */
    fun sendBitrateChange(bitrateMbps: Int): Boolean {
        val message = ControlMessage.createRemoteBitrateChange(connectionId, bitrateMbps)
        return sendMessage(message)
    }
    
    /**
     * 发送远程分辨率变更
     */
    fun sendResolutionChange(scalePercent: Int): Boolean {
        val message = ControlMessage.createRemoteResolutionChange(connectionId, scalePercent)
        return sendMessage(message)
    }
    
    /**
     * 发送远程音量变更
     */
    fun sendVolumeChange(volumeType: String, volume: Int): Boolean {
        val message = ControlMessage.createRemoteVolumeChange(connectionId, volumeType, volume)
        return sendMessage(message)
    }

    /**
     * 📐 发送屏幕分辨率请求（用于缓存）
     */
    private fun sendScreenResolutionRequestForCache() {
        AppLog.d("📐 连接建立后自动请求屏幕分辨率进行缓存")
        val message = ControlMessage.createScreenResolutionRequest(connectionId)
        sendMessage(message)
    }


    /**
     * 发送特定连接的远程切换
     */
    fun sendSpecificConnectionToggle(targetConnectionId: String, functionType: String, enabled: Boolean): Boolean {
        val message = ControlMessage.createRemoteConnectionToggle(connectionId, functionType, enabled, targetConnectionId)
        return sendMessage(message)
    }

    /**
     * 发送添加连接请求
     */
    fun sendAddConnectionRequest(ipAddress: String, port: Int): Boolean {
        val message = ControlMessage.createRemoteConnectionAddRequest(connectionId, ipAddress, port)
        return sendMessage(message)
    }

    /**
     * 发送编辑连接请求
     */
    fun sendEditConnectionRequest(targetConnectionId: String, newIpAddress: String, newPort: Int): Boolean {
        val message = ControlMessage.createRemoteConnectionEditRequest(connectionId, targetConnectionId, newIpAddress, newPort)
        return sendMessage(message)
    }

    /**
     * 发送删除连接请求
     */
    fun sendDeleteConnectionRequest(targetConnectionId: String): Boolean {
        val message = ControlMessage.createRemoteConnectionDeleteRequest(connectionId, targetConnectionId)
        return sendMessage(message)
    }
    
    /**
     * 发送设置同步请求
     */
    fun requestSettingsSync(): Boolean {
        val message = ControlMessage.createRemoteSettingsSync(connectionId, mapOf("action" to "request_sync"))
        return sendMessage(message)
    }
    
    /**
     * 检查连接状态
     */
    fun getConnectionStatus(): Boolean = isConnected.get()

    /**
     * 🔧 新增：获取WebSocket连接的实际连接ID
     */
    fun getActualConnectionId(): String {
        return connectionId
    }

}
