package com.example.castapp.remote

import android.content.Context
import android.content.Intent
import android.graphics.RectF
import android.media.AudioManager
import androidx.core.content.edit
import com.example.castapp.manager.StateManager
import com.example.castapp.service.ReceivingService
import com.example.castapp.utils.AppLog
import com.example.castapp.websocket.ControlMessage
import com.example.castapp.websocket.WebSocketServer
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 🎛️ 远程接收端控制服务器
 * 独立管理7777端口WebSocket连接，专门处理远程接收端设置控制
 * 完全独立于音视频服务的生命周期
 * 
 * 主要功能：
 * - 管理7777端口WebSocket服务器
 * - 处理远程接收端设置控制消息
 * - 提供持续的双向控制同步
 * - 消息缓存和连接管理
 */
class RemoteReceiverControlServer private constructor() {

    companion object {
        @Volatile
        private var INSTANCE: RemoteReceiverControlServer? = null
        
        // 固定端口7777
        const val REMOTE_RECEIVER_CONTROL_PORT = 7777
        
        fun getInstance(): RemoteReceiverControlServer {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: RemoteReceiverControlServer().also { INSTANCE = it }
            }
        }
    }

    // WebSocket服务器
    private var webSocketServer: WebSocketServer? = null
    
    // 运行状态
    private val isRunning = AtomicBoolean(false)
    
    // 连接管理
    private val remoteControlConnections = ConcurrentHashMap.newKeySet<String>()
    private val connectionMappingEstablished = ConcurrentHashMap.newKeySet<String>()
    private val pendingMessages = ConcurrentHashMap<String, MutableList<ControlMessage>>()

    // 🔧 修复：WebSocket连接ID到实际连接ID的映射
    private val webSocketToActualConnectionMapping = ConcurrentHashMap<String, String>()
    
    // 应用上下文（用于跨服务通信）
    private var applicationContext: Context? = null
    
    // 回调接口
    private var onConnectionStateChanged: ((Boolean) -> Unit)? = null

    /**
     * 设置应用上下文
     */
    fun setApplicationContext(context: Context) {
        this.applicationContext = context.applicationContext
    }

    /**
     * 启动远程控制服务器
     */
    fun startServer(): Boolean {
        if (isRunning.get()) {
            AppLog.d("【远程控制服务器】服务器已在运行")
            return true
        }

        return try {
            AppLog.d("【远程控制服务器】启动服务器，端口: $REMOTE_RECEIVER_CONTROL_PORT")

            webSocketServer = WebSocketServer(
                port = REMOTE_RECEIVER_CONTROL_PORT,
                onMessageReceived = { message ->
                    AppLog.d("【远程控制服务器】收到消息: ${message.type}")
                    handleRemoteControlMessage(message)
                },
                onConnectionRequest = { connectionId, clientIP, clientPort, deviceName ->
                    AppLog.d("【远程控制服务器】连接请求: $connectionId from $clientIP:$clientPort ($deviceName)")
                    handleConnectionRequest(connectionId, clientIP, clientPort, deviceName)
                },
                serverType = "remote_control" // 标识为远程控制服务器
            )

            webSocketServer?.start()
            isRunning.set(true)
            onConnectionStateChanged?.invoke(true)

            AppLog.d("【远程控制服务器】服务器启动成功")
            true

        } catch (e: Exception) {
            AppLog.e("【远程控制服务器】启动服务器失败", e)
            stopServer()
            false
        }
    }

    /**
     * 停止远程控制服务器
     */
    fun stopServer() {
        if (!isRunning.get()) {
            AppLog.d("【远程控制服务器】服务器未运行")
            return
        }

        try {
            AppLog.d("【远程控制服务器】停止服务器")

            // 发送远程被控服务停止消息
            webSocketServer?.let { server ->
                val activeConnections = server.getActiveConnectionIds()
                AppLog.d("【远程控制服务器】向 ${activeConnections.size} 个连接发送服务停止通知")

                activeConnections.forEach { connectionId ->
                    try {
                        val serviceStoppedMessage = ControlMessage.createRemoteControlServiceStopped(
                            connectionId, 
                            "远程控制服务关闭"
                        )
                        server.sendMessageToClient(connectionId, serviceStoppedMessage)
                        AppLog.d("【远程控制服务器】已向连接 $connectionId 发送服务停止通知")
                    } catch (e: Exception) {
                        AppLog.e("【远程控制服务器】通知连接 $connectionId 失败", e)
                    }
                }

                // 给客户端时间处理消息
                try {
                    Thread.sleep(150)
                    AppLog.d("【远程控制服务器】已等待客户端处理消息")
                } catch (_: InterruptedException) {
                    AppLog.w("【远程控制服务器】等待被中断")
                }
            }

            // 停止WebSocket服务器
            webSocketServer?.stopServer()
            webSocketServer = null
            isRunning.set(false)

            // 清理连接状态
            remoteControlConnections.clear()
            connectionMappingEstablished.clear()
            pendingMessages.clear()

            onConnectionStateChanged?.invoke(false)

            AppLog.d("【远程控制服务器】服务器已停止")

        } catch (e: Exception) {
            AppLog.e("【远程控制服务器】停止服务器失败", e)
        }
    }

    /**
     * 处理连接请求
     */
    private fun handleConnectionRequest(connectionId: String, clientIP: String, clientPort: Int, deviceName: String?) {
        AppLog.d("【远程控制服务器】处理连接请求: $connectionId from $clientIP:$clientPort ($deviceName)")

        // 标记为遥控连接
        remoteControlConnections.add(connectionId)
        AppLog.d("【远程控制服务器】已标记为遥控连接: $connectionId")

        // 🔧 修复：建立WebSocket连接ID映射，允许任何connectionId都能找到对应的WebSocket连接
        webSocketToActualConnectionMapping[connectionId] = connectionId
        AppLog.d("【远程控制服务器】建立WebSocket连接映射: $connectionId -> $connectionId")

        // 标记连接映射已建立
        connectionMappingEstablished.add(connectionId)
        AppLog.d("【远程控制服务器】连接映射已建立: $connectionId")

        // 处理缓存的消息
        processCachedMessages(connectionId)

        AppLog.d("【远程控制服务器】连接请求处理完成: $connectionId")
    }

    /**
     * 处理缓存的消息
     */
    private fun processCachedMessages(connectionId: String) {
        val cachedMessages = pendingMessages.remove(connectionId)
        if (cachedMessages != null && cachedMessages.isNotEmpty()) {
            AppLog.d("【远程控制服务器】处理 ${cachedMessages.size} 个缓存消息 for $connectionId")
            cachedMessages.forEach { message ->
                AppLog.d("【远程控制服务器】处理缓存消息: ${message.type} for $connectionId")
                handleRemoteControlMessage(message)
            }
        } else {
            AppLog.d("【远程控制服务器】无缓存消息需要处理 for $connectionId")
        }
    }

    /**
     * 处理远程控制消息
     */
    private fun handleRemoteControlMessage(controlMessage: ControlMessage) {
        AppLog.d("【远程控制服务器】处理消息: ${controlMessage.type}, 连接ID: ${controlMessage.connectionId}")

        // 🔧 修复：检查是否有任何已建立的连接可以处理这个消息
        val hasActiveConnection = connectionMappingEstablished.isNotEmpty()

        if (!hasActiveConnection) {
            AppLog.d("【远程控制服务器】无活跃连接，缓存消息: ${controlMessage.type}")
            pendingMessages.computeIfAbsent(controlMessage.connectionId) { mutableListOf() }.add(controlMessage)
            return
        }

        AppLog.d("【远程控制服务器】找到活跃连接，处理消息: ${controlMessage.type}")

        when (controlMessage.type) {
            ControlMessage.TYPE_CONNECTION_REQUEST -> {
                handleConnectionRequestMessage(controlMessage)
            }
            ControlMessage.TYPE_REMOTE_CONTROL_REQUEST -> {
                handleRemoteControlRequestMessage(controlMessage)
            }
            ControlMessage.TYPE_HEARTBEAT -> {
                handleHeartbeatMessage(controlMessage)
            }
            ControlMessage.TYPE_REMOTE_RECEIVER_AUDIO_VIDEO_TOGGLE -> {
                handleRemoteReceiverAudioVideoToggle(controlMessage)
            }
            ControlMessage.TYPE_REMOTE_RECEIVER_PLAYBACK_MODE_CHANGE -> {
                handleRemoteReceiverPlaybackModeChange(controlMessage)
            }
            ControlMessage.TYPE_REMOTE_RECEIVER_VOLUME_CHANGE -> {
                handleRemoteReceiverVolumeChange(controlMessage)
            }
            ControlMessage.TYPE_REMOTE_RECEIVER_SETTINGS_REQUEST -> {
                handleRemoteReceiverSettingsRequest(controlMessage)
            }
            ControlMessage.TYPE_WINDOW_MANAGER_REQUEST -> {
                handleWindowManagerRequest(controlMessage)
            }
            ControlMessage.TYPE_SCREENSHOT_REQUEST -> {
                handleScreenshotRequest(controlMessage)
            }
            ControlMessage.TYPE_SCREEN_RESOLUTION_REQUEST -> {
                handleScreenResolutionRequest(controlMessage)
            }
            ControlMessage.TYPE_REMOTE_WINDOW_TRANSFORM_CONTROL -> {
                handleRemoteWindowTransformControl(controlMessage)
            }
            else -> {
                AppLog.d("【远程控制服务器】未处理的消息类型: ${controlMessage.type}")
            }
        }
    }

    /**
     * 广播消息到所有连接的遥控端
     */
    fun broadcastMessage(message: ControlMessage) {
        webSocketServer?.broadcastMessage(message)
    }


    // ========== 🔧 基础消息处理方法 ==========

    /**
     * 🔧 处理连接请求消息（在消息处理阶段）
     */
    private fun handleConnectionRequestMessage(controlMessage: ControlMessage) {
        AppLog.d("【远程控制服务器】处理连接请求消息: ${controlMessage.connectionId}")

        // 连接请求已在onConnectionRequest回调中处理，这里只需记录
        val deviceName = controlMessage.getStringData("device_name")
        AppLog.d("【远程控制服务器】连接请求消息处理完成: $deviceName")

        // 发送连接响应确认
        sendConnectionResponse(controlMessage.connectionId)
    }

    /**
     * 🔧 处理远程控制请求消息
     */
    private fun handleRemoteControlRequestMessage(controlMessage: ControlMessage) {
        AppLog.d("【远程控制服务器】处理远程控制请求消息: ${controlMessage.connectionId}")

        val deviceName = controlMessage.getStringData("device_name")
        AppLog.d("【远程控制服务器】远程控制请求来自设备: $deviceName")

        // 远程控制请求处理完成，可以在这里添加额外的初始化逻辑
        AppLog.d("【远程控制服务器】远程控制请求处理完成")
    }

    /**
     * 🔧 处理心跳消息
     */
    private fun handleHeartbeatMessage(controlMessage: ControlMessage) {
        // 心跳消息通常不需要特殊处理，只需要记录即可
        // 这里可以用来维护连接活跃状态
        AppLog.v("【远程控制服务器】收到心跳消息: ${controlMessage.connectionId}")

        // 可选：发送心跳响应（如果需要的话）
        // sendHeartbeatResponse(controlMessage.connectionId)
    }

    /**
     * 🔧 发送连接响应消息
     */
    private fun sendConnectionResponse(connectionId: String) {
        try {
            webSocketServer?.let { server ->
                val responseMessage = ControlMessage.createConnectionResponse(connectionId, true, "连接成功")
                server.sendMessageToClient(connectionId, responseMessage)
                AppLog.d("【远程控制服务器】连接响应已发送: 连接成功")
            } ?: run {
                AppLog.w("【远程控制服务器】WebSocket服务器未运行，无法发送连接响应")
            }
        } catch (e: Exception) {
            AppLog.e("【远程控制服务器】发送连接响应失败", e)
        }
    }

    // ========== 🎛️ 远程接收端设置控制处理方法 ==========

    /**
     * 处理远程音视频服务开关切换
     */
    private fun handleRemoteReceiverAudioVideoToggle(controlMessage: ControlMessage) {
        AppLog.d("【远程控制服务器】处理音视频服务开关切换")

        val enabled = controlMessage.getBooleanData("enabled") ?: return
        AppLog.d("【远程控制服务器】音视频服务开关: $enabled")

        try {
            // 通过Intent控制音视频服务
            applicationContext?.let { context ->
                val intent = Intent(context, ReceivingService::class.java)
                if (enabled) {
                    // 启动音视频服务 - 使用默认端口8888
                    intent.action = ReceivingService.ACTION_START
                    intent.putExtra(ReceivingService.EXTRA_PORT, 8888)
                    context.startService(intent)
                    AppLog.d("【远程控制服务器】已启动音视频服务")
                } else {
                    // 停止音视频服务
                    intent.action = ReceivingService.ACTION_STOP
                    context.startService(intent)
                    AppLog.d("【远程控制服务器】已停止音视频服务")
                }

                // 🔧 修复：通知本地UI更新
                notifyLocalUIUpdate(context, "audio_video_enabled", enabled)

                // 发送状态同步消息
                sendSettingsSyncResponse(controlMessage.connectionId, mapOf(
                    "audio_video_enabled" to enabled,
                    "action" to "audio_video_toggle",
                    "success" to true
                ))
            } ?: run {
                AppLog.e("【远程控制服务器】应用上下文未设置")
                sendSettingsSyncResponse(controlMessage.connectionId, mapOf(
                    "audio_video_enabled" to !enabled,
                    "action" to "audio_video_toggle",
                    "success" to false,
                    "error" to "应用上下文未设置"
                ))
            }

        } catch (e: Exception) {
            AppLog.e("【远程控制服务器】音视频服务开关切换失败", e)
            sendSettingsSyncResponse(controlMessage.connectionId, mapOf(
                "audio_video_enabled" to !enabled,
                "action" to "audio_video_toggle",
                "success" to false,
                "error" to (e.message ?: "未知错误")
            ))
        }
    }

    /**
     * 处理远程播放模式切换
     */
    private fun handleRemoteReceiverPlaybackModeChange(controlMessage: ControlMessage) {
        AppLog.d("【远程控制服务器】处理播放模式切换")

        val isSpeakerMode = controlMessage.getBooleanData("is_speaker_mode") ?: return
        AppLog.d("【远程控制服务器】播放模式: ${if (isSpeakerMode) "扬声器" else "听筒"}")

        try {
            // 通过Intent控制播放模式
            applicationContext?.let { context ->
                val intent = Intent(context, ReceivingService::class.java).apply {
                    action = ReceivingService.ACTION_SET_AUDIO_OUTPUT_MODE
                    putExtra(ReceivingService.EXTRA_SPEAKER_MODE, isSpeakerMode)
                }
                context.startService(intent)

                // 🔧 修复：通知本地UI更新
                notifyLocalUIUpdate(context, "playback_mode", isSpeakerMode)

                AppLog.d("【远程控制服务器】播放模式切换成功")

                // 发送状态同步消息
                sendSettingsSyncResponse(controlMessage.connectionId, mapOf(
                    "is_speaker_mode" to isSpeakerMode,
                    "mode" to if (isSpeakerMode) "speaker" else "earpiece",
                    "action" to "playback_mode_change",
                    "success" to true
                ))
            } ?: run {
                AppLog.e("【远程控制服务器】应用上下文未设置")
                sendSettingsSyncResponse(controlMessage.connectionId, mapOf(
                    "is_speaker_mode" to !isSpeakerMode,
                    "action" to "playback_mode_change",
                    "success" to false,
                    "error" to "应用上下文未设置"
                ))
            }

        } catch (e: Exception) {
            AppLog.e("【远程控制服务器】播放模式切换失败", e)
            sendSettingsSyncResponse(controlMessage.connectionId, mapOf(
                "is_speaker_mode" to !isSpeakerMode,
                "action" to "playback_mode_change",
                "success" to false,
                "error" to (e.message ?: "未知错误")
            ))
        }
    }

    /**
     * 处理远程音量调整
     */
    private fun handleRemoteReceiverVolumeChange(controlMessage: ControlMessage) {
        AppLog.d("【远程控制服务器】处理音量调整")

        val volume = controlMessage.getIntData("volume") ?: return
        AppLog.d("【远程控制服务器】音量调整: $volume%")

        try {
            applicationContext?.let { context ->
                // 🔧 修复：直接设置系统音量，确保立即生效
                setSystemVolumeDirectly(context, volume)

                // 同时通过Intent通知ReceivingService更新相关状态
                val intent = Intent(context, ReceivingService::class.java).apply {
                    action = ReceivingService.ACTION_SET_RECEIVER_VOLUME
                    putExtra(ReceivingService.EXTRA_VOLUME, volume)
                }
                context.startService(intent)

                // 🔧 修复：通知本地UI更新
                notifyLocalUIUpdate(context, "volume", volume)

                AppLog.d("【远程控制服务器】音量调整成功: $volume%")

                // 发送状态同步消息
                sendSettingsSyncResponse(controlMessage.connectionId, mapOf(
                    "volume" to volume,
                    "action" to "volume_change",
                    "success" to true
                ))
            } ?: run {
                AppLog.e("【远程控制服务器】应用上下文未设置")
                sendSettingsSyncResponse(controlMessage.connectionId, mapOf(
                    "volume" to volume,
                    "action" to "volume_change",
                    "success" to false,
                    "error" to "应用上下文未设置"
                ))
            }

        } catch (e: Exception) {
            AppLog.e("【远程控制服务器】音量调整失败", e)
            sendSettingsSyncResponse(controlMessage.connectionId, mapOf(
                "volume" to volume,
                "action" to "volume_change",
                "success" to false,
                "error" to (e.message ?: "未知错误")
            ))
        }
    }

    /**
     * 处理远程设置状态请求
     */
    private fun handleRemoteReceiverSettingsRequest(controlMessage: ControlMessage) {
        AppLog.d("【远程控制服务器】处理设置状态请求")

        try {
            applicationContext?.let { context ->
                // 读取当前设置状态
                val sharedPrefs = context.getSharedPreferences("receiver_settings", Context.MODE_PRIVATE)
                val isSpeakerMode = sharedPrefs.getBoolean("audio_output_speaker_mode", true)
                val volume = sharedPrefs.getInt("receiver_volume", 80)

                // 🔧 修复：从StateManager获取真实的音视频服务运行状态
                val stateManager = StateManager.getInstance(context.applicationContext as android.app.Application)
                val audioVideoEnabled = stateManager.isReceivingServiceRunning.value ?: false

                val currentSettings = mapOf(
                    "audio_video_enabled" to audioVideoEnabled,
                    "is_speaker_mode" to isSpeakerMode,
                    "mode" to if (isSpeakerMode) "speaker" else "earpiece",
                    "volume" to volume,
                    "action" to "settings_request",
                    "success" to true
                )

                AppLog.d("【远程控制服务器】当前设置状态: $currentSettings")

                // 发送当前设置状态
                sendSettingsSyncResponse(controlMessage.connectionId, currentSettings)
            } ?: run {
                AppLog.e("【远程控制服务器】应用上下文未设置")
                sendSettingsSyncResponse(controlMessage.connectionId, mapOf(
                    "action" to "settings_request",
                    "success" to false,
                    "error" to "应用上下文未设置"
                ))
            }

        } catch (e: Exception) {
            AppLog.e("【远程控制服务器】获取设置状态失败", e)
            sendSettingsSyncResponse(controlMessage.connectionId, mapOf(
                "action" to "settings_request",
                "success" to false,
                "error" to (e.message ?: "未知错误")
            ))
        }
    }

    /**
     * 🪟 处理投屏窗口管理请求
     */
    private fun handleWindowManagerRequest(controlMessage: ControlMessage) {
        AppLog.d("【远程控制服务器】处理投屏窗口管理请求")

        try {
            applicationContext?.let { context ->
                // 获取CastWindowManager实例
                val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()

                // 获取当前所有投屏窗口信息
                val windowInfoList = windowSettingsManager.getCurrentWindowInfoList()

                AppLog.d("【远程控制服务器】获取到 ${windowInfoList.size} 个投屏窗口信息")

                // 将CastWindowInfo转换为可序列化的Map格式
                val serializedWindowList = windowInfoList.map { windowInfo ->
                    // 🪟 计算裁剪后的实际显示尺寸
                    val (actualWidth, actualHeight) = if (windowInfo.isCropping && windowInfo.cropRectRatio != null) {
                        // 有裁剪：计算裁剪后的尺寸
                        val cropRect = windowInfo.cropRectRatio
                        val croppedWidth = windowInfo.baseWindowWidth * (cropRect.right - cropRect.left)
                        val croppedHeight = windowInfo.baseWindowHeight * (cropRect.bottom - cropRect.top)
                        Pair(croppedWidth.toInt(), croppedHeight.toInt())
                    } else {
                        // 无裁剪：使用原始尺寸
                        Pair(windowInfo.baseWindowWidth, windowInfo.baseWindowHeight)
                    }

                    AppLog.d("【窗口管理】窗口 ${windowInfo.connectionId} 尺寸计算: 原始=${windowInfo.baseWindowWidth}×${windowInfo.baseWindowHeight}, 裁剪=${windowInfo.isCropping}, 实际显示=${actualWidth}×${actualHeight}")

                    // 📝 收集文本窗口的完整富文本格式信息
                    val (textContent, textFormatData) = if (windowInfo.connectionId.startsWith("text_")) {
                        try {
                            val textFormatManager = com.example.castapp.utils.TextFormatManager(context)

                            // 📝 优先获取富文本格式（包含完整的SpannableString信息）
                            val richTextSpannable = textFormatManager.getRichTextFormat(windowInfo.connectionId)

                            if (richTextSpannable != null) {
                                // 获取富文本数据的JSON序列化
                                val richTextData = context.getSharedPreferences("text_format_preferences", android.content.Context.MODE_PRIVATE)
                                    .getString("rich_text_data_${windowInfo.connectionId}", null)

                                // 🎨 获取窗口背景颜色信息（富文本情况）
                                val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()
                                var windowColorEnabled = false
                                var windowBackgroundColor = 0xFFFFFFFF.toInt()
                                var actualLineTexts = emptyList<String>() // 🎯 新方案：获取接收端实际每行文本

                                try {
                                    // 🎯 修复：使用正确的方法获取TransformHandler
                                    val transformHandler = windowSettingsManager.getWindowMapping(windowInfo.connectionId)
                                    val textWindowView = transformHandler?.getTextWindowManager()?.getTextWindowView()
                                    if (textWindowView != null) {
                                        val colorState = textWindowView.getWindowBackgroundColorState()
                                        windowColorEnabled = colorState.first
                                        windowBackgroundColor = colorState.second

                                        // 🎯 新方案：直接获取接收端的每行文本内容（最精准的换行同步方案）
                                        actualLineTexts = textWindowView.getActualLineTexts()
                                        AppLog.d("【远程控制服务器】🎯 获取到接收端实际换行结果: ${actualLineTexts.size}行")
                                        actualLineTexts.forEachIndexed { index, lineText ->
                                            AppLog.d("【远程控制服务器】  第${index + 1}行: \"$lineText\"")
                                        }

                                        AppLog.d("【远程控制服务器】🎨 获取富文本窗口背景颜色: ${windowInfo.connectionId}")
                                        AppLog.d("  启用状态: $windowColorEnabled")
                                        AppLog.d("  颜色值: ${String.format("#%08X", windowBackgroundColor)}")
                                        AppLog.d("  实际背景: ${textWindowView.background}")
                                    } else {
                                        AppLog.w("【远程控制服务器】🎨 未找到TextWindowView: ${windowInfo.connectionId}")
                                    }
                                } catch (e: Exception) {
                                    AppLog.w("【远程控制服务器】获取富文本窗口背景颜色失败: ${windowInfo.connectionId}", e)
                                }

                                // 🎨 获取扩展格式信息（富文本也需要基本格式信息）
                                val extendedFormat = textFormatManager.getExtendedTextFormat(windowInfo.connectionId)

                                val formatData = mapOf(
                                    "hasRichText" to true,
                                    "richTextData" to (richTextData ?: ""),
                                    "textLength" to richTextSpannable.length,
                                    // 🎨 新增：基本格式信息（富文本也需要）
                                    "isBold" to (extendedFormat?.isBold ?: false),
                                    "isItalic" to (extendedFormat?.isItalic ?: false),
                                    "fontSize" to (extendedFormat?.fontSize ?: 13),
                                    "fontName" to (extendedFormat?.fontName ?: "Roboto"),
                                    "fontFamily" to (extendedFormat?.fontFamily ?: ""),
                                    "lineSpacing" to (extendedFormat?.lineSpacing ?: 0.0f),
                                    "textAlignment" to (extendedFormat?.textAlignment ?: 0),
                                    // 🎨 新增：窗口背景颜色信息
                                    "windowColorEnabled" to windowColorEnabled,
                                    "windowBackgroundColor" to windowBackgroundColor,
                                    // 🎯 新方案：传输接收端实际每行文本内容（最精准的换行同步）
                                    "actualLineTexts" to actualLineTexts
                                )

                                AppLog.d("【远程控制服务器】获取到富文本格式: ${windowInfo.connectionId}, 长度=${richTextSpannable.length}")
                                Pair(richTextSpannable.toString(), formatData)
                            } else {
                                // 后备方案：获取扩展格式信息
                                val extendedFormat = textFormatManager.getExtendedTextFormat(windowInfo.connectionId)

                                if (extendedFormat != null) {
                                    // 🎨 获取窗口背景颜色信息
                                    val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()
                                    var windowColorEnabled = false
                                    var windowBackgroundColor = 0xFFFFFFFF.toInt()
                                    var actualLineTexts = emptyList<String>() // 🎯 新方案：获取接收端实际每行文本

                                    try {
                                        // 🎯 修复：使用正确的方法获取TransformHandler
                                        val transformHandler = windowSettingsManager.getWindowMapping(windowInfo.connectionId)
                                        val textWindowView = transformHandler?.getTextWindowManager()?.getTextWindowView()
                                        if (textWindowView != null) {
                                            val colorState = textWindowView.getWindowBackgroundColorState()
                                            windowColorEnabled = colorState.first
                                            windowBackgroundColor = colorState.second

                                            // 🎯 新方案：直接获取接收端的每行文本内容（最精准的换行同步方案）
                                            actualLineTexts = textWindowView.getActualLineTexts()
                                            AppLog.d("【远程控制服务器】🎯 获取到接收端实际换行结果（基本格式）: ${actualLineTexts.size}行")
                                            actualLineTexts.forEachIndexed { index, lineText ->
                                                AppLog.d("【远程控制服务器】  第${index + 1}行: \"$lineText\"")
                                            }

                                            AppLog.d("【远程控制服务器】🎨 获取基本格式窗口背景颜色: ${windowInfo.connectionId}")
                                            AppLog.d("  启用状态: $windowColorEnabled")
                                            AppLog.d("  颜色值: ${String.format("#%08X", windowBackgroundColor)}")
                                            AppLog.d("  实际背景: ${textWindowView.background}")
                                        } else {
                                            AppLog.w("【远程控制服务器】🎨 未找到TextWindowView: ${windowInfo.connectionId}")
                                        }
                                    } catch (e: Exception) {
                                        AppLog.w("【远程控制服务器】获取文本窗口背景颜色失败: ${windowInfo.connectionId}", e)
                                    }

                                    val formatData = mapOf(
                                        "hasRichText" to false,
                                        "isBold" to extendedFormat.isBold,
                                        "isItalic" to extendedFormat.isItalic,
                                        "fontSize" to extendedFormat.fontSize,
                                        "fontName" to (extendedFormat.fontName ?: "Roboto"),
                                        "fontFamily" to (extendedFormat.fontFamily ?: ""),
                                        "lineSpacing" to extendedFormat.lineSpacing,
                                        "textAlignment" to extendedFormat.textAlignment,
                                        // 🎨 新增：窗口背景颜色信息
                                        "windowColorEnabled" to windowColorEnabled,
                                        "windowBackgroundColor" to windowBackgroundColor,
                                        // 🎯 新方案：传输接收端实际每行文本内容（最精准的换行同步）
                                        "actualLineTexts" to actualLineTexts
                                    )
                                    Pair(extendedFormat.textContent, formatData)
                                } else {
                                    Pair("文本内容", mapOf("hasRichText" to false, "windowColorEnabled" to false, "windowBackgroundColor" to 0xFFFFFFFF.toInt()))
                                }
                            }
                        } catch (e: Exception) {
                            AppLog.e("【远程控制服务器】获取文本窗口内容失败: ${windowInfo.connectionId}", e)
                            Pair("文本内容", mapOf("hasRichText" to false))
                        }
                    } else {
                        Pair(null, null)
                    }

                    mapOf(
                        "connectionId" to windowInfo.connectionId,
                        "ipAddress" to windowInfo.ipAddress,
                        "port" to windowInfo.port,
                        "isActive" to windowInfo.isActive,
                        "deviceName" to (windowInfo.deviceName ?: "未知设备"),
                        "positionX" to windowInfo.positionX,
                        "positionY" to windowInfo.positionY,
                        "scaleFactor" to windowInfo.scaleFactor,
                        "rotationAngle" to windowInfo.rotationAngle,
                        "zOrder" to windowInfo.zOrder,
                        "isCropping" to windowInfo.isCropping,
                        "isDragEnabled" to windowInfo.isDragEnabled,
                        "isScaleEnabled" to windowInfo.isScaleEnabled,
                        "isRotationEnabled" to windowInfo.isRotationEnabled,
                        "isVisible" to windowInfo.isVisible,
                        "isMirrored" to windowInfo.isMirrored,
                        "cornerRadius" to windowInfo.cornerRadius,
                        "alpha" to windowInfo.alpha,
                        "isControlEnabled" to windowInfo.isControlEnabled,

                        // 🎯 添加边框参数到WebSocket传输
                        "isBorderEnabled" to windowInfo.isBorderEnabled,
                        "borderColor" to windowInfo.borderColor,
                        "borderWidth" to windowInfo.borderWidth,
                        "baseWindowWidth" to windowInfo.baseWindowWidth,
                        "baseWindowHeight" to windowInfo.baseWindowHeight,
                        // 🪟 新增：实际显示尺寸（考虑裁剪）
                        "actualDisplayWidth" to actualWidth,
                        "actualDisplayHeight" to actualHeight,
                        // 📝 新增：文本窗口内容和格式信息
                        "textContent" to (textContent ?: ""),
                        "textFormatData" to (textFormatData ?: emptyMap<String, Any>())
                    ).let { baseMap ->
                        // 🔧 修复类型问题：只有当cropRectRatio不为null时才添加
                        if (windowInfo.cropRectRatio != null) {
                            baseMap + mapOf(
                                "cropRectRatio" to mapOf(
                                    "left" to windowInfo.cropRectRatio.left,
                                    "top" to windowInfo.cropRectRatio.top,
                                    "right" to windowInfo.cropRectRatio.right,
                                    "bottom" to windowInfo.cropRectRatio.bottom
                                )
                            )
                        } else {
                            baseMap
                        }
                    }
                }

                // 发送窗口管理响应消息
                sendWindowManagerResponse(controlMessage.connectionId, serializedWindowList)

            } ?: run {
                AppLog.e("【远程控制服务器】应用上下文未设置")
                sendWindowManagerErrorResponse(controlMessage.connectionId, "应用上下文未设置")
            }

        } catch (e: Exception) {
            AppLog.e("【远程控制服务器】处理投屏窗口管理请求失败", e)
            sendWindowManagerErrorResponse(controlMessage.connectionId, e.message ?: "未知错误")
        }
    }

    /**
     * 🪟 发送投屏窗口管理响应消息
     */
    private fun sendWindowManagerResponse(connectionId: String, windowInfoList: List<Map<String, Any>>) {
        try {
            webSocketServer?.let { server ->
                val responseMessage = ControlMessage.createWindowManagerResponse(connectionId, windowInfoList)
                server.broadcastMessage(responseMessage)
                AppLog.d("【远程控制服务器】投屏窗口管理响应已发送，窗口数量: ${windowInfoList.size}")
            } ?: run {
                AppLog.w("【远程控制服务器】WebSocket服务器未运行")
            }
        } catch (e: Exception) {
            AppLog.e("【远程控制服务器】发送投屏窗口管理响应失败", e)
        }
    }

    /**
     * 🪟 发送投屏窗口管理错误响应
     */
    private fun sendWindowManagerErrorResponse(connectionId: String, errorMessage: String) {
        try {
            webSocketServer?.let { server ->
                val errorResponse = ControlMessage(
                    type = ControlMessage.TYPE_WINDOW_MANAGER_RESPONSE,
                    connectionId = connectionId,
                    data = mapOf(
                        "timestamp" to System.currentTimeMillis(),
                        "window_count" to 0,
                        "window_info_list" to emptyList<Map<String, Any>>(),
                        "success" to false,
                        "error" to errorMessage
                    )
                )
                server.broadcastMessage(errorResponse)
                AppLog.d("【远程控制服务器】投屏窗口管理错误响应已发送: $errorMessage")
            } ?: run {
                AppLog.w("【远程控制服务器】WebSocket服务器未运行")
            }
        } catch (e: Exception) {
            AppLog.e("【远程控制服务器】发送投屏窗口管理错误响应失败", e)
        }
    }

    // ========== 📐 屏幕分辨率请求处理方法 ==========

    /**
     * 📐 处理屏幕分辨率请求
     */
    private fun handleScreenResolutionRequest(controlMessage: ControlMessage) {
        AppLog.d("📐 处理屏幕分辨率请求: ${controlMessage.connectionId}")

        try {
            applicationContext?.let { context ->
                // 获取ResolutionManager实例
                val resolutionManager = com.example.castapp.manager.ResolutionManager.getInstance(context)

                // 获取可用屏幕分辨率（去除状态栏高度）
                val (width, height) = resolutionManager.getUsableScreenResolution()

                AppLog.d("📐 获取到接收端可用屏幕分辨率: ${width}×${height} (已去除状态栏)")

                // 发送屏幕分辨率响应
                sendScreenResolutionResponse(controlMessage.connectionId, width, height)

            } ?: run {
                AppLog.e("📐 应用上下文未设置")
                // 发送错误响应（使用0x0表示获取失败）
                sendScreenResolutionResponse(controlMessage.connectionId, 0, 0)
            }

        } catch (e: Exception) {
            AppLog.e("📐 处理屏幕分辨率请求失败", e)
            // 发送错误响应
            sendScreenResolutionResponse(controlMessage.connectionId, 0, 0)
        }
    }

    /**
     * 📐 发送屏幕分辨率响应消息
     */
    private fun sendScreenResolutionResponse(connectionId: String, width: Int, height: Int) {
        try {
            webSocketServer?.let { server ->
                val responseMessage = ControlMessage.createScreenResolutionResponse(connectionId, width, height)
                server.broadcastMessage(responseMessage)
                AppLog.d("📐 可用屏幕分辨率响应已发送: ${width}×${height} (已去除状态栏)")
            } ?: run {
                AppLog.w("📐 WebSocket服务器未运行")
            }
        } catch (e: Exception) {
            AppLog.e("📐 发送屏幕分辨率响应失败", e)
        }
    }

    // ========== 📸 截图功能处理方法 ==========

    /**
     * 📸 处理截图请求
     */
    private fun handleScreenshotRequest(controlMessage: ControlMessage) {
        AppLog.d("📸 处理截图请求: ${controlMessage.connectionId}")

        try {
            applicationContext?.let { context ->
                // 获取CastWindowManager实例
                val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()

                // 🎯 使用重构后的截图功能，为遥控端请求提供原始截图
                windowSettingsManager.captureAllWindowScreenshots(
                    callback = { screenshotDataList ->
                        if (screenshotDataList.isNotEmpty()) {
                            sendScreenshotResponse(controlMessage.connectionId, screenshotDataList)
                            AppLog.d("📸 截图请求处理完成: ${controlMessage.connectionId}, 截图数量: ${screenshotDataList.size}")
                        } else {
                            sendScreenshotErrorResponse(controlMessage.connectionId, "没有可截图的投屏窗口")
                            AppLog.w("📸 截图请求完成但无截图: ${controlMessage.connectionId}")
                        }
                    },
                    forRemoteControl = true  // 🎯 关键修复：为遥控端提供原始截图
                )

            } ?: run {
                AppLog.e("📸 应用上下文未设置")
                sendScreenshotErrorResponse(controlMessage.connectionId, "应用上下文未设置")
            }

        } catch (e: Exception) {
            AppLog.e("📸 处理截图请求失败", e)
            sendScreenshotErrorResponse(controlMessage.connectionId, e.message ?: "未知错误")
        }
    }

    /**
     * 📸 发送截图响应消息
     */
    private fun sendScreenshotResponse(connectionId: String, screenshotDataList: List<Map<String, Any>>) {
        try {
            webSocketServer?.let { server ->
                val responseMessage = ControlMessage.createScreenshotResponse(connectionId, screenshotDataList)
                server.broadcastMessage(responseMessage)
                AppLog.d("📸 截图响应已发送，截图数量: ${screenshotDataList.size}")
            } ?: run {
                AppLog.w("📸 WebSocket服务器未运行")
            }
        } catch (e: Exception) {
            AppLog.e("📸 发送截图响应失败", e)
        }
    }

    /**
     * 📸 发送截图错误响应
     */
    private fun sendScreenshotErrorResponse(connectionId: String, errorMessage: String) {
        try {
            webSocketServer?.let { server ->
                val errorResponse = ControlMessage.createScreenshotError(connectionId, errorMessage)
                server.broadcastMessage(errorResponse)
                AppLog.d("📸 截图错误响应已发送: $errorMessage")
            } ?: run {
                AppLog.w("📸 WebSocket服务器未运行")
            }
        } catch (e: Exception) {
            AppLog.e("📸 发送截图错误响应失败", e)
        }
    }

    /**
     * 发送设置同步响应消息
     */
    private fun sendSettingsSyncResponse(connectionId: String, settings: Map<String, Any>) {
        try {
            webSocketServer?.let { server ->
                // 🔧 修复：使用原始connectionId创建消息，但广播到所有连接
                val syncMessage = ControlMessage.createRemoteReceiverSettingsSync(connectionId, settings)
                server.broadcastMessage(syncMessage)
                AppLog.d("【远程控制服务器】设置同步消息已广播到所有遥控端，原始连接ID: $connectionId")
            } ?: run {
                AppLog.w("【远程控制服务器】WebSocket服务器未运行")
            }
        } catch (e: Exception) {
            AppLog.e("【远程控制服务器】发送设置同步消息失败", e)
        }
    }

    /**
     * 🔧 直接设置系统音量
     */
    private fun setSystemVolumeDirectly(context: Context, volumePercent: Int) {
        try {
            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

            // 读取当前播放模式来确定音频流类型
            val sharedPrefs = context.getSharedPreferences("receiver_settings", Context.MODE_PRIVATE)
            val isSpeakerMode = sharedPrefs.getBoolean("audio_output_speaker_mode", true)

            val streamType = if (isSpeakerMode) AudioManager.STREAM_MUSIC else AudioManager.STREAM_VOICE_CALL
            val maxVolume = audioManager.getStreamMaxVolume(streamType)
            val targetVolume = (volumePercent * maxVolume / 100.0).toInt().coerceIn(0, maxVolume)

            audioManager.setStreamVolume(streamType, targetVolume, 0)

            // 保存到SharedPreferences
            sharedPrefs.edit {
                putInt("receiver_volume", volumePercent)
            }

            AppLog.d("【远程控制服务器】直接设置系统音量: $volumePercent% -> $targetVolume/$maxVolume [${if (isSpeakerMode) "STREAM_MUSIC" else "STREAM_VOICE_CALL"}]")

        } catch (e: Exception) {
            AppLog.e("【远程控制服务器】直接设置系统音量失败", e)
        }
    }

    /**
     * 🔧 通知本地UI更新
     */
    private fun notifyLocalUIUpdate(context: Context, settingType: String, value: Any) {
        try {
            // 🔧 修复：使用正确的广播Action，与ReceiverDialogFragment监听的Action一致
            val intent = Intent("com.example.castapp.REMOTE_SETTINGS_CHANGED").apply {
                putExtra("setting_type", settingType)
                when (value) {
                    is Boolean -> putExtra("setting_value_boolean", value)
                    is Int -> putExtra("setting_value_int", value)
                    is String -> putExtra("setting_value_string", value)
                }
            }
            context.sendBroadcast(intent)
            AppLog.d("【远程控制服务器】已发送本地UI更新广播: $settingType = $value")
        } catch (e: Exception) {
            AppLog.e("【远程控制服务器】发送本地UI更新广播失败", e)
        }
    }

    // ========== 🔄 窗口变换控制处理方法 ==========

    /**
     * 🔄 处理远程窗口变换控制消息
     */
    private fun handleRemoteWindowTransformControl(controlMessage: ControlMessage) {
        AppLog.d("【远程控制服务器】处理窗口变换控制消息")

        try {
            val targetWindowId = controlMessage.getStringData("target_window_id") ?: return
            val transformType = controlMessage.getStringData("transform_type") ?: return
            val transformData = controlMessage.data["transform_data"] as? Map<String, Any> ?: return

            AppLog.d("【远程控制服务器】窗口变换控制: $targetWindowId -> $transformType")

            applicationContext?.let { context ->
                // 通过WindowSettingsManager执行窗口变换操作
                val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()

                when (transformType) {
                    "position" -> {
                        // 🎯 修复：处理窗口位置更新，保持当前缩放和旋转
                        val x = (transformData["x"] as? Number)?.toFloat() ?: return
                        val y = (transformData["y"] as? Number)?.toFloat() ?: return
                        windowSettingsManager.applyRemotePosition(targetWindowId, x, y)
                        AppLog.d("【远程控制服务器】窗口位置更新: $targetWindowId -> ($x, $y)")
                    }
                    "scale" -> {
                        // 🎯 新增：处理窗口缩放更新
                        val scaleFactor = (transformData["scale_factor"] as? Number)?.toFloat() ?: return
                        windowSettingsManager.applyRemoteScale(targetWindowId, scaleFactor)
                        AppLog.d("【远程控制服务器】窗口缩放更新: $targetWindowId -> $scaleFactor")
                    }
                    "rotation" -> {
                        // 🎯 新增：处理窗口旋转更新
                        val rotationAngle = (transformData["rotation_angle"] as? Number)?.toFloat() ?: return
                        windowSettingsManager.applyRemoteRotation(targetWindowId, rotationAngle)
                        AppLog.d("【远程控制服务器】窗口旋转更新: $targetWindowId -> $rotationAngle°")
                    }
                    "scale_and_position" -> {
                        // 🎯 增强型同步：处理窗口缩放和位置组合更新
                        val scaleFactor = (transformData["scale_factor"] as? Number)?.toFloat() ?: return
                        val x = (transformData["x"] as? Number)?.toFloat() ?: return
                        val y = (transformData["y"] as? Number)?.toFloat() ?: return
                        windowSettingsManager.applyRemoteScaleAndPosition(targetWindowId, scaleFactor, x, y)
                        AppLog.d("【远程控制服务器】窗口缩放和位置组合更新: $targetWindowId -> Scale=$scaleFactor, Position=($x, $y)")
                    }
                    "rotation_and_position" -> {
                        // 🎯 增强型同步：处理窗口旋转和位置组合更新
                        val rotationAngle = (transformData["rotation_angle"] as? Number)?.toFloat() ?: return
                        val x = (transformData["x"] as? Number)?.toFloat() ?: return
                        val y = (transformData["y"] as? Number)?.toFloat() ?: return
                        windowSettingsManager.applyRemoteRotationAndPosition(targetWindowId, rotationAngle, x, y)
                        AppLog.d("【远程控制服务器】窗口旋转和位置组合更新: $targetWindowId -> Rotation=$rotationAngle°, Position=($x, $y)")
                    }
                    "crop" -> {
                        // 🎯 新增：处理窗口裁剪更新
                        val cropLeft = (transformData["crop_left"] as? Number)?.toFloat() ?: return
                        val cropTop = (transformData["crop_top"] as? Number)?.toFloat() ?: return
                        val cropRight = (transformData["crop_right"] as? Number)?.toFloat() ?: return
                        val cropBottom = (transformData["crop_bottom"] as? Number)?.toFloat() ?: return

                        val cropRatio = RectF(cropLeft, cropTop, cropRight, cropBottom)
                        windowSettingsManager.applyRemoteCrop(targetWindowId, cropRatio)
                        AppLog.d("【远程控制服务器】窗口裁剪更新: $targetWindowId -> $cropRatio")
                    }
                    "drag_enabled" -> {
                        val enabled = transformData["enabled"] as? Boolean ?: return
                        // 使用现有的公开方法
                        windowSettingsManager.setDragEnabled(targetWindowId, enabled)
                    }
                    "scale_enabled" -> {
                        val enabled = transformData["enabled"] as? Boolean ?: return
                        windowSettingsManager.setScaleEnabled(targetWindowId, enabled)
                    }
                    "rotation_enabled" -> {
                        val enabled = transformData["enabled"] as? Boolean ?: return
                        windowSettingsManager.setRotationEnabled(targetWindowId, enabled)
                    }
                    "crop_enabled" -> {
                        val enabled = transformData["enabled"] as? Boolean ?: return
                        windowSettingsManager.setCropEnabled(targetWindowId, enabled)
                    }
                    "visibility" -> {
                        val visible = transformData["visible"] as? Boolean ?: return
                        windowSettingsManager.setWindowVisibility(targetWindowId, visible)
                    }
                    "mirror" -> {
                        val enabled = transformData["enabled"] as? Boolean ?: return
                        windowSettingsManager.setMirrorEnabled(targetWindowId, enabled)
                    }
                    "corner_radius" -> {
                        val radius = (transformData["radius"] as? Number)?.toFloat() ?: return
                        windowSettingsManager.setCornerRadius(targetWindowId, radius)
                    }
                    "alpha" -> {
                        val alpha = (transformData["alpha"] as? Number)?.toFloat() ?: return
                        windowSettingsManager.setWindowAlpha(targetWindowId, alpha)
                    }
                    "border" -> {
                        val enabled = transformData["enabled"] as? Boolean ?: return
                        windowSettingsManager.setBorderEnabled(targetWindowId, enabled)
                    }
                    "border_color" -> {
                        val color = (transformData["color"] as? Number)?.toInt() ?: return
                        windowSettingsManager.setBorderColor(targetWindowId, color)
                    }
                    "border_width" -> {
                        val width = (transformData["width"] as? Number)?.toFloat() ?: return
                        windowSettingsManager.setBorderWidth(targetWindowId, width)
                    }
                    else -> {
                        AppLog.w("【远程控制服务器】未知的窗口变换类型: $transformType")
                        return
                    }
                }

                AppLog.d("【远程控制服务器】窗口变换控制执行成功: $targetWindowId -> $transformType")

                // 发送成功响应（可选）
                sendWindowTransformControlResponse(controlMessage.connectionId, targetWindowId, transformType, true)

            } ?: run {
                AppLog.e("【远程控制服务器】应用上下文未设置")
                sendWindowTransformControlResponse(controlMessage.connectionId, targetWindowId, transformType, false, "应用上下文未设置")
            }

        } catch (e: Exception) {
            AppLog.e("【远程控制服务器】窗口变换控制失败", e)
            val targetWindowId = controlMessage.getStringData("target_window_id") ?: "unknown"
            val transformType = controlMessage.getStringData("transform_type") ?: "unknown"
            sendWindowTransformControlResponse(controlMessage.connectionId, targetWindowId, transformType, false, e.message ?: "未知错误")
        }
    }

    /**
     * 🔄 发送窗口变换控制响应消息（可选）
     */
    private fun sendWindowTransformControlResponse(
        connectionId: String,
        targetWindowId: String,
        transformType: String,
        success: Boolean,
        errorMessage: String? = null
    ) {
        try {
            webSocketServer?.let { server ->
                val responseData = mutableMapOf<String, Any>(
                    "target_window_id" to targetWindowId,
                    "transform_type" to transformType,
                    "success" to success,
                    "timestamp" to System.currentTimeMillis()
                )

                errorMessage?.let { responseData["error"] = it }

                val responseMessage = ControlMessage(
                    type = "remote_window_transform_control_response",
                    connectionId = connectionId,
                    data = responseData
                )

                server.broadcastMessage(responseMessage)
                AppLog.d("【远程控制服务器】窗口变换控制响应已发送: $success")
            } ?: run {
                AppLog.w("【远程控制服务器】WebSocket服务器未运行")
            }
        } catch (e: Exception) {
            AppLog.e("【远程控制服务器】发送窗口变换控制响应失败", e)
        }
    }
}
