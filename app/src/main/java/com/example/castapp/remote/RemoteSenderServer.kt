package com.example.castapp.remote

import com.example.castapp.utils.AppLog
import com.example.castapp.websocket.ControlMessage
import com.example.castapp.websocket.WebSocketServer
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 远程被控服务器
 * 固定监听9999端口，等待远程控制发送端连接
 */
class RemoteSenderServer private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: RemoteSenderServer? = null
        
        // 固定端口9999
        const val REMOTE_CONTROL_PORT = 9999
        
        fun getInstance(): RemoteSenderServer {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: RemoteSenderServer().also { INSTANCE = it }
            }
        }
    }
    
    private var webSocketServer: WebSocketServer? = null
    private val isRunning = AtomicBoolean(false)
    private var onConnectionStateChanged: ((Boolean) -> Unit)? = null
    private var onRemoteControlMessage: ((ControlMessage) -> Unit)? = null

    // UI更新回调
    private var onBitrateUpdate: ((Int) -> Unit)? = null
    private var onResolutionUpdate: ((Int) -> Unit)? = null
    private var onVolumeUpdate: ((String, Int) -> Unit)? = null
    private var onConnectionToggle: ((String, Boolean, String?) -> Unit)? = null
    private var onConnectionManagementRequest: ((String, String, Int, String?) -> Unit)? = null
    
    /**
     * 启动远程控制服务器
     */
    fun startServer(): Boolean {
        if (isRunning.get()) {
            AppLog.d("远程控制服务器已在运行")
            return true
        }
        
        return try {
            AppLog.d("启动远程控制服务器，端口: $REMOTE_CONTROL_PORT")
            
            webSocketServer = WebSocketServer(
                port = REMOTE_CONTROL_PORT,
                onMessageReceived = { message ->
                    AppLog.d("收到远程控制消息: ${message.type}")
                    handleRemoteControlMessage(message)
                    onRemoteControlMessage?.invoke(message)
                },
                onConnectionRequest = { connectionId, clientIP, clientPort, deviceName ->
                    AppLog.d("远程控制连接请求: $connectionId from $clientIP:$clientPort ($deviceName)")
                    // 发送连接响应
                    val response = ControlMessage.createRemoteControlResponse(connectionId, true, "远程控制连接已建立")
                    webSocketServer?.sendMessageToClient(connectionId, response)
                },
                serverType = "remote_control" // 🐾 关键修复：标识为远程控制服务器
            )
            
            webSocketServer?.start()
            isRunning.set(true)
            onConnectionStateChanged?.invoke(true)
            
            AppLog.d("远程控制服务器启动成功")
            true
            
        } catch (e: Exception) {
            AppLog.e("启动远程控制服务器失败", e)
            stopServer()
            false
        }
    }
    
    /**
     * 停止远程控制服务器
     */
    fun stopServer() {
        if (!isRunning.get()) {
            AppLog.d("远程控制服务器未运行")
            return
        }

        try {
            AppLog.d("停止远程控制服务器")

            // 🐾 关键修复：发送远程被控服务停止消息，然后阻止disconnect消息
            webSocketServer?.let { server ->
                val activeConnections = server.getActiveConnectionIds()
                AppLog.d("【9999端口停止】向 ${activeConnections.size} 个连接发送远程被控服务停止通知")

                activeConnections.forEach { connectionId ->
                    try {
                        val serviceStoppedMessage = ControlMessage.createRemoteControlServiceStopped(connectionId, "用户关闭远程被控开关")
                        server.sendMessageToClient(connectionId, serviceStoppedMessage)
                        AppLog.d("【9999端口停止】已向连接 $connectionId 发送远程被控服务停止通知")
                    } catch (e: Exception) {
                        AppLog.e("【9999端口停止】通知连接 $connectionId 失败", e)
                    }
                }

                // 给客户端时间处理消息
                try {
                    Thread.sleep(150)
                    AppLog.d("【9999端口停止】已等待客户端处理消息")
                } catch (_: InterruptedException) {
                    AppLog.w("【9999端口停止】等待被中断")
                }

                // 🐾 关键修复：标记断开通知已发送，阻止后续的disconnect消息
                server.markDisconnectNotificationSent()
                AppLog.d("【9999端口停止】已标记断开通知，阻止后续disconnect消息")
            }

            webSocketServer?.stopServer()
            webSocketServer = null
            isRunning.set(false)
            onConnectionStateChanged?.invoke(false)

            AppLog.d("远程控制服务器已停止")

        } catch (e: Exception) {
            AppLog.e("停止远程控制服务器失败", e)
        }
    }
    
    /**
     * 检查服务器是否运行中
     */
    fun isServerRunning(): Boolean = isRunning.get()
    
    /**
     * 设置连接状态变化监听器
     */
    fun setOnConnectionStateChangedListener(listener: (Boolean) -> Unit) {
        onConnectionStateChanged = listener
    }

    /**
     * 设置UI更新回调
     */
    fun setUIUpdateCallbacks(
        onBitrateUpdate: ((Int) -> Unit)? = null,
        onResolutionUpdate: ((Int) -> Unit)? = null,
        onVolumeUpdate: ((String, Int) -> Unit)? = null,
        onConnectionToggle: ((String, Boolean, String?) -> Unit)? = null,
        onConnectionManagementRequest: ((String, String, Int, String?) -> Unit)? = null
    ) {
        this.onBitrateUpdate = onBitrateUpdate
        this.onResolutionUpdate = onResolutionUpdate
        this.onVolumeUpdate = onVolumeUpdate
        this.onConnectionToggle = onConnectionToggle
        this.onConnectionManagementRequest = onConnectionManagementRequest
        AppLog.d("设置远程控制UI更新回调")
    }
    
    /**
     * 发送消息到所有连接的远程控制客户端
     */
    fun sendMessageToAllClients(message: ControlMessage): Boolean {
        return webSocketServer?.let { server ->
            try {
                val successCount = server.broadcastMessage(message)
                AppLog.d("广播远程控制消息: ${message.type}, 成功发送到 $successCount 个客户端")
                successCount > 0
            } catch (e: Exception) {
                AppLog.e("发送远程控制消息失败", e)
                false
            }
        } ?: false
    }
    
    /**
     * 处理远程控制消息
     */
    private fun handleRemoteControlMessage(message: ControlMessage) {
        when (message.type) {
            ControlMessage.TYPE_REMOTE_BITRATE_CHANGE -> {
                val bitrateMbps = message.getIntData("bitrate_mbps")
                if (bitrateMbps != null) {
                    AppLog.d("远程码率变更: ${bitrateMbps}Mbps")
                    onBitrateUpdate?.invoke(bitrateMbps)
                }
            }

            ControlMessage.TYPE_REMOTE_RESOLUTION_CHANGE -> {
                val scalePercent = message.getIntData("scale_percent")
                if (scalePercent != null) {
                    AppLog.d("远程分辨率变更: ${scalePercent}%")
                    onResolutionUpdate?.invoke(scalePercent)
                }
            }

            ControlMessage.TYPE_REMOTE_VOLUME_CHANGE -> {
                val volumeType = message.getStringData("volume_type")
                val volume = message.getIntData("volume")
                if (volumeType != null && volume != null) {
                    AppLog.d("远程音量变更: $volumeType = $volume%")
                    onVolumeUpdate?.invoke(volumeType, volume)
                }
            }

            ControlMessage.TYPE_REMOTE_CONNECTION_TOGGLE -> {
                val functionType = message.getStringData("function_type")
                val enabled = message.getBooleanData("enabled")
                val targetConnectionId = message.getStringData("target_connection_id")
                if (functionType != null && enabled != null) {
                    AppLog.d("远程连接切换: $functionType = $enabled, 目标连接: $targetConnectionId")
                    onConnectionToggle?.invoke(functionType, enabled, targetConnectionId)
                }
            }

            ControlMessage.TYPE_REMOTE_SETTINGS_SYNC -> {
                val action = message.getStringData("action")
                if (action == "request_sync") {
                    // 发送当前设置状态
                    sendCurrentSettings(message.connectionId)
                }
            }

            ControlMessage.TYPE_REMOTE_CONTROL_REQUEST -> {
                // 远程控制请求已在WebSocketServer中处理，这里只记录
                AppLog.d("收到远程控制请求: ${message.connectionId}")
            }

            ControlMessage.TYPE_CONNECTION_REQUEST -> {
                // 连接请求已在WebSocketServer中处理，这里只记录
                AppLog.d("收到连接请求: ${message.connectionId}")
            }

            ControlMessage.TYPE_HEARTBEAT -> {
                // 心跳消息，无需特殊处理
                // AppLog.d("收到心跳消息: ${message.connectionId}")
            }

            ControlMessage.TYPE_REMOTE_CONNECTION_ADD_REQUEST -> {
                val ipAddress = message.getStringData("ip_address")
                val port = message.getIntData("port")
                if (ipAddress != null && port != null) {
                    AppLog.d("远程添加连接请求: $ipAddress:$port")
                    onConnectionManagementRequest?.invoke("add", ipAddress, port, null)
                }
            }

            ControlMessage.TYPE_REMOTE_CONNECTION_EDIT_REQUEST -> {
                val targetConnectionId = message.getStringData("target_connection_id")
                val newIpAddress = message.getStringData("new_ip_address")
                val newPort = message.getIntData("new_port")
                if (targetConnectionId != null && newIpAddress != null && newPort != null) {
                    AppLog.d("远程编辑连接请求: $targetConnectionId -> $newIpAddress:$newPort")
                    onConnectionManagementRequest?.invoke("edit", newIpAddress, newPort, targetConnectionId)
                }
            }

            ControlMessage.TYPE_REMOTE_CONNECTION_DELETE_REQUEST -> {
                val targetConnectionId = message.getStringData("target_connection_id")
                if (targetConnectionId != null) {
                    AppLog.d("远程删除连接请求: $targetConnectionId")
                    onConnectionManagementRequest?.invoke("delete", "", 0, targetConnectionId)
                }
            }

            else -> {
                AppLog.d("未处理的远程控制消息: ${message.type}")
            }
        }
    }

    /**
     * 发送当前设置状态
     */
    private fun sendCurrentSettings(connectionId: String) {
        try {
            // 通过MainViewModel获取应用上下文
            val mainViewModel = com.example.castapp.viewmodel.MainViewModel.getInstance()
            if (mainViewModel == null) {
                AppLog.w("MainViewModel实例不可用，无法获取当前设置")
                return
            }

            val context = mainViewModel.getApplication<android.app.Application>()
            val sharedPrefs = context.getSharedPreferences("cast_settings", android.content.Context.MODE_PRIVATE)
            val resolutionManager = com.example.castapp.manager.ResolutionManager.getInstance(context)

            // 获取连接列表信息
            val stateManager = com.example.castapp.manager.StateManager.getInstance(context)
            val connections = stateManager.getAllConnections()
            val connectionData = connections.map { connection ->
                mapOf(
                    "connectionId" to connection.connectionId,
                    "ipAddress" to connection.ipAddress,
                    "port" to connection.port,
                    "isCasting" to connection.isCasting,
                    "isMediaAudioEnabled" to connection.isMediaAudioEnabled,
                    "isMicAudioEnabled" to connection.isMicAudioEnabled,
                    "isConnected" to connection.isConnected,
                    "webSocketConnected" to connection.webSocketConnected
                )
            }

            val settings = mapOf(
                "bitrate_mbps" to sharedPrefs.getInt("bitrate_mbps", 20),
                "resolution_scale" to resolutionManager.getCurrentResolutionScale(),
                "media_volume" to sharedPrefs.getInt("media_audio_volume", 80),
                "mic_volume" to sharedPrefs.getInt("mic_audio_volume", 80),
                "connections" to connectionData
            )

            val message = ControlMessage.createRemoteSettingsSync(connectionId, settings)
            webSocketServer?.sendMessageToClient(connectionId, message)
            AppLog.d("发送当前设置状态: $settings (包含 ${connections.size} 个连接)")
        } catch (e: Exception) {
            AppLog.e("发送当前设置状态失败", e)
        }
    }

    /**
     * 广播设置更新到所有连接的控制端
     */
    fun broadcastSettingsUpdate(settings: Map<String, Any>) {
        try {
            webSocketServer?.let { server ->
                val message = ControlMessage.createRemoteSettingsSync("broadcast", settings)
                val successCount = server.broadcastMessage(message)
                AppLog.d("广播设置更新完成: $settings, 成功发送到 $successCount 个客户端")
            }
        } catch (e: Exception) {
            AppLog.e("广播设置更新失败", e)
        }
    }

    /**
     * 清理监听器（但保持服务器运行）
     * 用于UI组件销毁时清理监听器，但不停止服务器
     */
    fun clearListeners() {
        onConnectionStateChanged = null
        onRemoteControlMessage = null
        onBitrateUpdate = null
        onResolutionUpdate = null
        onVolumeUpdate = null
        onConnectionToggle = null
        AppLog.d("清理远程控制服务器监听器，但保持服务器运行")
    }

    /**
     * 完全清理资源（停止服务器）
     * 只应在应用退出或用户明确关闭远程被控功能时调用
     */
    fun cleanup() {
        stopServer()
        onConnectionStateChanged = null
        onRemoteControlMessage = null
        onBitrateUpdate = null
        onResolutionUpdate = null
        onVolumeUpdate = null
        onConnectionToggle = null
        AppLog.d("完全清理远程控制服务器资源")
    }
}
