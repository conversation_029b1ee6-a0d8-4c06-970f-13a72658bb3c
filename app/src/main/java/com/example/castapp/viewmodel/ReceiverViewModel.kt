package com.example.castapp.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.core.content.edit
import com.example.castapp.network.NetworkUtils
import com.example.castapp.service.ReceivingService
import com.example.castapp.service.RemoteReceiverService
import com.example.castapp.manager.StateManager
import android.content.Intent
import android.os.Build
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import com.example.castapp.utils.AppLog
import com.example.castapp.manager.WindowSettingsManager

/**
 * 接收端ViewModel
 * 管理接收端服务状态和网络信息
 */
class ReceiverViewModel(application: Application) : AndroidViewModel(application) {
    companion object {        private const val DEFAULT_PORT = 8888
    }

    private val stateManager = StateManager.getInstance(application)

    // 暴露StateManager的LiveData
    val isReceivingServiceRunning: LiveData<Boolean> = stateManager.isReceivingServiceRunning
    private val receivingPort: LiveData<Int> = stateManager.receivingPort

    private val _localIpAddress = MutableLiveData<String>()
    val localIpAddress: LiveData<String> = _localIpAddress

    private val _port = MutableLiveData<Int>()
    val port: LiveData<Int> = _port

    private val _toastMessage = MutableLiveData<String>()
    val toastMessage: LiveData<String> = _toastMessage

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _serverStatusText = MutableLiveData<String>()
    val serverStatusText: LiveData<String> = _serverStatusText

    // 固定端口WebSocket服务器状态管理
    private val _isFixedWebSocketRunning = MutableLiveData<Boolean>()
    val isFixedWebSocketRunning: LiveData<Boolean> = _isFixedWebSocketRunning

    private val _isFixedWebSocketLoading = MutableLiveData<Boolean>()
    val isFixedWebSocketLoading: LiveData<Boolean> = _isFixedWebSocketLoading

    // 保存观察者引用以便清理
    private val serviceStatusObserver = androidx.lifecycle.Observer<Boolean> { _ ->
        updateServerStatus()
    }

    init {
        // 初始化数据
        _port.value = DEFAULT_PORT
        loadLocalIpAddress()
        updateServerStatus()

        // 初始化固定端口WebSocket服务器状态
        loadFixedWebSocketState()

        // 观察服务状态变化
        isReceivingServiceRunning.observeForever(serviceStatusObserver)
    }

    /**
     * 加载本地IP地址
     */
    private fun loadLocalIpAddress() {
        viewModelScope.launch {
            try {
                val ipAddress = NetworkUtils.getLocalIpAddress()
                _localIpAddress.value = ipAddress ?: "获取IP失败"
                AppLog.d("本地IP地址: $ipAddress")
            } catch (e: Exception) {
                AppLog.e("获取本地IP地址失败", e)
                _localIpAddress.value = "获取IP失败"
            }
        }
    }

    /**
     * 更新服务器状态显示
     */
    private fun updateServerStatus() {
        val isRunning = isReceivingServiceRunning.value ?: false
        // 优先使用ViewModel中的端口值，避免StateManager时序问题
        val currentPort = _port.value ?: receivingPort.value ?: 0

        if (isRunning) {
            _serverStatusText.value = "服务器运行中 (端口: $currentPort)"
        } else {
            _serverStatusText.value = "服务器未运行"
        }

        AppLog.d("更新服务器状态: 运行=$isRunning, 端口=$currentPort")
    }

    /**
     * 设置端口
     */
    fun setPort(port: Int) {
        _port.value = port
        AppLog.d("设置端口: $port")
    }

    /**
     * 设置音频输出模式
     */
    fun setAudioOutputMode(isSpeakerMode: Boolean) {
        AppLog.d("设置音频输出模式: ${if (isSpeakerMode) "扬声器" else "听筒"}")

        // 通知接收服务更新音频输出模式
        val intent = Intent(getApplication(), ReceivingService::class.java).apply {
            action = ReceivingService.ACTION_SET_AUDIO_OUTPUT_MODE
            putExtra(ReceivingService.EXTRA_SPEAKER_MODE, isSpeakerMode)
        }
        getApplication<Application>().startService(intent)
    }



    /**
     * 切换服务器状态
     */
    fun toggleServer() {
        viewModelScope.launch {
            val isRunning = isReceivingServiceRunning.value ?: false

            if (isRunning) {
                stopServer()
            } else {
                startServer()
            }
        }
    }

    /**
     * 启动服务器
     */
    private fun startServer() {
        val port = _port.value ?: DEFAULT_PORT

        if (port <= 0 || port > 65535) {
            _toastMessage.value = "请输入有效的端口号 (1-65535)"
            return
        }

        _isLoading.value = true

        try {
            // 直接启动接收服务
            val intent = Intent(getApplication(), ReceivingService::class.java).apply {
                action = ReceivingService.ACTION_START
                putExtra(ReceivingService.EXTRA_PORT, port)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                getApplication<Application>().startForegroundService(intent)
            } else {
                getApplication<Application>().startService(intent)
            }

            stateManager.updateServiceState("receiving", true, port)
            _toastMessage.value = "服务器启动成功，端口: $port"
            AppLog.d("启动接收服务器，端口: $port")
        } catch (e: Exception) {
            AppLog.e("启动服务器失败", e)
            _toastMessage.value = "启动服务器失败: ${e.message}"
        } finally {
            _isLoading.value = false
        }
    }

    /**
     * 停止服务器
     */
    private fun stopServer() {
        _isLoading.value = true

        try {
            // 🚀 立即UI状态响应：用户点击停止按钮后立即更新UI状态（不移除窗口）
            AppLog.d("【立即UI响应】用户点击停止服务器，立即更新UI状态")
            stateManager.updateServiceState("receiving", false)
            _toastMessage.value = "服务器已停止"
            AppLog.d("【立即UI响应】服务器状态已立即更新")

            // 🚀 异步优化：确保正确的执行顺序，避免MediaCodec错误
            viewModelScope.launch(Dispatchers.IO) {
                try {
                    AppLog.d("【异步处理】开始后台异步处理，确保MediaCodec安全停止")

                    // 第一步：异步发送断开连接消息
                    AppLog.d("【异步断开】异步发送断开连接消息")
                    ReceivingService.immediateNotifyDisconnectFromExternal()
                    AppLog.d("【异步断开】断开连接消息已异步发送完成")

                    // 第二步：异步停止MediaCodec解码器（关键步骤）
                    AppLog.d("【异步停止解码器】异步停止所有MediaCodec解码器")
                    ReceivingService.immediateStopAllDecodersFromExternal()
                    AppLog.d("【异步停止解码器】MediaCodec解码器已异步停止")

                    // 第三步：在解码器安全停止后，立即移除UI窗口
                    withContext(Dispatchers.Main) {
                        AppLog.d("【安全UI清理】在解码器停止后立即移除投屏窗口")
                        WindowSettingsManager.getInstance().removeAllTextureViews()
                        AppLog.d("【安全UI清理】投屏窗口已安全移除完成")
                    }

                    // 第四步：异步停止接收服务
                    AppLog.d("【异步停止服务】异步停止接收服务")
                    val intent = Intent(getApplication(), ReceivingService::class.java).apply {
                        action = ReceivingService.ACTION_STOP
                    }
                    getApplication<Application>().startService(intent)
                    AppLog.d("【异步停止服务】接收服务已异步停止")

                    AppLog.d("【异步处理】所有后台异步处理已完成")
                } catch (e: Exception) {
                    AppLog.e("【异步处理】后台异步处理失败", e)
                }
            }

            AppLog.d("【立即响应】停止服务器的立即响应部分已完成，后台异步处理中")
        } catch (e: Exception) {
            AppLog.e("停止服务器失败", e)
            _toastMessage.value = "停止服务器失败: ${e.message}"
        } finally {
            _isLoading.value = false
        }
    }

    /**
     * 加载固定端口WebSocket服务器状态
     */
    private fun loadFixedWebSocketState() {
        // 从SharedPreferences加载保存的状态
        val sharedPrefs = getApplication<Application>().getSharedPreferences("receiver_settings", android.content.Context.MODE_PRIVATE)
        val savedState = sharedPrefs.getBoolean("fixed_websocket_enabled", false)

        // 检查服务实际运行状态
        val actualState = RemoteReceiverService.isServiceRunning()

        AppLog.d("固定端口WebSocket服务器状态加载: 保存状态=$savedState, 实际状态=$actualState")

        // 如果用户之前开启了远程被控，但服务未运行，则自动启动
        if (savedState && !actualState) {
            AppLog.d("检测到远程被控开关之前是开启状态，自动启动7777端口WebSocket服务器")
            startFixedWebSocketServer()
        } else {
            _isFixedWebSocketRunning.value = actualState

            // 如果保存的状态与实际状态不一致，更新保存的状态
            if (savedState != actualState) {
                sharedPrefs.edit {
                    putBoolean("fixed_websocket_enabled", actualState)
                }
                AppLog.d("已同步固定端口WebSocket服务器状态: $actualState")
            }
        }

        AppLog.d("固定端口WebSocket服务器状态已加载完成")

        // 🚀 新增：监听Service状态变化，确保ViewModel状态与Service同步
        stateManager.addServiceStateListener("fixed_websocket_vm") { serviceName, isRunning ->
            if (serviceName == "fixed_websocket") {
                viewModelScope.launch(Dispatchers.Main) {
                    _isFixedWebSocketRunning.value = isRunning
                    AppLog.d("【状态同步】ViewModel已同步7777端口服务器状态: $isRunning")
                }
            }
        }
    }

    /**
     * 从UI启动固定端口WebSocket服务器
     */
    fun startFixedWebSocketServerFromUI() {
        if (_isFixedWebSocketRunning.value == true) {
            AppLog.d("固定端口WebSocket服务器已在运行，跳过启动")
            return
        }
        startFixedWebSocketServer()
    }

    /**
     * 从UI停止固定端口WebSocket服务器
     */
    fun stopFixedWebSocketServerFromUI() {
        if (_isFixedWebSocketRunning.value == false) {
            AppLog.d("固定端口WebSocket服务器未运行，跳过停止")
            return
        }
        stopFixedWebSocketServer()
    }

    /**
     * 启动固定端口WebSocket服务器
     */
    private fun startFixedWebSocketServer() {
        _isFixedWebSocketLoading.value = true

        try {
            val intent = Intent(getApplication(), RemoteReceiverService::class.java).apply {
                action = RemoteReceiverService.ACTION_START_REMOTE_CONTROL
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                getApplication<Application>().startForegroundService(intent)
            } else {
                getApplication<Application>().startService(intent)
            }

            _isFixedWebSocketRunning.value = true
            saveFixedWebSocketState(true)
            AppLog.d("启动远程控制服务器成功")
        } catch (e: Exception) {
            AppLog.e("启动远程控制服务器失败", e)
            _toastMessage.value = "启动远程控制服务器失败: ${e.message}"
            _isFixedWebSocketRunning.value = false
        } finally {
            _isFixedWebSocketLoading.value = false
        }
    }

    /**
     * 停止固定端口WebSocket服务器
     */
    private fun stopFixedWebSocketServer() {
        _isFixedWebSocketLoading.value = true

        try {
            val intent = Intent(getApplication(), RemoteReceiverService::class.java).apply {
                action = RemoteReceiverService.ACTION_STOP_REMOTE_CONTROL
            }

            getApplication<Application>().startService(intent)

            // 🚀 修复时序问题：不在这里立即设置状态，等待Service完成停止后通过状态监听器更新
            // _isFixedWebSocketRunning.value = false // 移除立即设置
            saveFixedWebSocketState(false)
            AppLog.d("停止远程控制服务器请求已发送")
        } catch (e: Exception) {
            AppLog.e("停止远程控制服务器失败", e)
            _toastMessage.value = "停止远程控制服务器失败: ${e.message}"
        } finally {
            _isFixedWebSocketLoading.value = false
        }
    }

    /**
     * 保存固定端口WebSocket服务器状态
     */
    private fun saveFixedWebSocketState(enabled: Boolean) {
        val sharedPrefs = getApplication<Application>().getSharedPreferences("receiver_settings", android.content.Context.MODE_PRIVATE)
        sharedPrefs.edit {
            putBoolean("fixed_websocket_enabled", enabled)
        }
        AppLog.d("固定端口WebSocket服务器状态已保存: $enabled")
    }

    /**
     * 验证端口号
     */
    fun validatePort(portText: String): Boolean {
        return try {
            val port = portText.toInt()
            port in 1..65535
        } catch (_: NumberFormatException) {
            false
        }
    }

    override fun onCleared() {
        super.onCleared()
        // 移除观察者以防止内存泄漏
        isReceivingServiceRunning.removeObserver(serviceStatusObserver)
        // 🚀 清理服务状态监听器
        stateManager.removeServiceStateListener("fixed_websocket_vm")
        AppLog.d("ReceiverViewModel已清理（设置窗口关闭，接收服务继续运行）")
    }
}
