package com.example.castapp.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.example.castapp.manager.StateManager

import kotlinx.coroutines.launch
import java.lang.ref.WeakReference
import com.example.castapp.utils.AppLog

/**
 * 主界面ViewModel
 * 管理主界面的状态和业务逻辑
 */
class MainViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        // 安全的弱引用，避免内存泄露
        @Volatile
        private var instanceRef: WeakReference<MainViewModel>? = null

        /**
         * 设置ViewModel实例（使用WeakReference）
         */
        fun setInstance(viewModel: MainViewModel) {
            instanceRef = WeakReference(viewModel)
            AppLog.d("设置MainViewModel弱引用实例")
        }

        /**
         * 获取ViewModel实例（安全访问）
         * @return MainViewModel实例，如果已被GC回收则返回null
         */
        fun getInstance(): MainViewModel? {
            val instance = instanceRef?.get()
            if (instance == null && instanceRef != null) {
                // 实例已被GC回收，清理失效引用
                instanceRef = null
                AppLog.d("MainViewModel实例已被GC回收，清理失效引用")
            }
            return instance
        }

        /**
         * 清理静态引用
         */
        fun clearInstance() {
            instanceRef?.clear()
            instanceRef = null
            AppLog.d("清理MainViewModel静态引用")
        }
    }

    private val stateManager = StateManager.getInstance(application)

    private val _showSenderDialog = MutableLiveData<Boolean>()
    val showSenderDialog: LiveData<Boolean> = _showSenderDialog

    private val _showReceiverDialog = MutableLiveData<Boolean>()
    val showReceiverDialog: LiveData<Boolean> = _showReceiverDialog

    private val _showRemoteControlManagerDialog = MutableLiveData<Boolean>()
    val showRemoteControlManagerDialog: LiveData<Boolean> = _showRemoteControlManagerDialog

    private val _toastMessage = MutableLiveData<String>()
    val toastMessage: LiveData<String> = _toastMessage

    private val _newConnectionEvent = MutableLiveData<String>()
    val newConnectionEvent: LiveData<String> = _newConnectionEvent

    private val _removeConnectionEvent = MutableLiveData<String>()
    val removeConnectionEvent: LiveData<String> = _removeConnectionEvent

    private val _removeAllConnectionsEvent = MutableLiveData<Boolean>()
    val removeAllConnectionsEvent: LiveData<Boolean> = _removeAllConnectionsEvent

    // 暴露状态管理器的LiveData
    val castingConnections: LiveData<Set<String>> = stateManager.castingConnections

    init {
        // 设置静态实例引用
        setInstance(this)
        AppLog.d("MainViewModel初始化完成")
    }



    /**
     * 显示发送端对话框
     */
    fun showSenderDialog() {
        _showSenderDialog.value = true
        AppLog.d("请求显示发送端对话框")
    }

    /**
     * 隐藏发送端对话框
     */
    fun hideSenderDialog() {
        _showSenderDialog.value = false
        AppLog.d("隐藏发送端对话框")
    }

    /**
     * 显示接收端对话框
     */
    fun showReceiverDialog() {
        _showReceiverDialog.value = true
        AppLog.d("请求显示接收端对话框")
    }

    /**
     * 隐藏接收端对话框
     */
    fun hideReceiverDialog() {
        _showReceiverDialog.value = false
        AppLog.d("隐藏接收端对话框")
    }

    /**
     * 显示遥控管理对话框
     */
    fun showRemoteControlManagerDialog() {
        _showRemoteControlManagerDialog.value = true
        AppLog.d("请求显示遥控管理对话框")
    }

    /**
     * 隐藏遥控管理对话框
     */
    fun hideRemoteControlManagerDialog() {
        _showRemoteControlManagerDialog.value = false
        AppLog.d("隐藏遥控管理对话框")
    }

    /**
     * 处理新连接事件
     */
    fun handleNewConnection(connectionId: String) {
        viewModelScope.launch {
            _newConnectionEvent.value = connectionId
            AppLog.d("处理新连接事件: connectionId=$connectionId")
        }
    }

    /**
     * 处理连接断开事件
     */
    fun handleConnectionDisconnected(connectionId: String) {
        viewModelScope.launch {
            // 使用postValue确保每个事件都能被处理，即使是快速连续的事件
            _removeConnectionEvent.postValue(connectionId)
            // 注意：这里不显示"连接已断开"的Toast，因为可能只是视频流停止，连接仍然存在用于音频
            AppLog.d("处理连接断开事件（移除投屏窗口）: connectionId=$connectionId")
        }
    }

    // 添加屏幕分辨率事件的LiveData
    private val _screenResolutionEvent = MutableLiveData<Triple<String, Int, Int>>()
    val screenResolutionEvent: LiveData<Triple<String, Int, Int>> = _screenResolutionEvent

    // 添加设备信息更新事件的LiveData
    private val _deviceInfoUpdatedEvent = MutableLiveData<String>()
    val deviceInfoUpdatedEvent: LiveData<String> = _deviceInfoUpdatedEvent

    // 🎯 横竖屏适配：添加视频方向变化事件的LiveData（包含分辨率信息）
    private val _videoOrientationChangedEvent = MutableLiveData<Triple<String, Int, Pair<Int, Int>>>()
    val videoOrientationChangedEvent: LiveData<Triple<String, Int, Pair<Int, Int>>> = _videoOrientationChangedEvent

    /**
     * 处理屏幕分辨率事件
     */
    fun handleScreenResolution(connectionId: String, width: Int, height: Int) {
        viewModelScope.launch {
            _screenResolutionEvent.postValue(Triple(connectionId, width, height))
            AppLog.d("处理屏幕分辨率事件: $connectionId ${width}x${height}")
        }
    }

    /**
     * 通知设备信息已更新
     */
    fun notifyDeviceInfoUpdated(connectionId: String) {
        viewModelScope.launch {
            _deviceInfoUpdatedEvent.postValue(connectionId)
            AppLog.d("通知设备信息已更新: $connectionId")
        }
    }

    /**
     * 🎯 横竖屏适配：处理视频方向变化事件（包含分辨率信息）
     */
    fun handleVideoOrientationChanged(connectionId: String, orientation: Int, videoWidth: Int = 0, videoHeight: Int = 0) {
        viewModelScope.launch {
            _videoOrientationChangedEvent.postValue(Triple(connectionId, orientation, Pair(videoWidth, videoHeight)))
            AppLog.d("🎯 处理视频方向变化事件: $connectionId, 方向: $orientation, 分辨率: ${videoWidth}×${videoHeight}")
        }
    }

    override fun onCleared() {
        super.onCleared()
        // 清理静态引用
        clearInstance()
        AppLog.d("MainViewModel已清理")
    }
}
