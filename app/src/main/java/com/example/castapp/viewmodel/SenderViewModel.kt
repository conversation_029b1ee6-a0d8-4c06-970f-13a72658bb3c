package com.example.castapp.viewmodel

import android.app.Application
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.example.castapp.manager.MediaProjectionManager
import com.example.castapp.model.Connection
import com.example.castapp.manager.StateManager
import com.example.castapp.manager.WebSocketManager
import com.example.castapp.service.CastingService
import com.example.castapp.service.AudioService
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentHashMap
import com.example.castapp.utils.AppLog

/**
 * 发送端ViewModel
 * 管理发送端连接列表和投屏状态
 */
class SenderViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        // 静态回调机制，用于CastingService通知ViewModel
        private var resolutionAdjustmentCallback: ((Int, Boolean, String?) -> Unit)? = null

        /**
         * 注册分辨率调整完成回调
         */
        fun registerResolutionAdjustmentCallback(callback: (Int, Boolean, String?) -> Unit) {
            resolutionAdjustmentCallback = callback
        }

        /**
         * 取消注册分辨率调整完成回调
         */
        fun unregisterResolutionAdjustmentCallback() {
            resolutionAdjustmentCallback = null
        }

        /**
         * 通知分辨率调整完成（供CastingService调用）
         */
        fun notifyResolutionAdjustmentComplete(scalePercent: Int, success: Boolean, error: String?) {
            AppLog.d("收到分辨率调整完成通知: 缩放=$scalePercent%, 成功=$success, 错误=$error")
            AppLog.d("回调是否已注册: ${resolutionAdjustmentCallback != null}")
            resolutionAdjustmentCallback?.invoke(scalePercent, success, error)
            AppLog.d("已调用回调函数")
        }
    }

    private val stateManager = StateManager.getInstance(application)
    private val webSocketManager = WebSocketManager.getInstance()

    // 暴露StateManager的LiveData
    val connections: LiveData<List<Connection>> = stateManager.connections
    val castingConnections: LiveData<Set<String>> = stateManager.castingConnections

    private val _showAddReceiverDialog = MutableLiveData<Boolean>()
    val showAddReceiverDialog: LiveData<Boolean> = _showAddReceiverDialog

    private val _toastMessage = MutableLiveData<String>()
    val toastMessage: LiveData<String> = _toastMessage

    private val _requestMediaProjection = MutableLiveData<Pair<Connection, String>?>()
    val requestMediaProjection: LiveData<Pair<Connection, String>?> = _requestMediaProjection

    // LiveData for resolution adjustment state
    private val _resolutionAdjustmentState = MutableLiveData<ResolutionAdjustmentState>()
    val resolutionAdjustmentState: LiveData<ResolutionAdjustmentState> = _resolutionAdjustmentState

    // LiveData for remote control UI updates
    private val _remoteControlUIUpdate = MutableLiveData<RemoteControlUIUpdate>()
    val remoteControlUIUpdate: LiveData<RemoteControlUIUpdate> = _remoteControlUIUpdate

    // 存储待处理的权限数据（投屏ID -> (resultCode, resultData)）
    private val pendingPermissionData = ConcurrentHashMap<String, Pair<Int, Intent>>()

    /**
     * 分辨率调整状态密封类
     */
    sealed class ResolutionAdjustmentState {
        data class Success(val scalePercent: Int) : ResolutionAdjustmentState()
        data class Failed(val scalePercent: Int, val error: String, val retryCount: Int = 0) : ResolutionAdjustmentState()
        data class InProgress(val scalePercent: Int) : ResolutionAdjustmentState()
    }

    /**
     * 远程控制UI更新密封类
     */
    sealed class RemoteControlUIUpdate {
        data class BitrateUpdate(val bitrateMbps: Int) : RemoteControlUIUpdate()
        data class ResolutionUpdate(val scalePercent: Int) : RemoteControlUIUpdate()
        data class VolumeUpdate(val volumeType: String, val volume: Int) : RemoteControlUIUpdate()
        data class ConnectionToggle(val functionType: String, val enabled: Boolean) : RemoteControlUIUpdate()
    }

    init {
        AppLog.d("SenderViewModel初始化完成")

        // 注册分辨率调整完成回调
        registerResolutionAdjustmentCallback { scalePercent, success, error ->
            handleResolutionAdjustmentComplete(scalePercent, success, error)
        }

        // 设置远程控制回调，确保即使UI窗口关闭也能响应远程控制
        setupRemoteControlCallbacks()
    }

    /**
     * 显示添加接收端对话框
     */
    fun showAddReceiverDialog() {
        _showAddReceiverDialog.value = true
    }

    /**
     * 隐藏添加接收端对话框
     */
    fun hideAddReceiverDialog() {
        _showAddReceiverDialog.value = false
    }

    /**
     * 添加新连接
     */
    fun addConnection(ipAddress: String, port: Int) {
        try {
            val newConnection = stateManager.addConnection(ipAddress, port)
            _toastMessage.value = "已添加连接: $ipAddress:$port (ID: ${newConnection.connectionId.takeLast(8)})"

            // 广播连接添加事件到远程控制端
            broadcastConnectionAdded(newConnection)
        } catch (e: Exception) {
            AppLog.e("添加连接失败", e)
            _toastMessage.value = "添加连接失败: ${e.message}"
        }
    }

    /**
     * 移除连接
     */
    fun removeConnection(connection: Connection) {
        viewModelScope.launch {
            try {
                val currentConnection = stateManager.getConnection(connection.connectionId)

                // 如果正在投屏，先停止投屏
                if (currentConnection?.isCasting == true) {
                    stopCasting(connection)
                }

                // 停止该连接的所有音频流
                if (currentConnection?.isMediaAudioEnabled == true) {
                    stopAudioService(connection.connectionId, AudioService.ACTION_STOP_MEDIA_AUDIO)
                }

                if (currentConnection?.isMicAudioEnabled == true) {
                    stopAudioService(connection.connectionId, AudioService.ACTION_STOP_MIC_AUDIO)
                }

                stateManager.removeConnection(connection)
                _toastMessage.value = "已移除连接: ${connection.getDisplayText()}"

                // 广播连接删除事件到远程控制端
                broadcastConnectionRemoved(connection.connectionId)

            } catch (e: Exception) {
                AppLog.e("移除连接失败", e)
                _toastMessage.value = "移除连接失败: ${e.message}"
            }
        }
    }

    /**
     * 切换投屏状态
     */
    fun toggleCasting(connection: Connection, shouldCast: Boolean) {
        viewModelScope.launch {
            try {
                if (shouldCast) {
                    val webSocketConnected = establishWebSocketConnectionFirst(
                        connection = connection,
                        functionType = WebSocketManager.FUNCTION_VIDEO
                    )

                    if (!webSocketConnected) {
                        updateConnectionState(connection.connectionId) { conn ->
                            conn.withCasting(false).withWebSocketConnection(false)
                        }
                        _toastMessage.value = "连接失败，无法投屏到: ${connection.getDisplayText()}"
                        return@launch
                    }

                    startCasting(connection)
                } else {
                    stopCasting(connection)
                }
            } catch (e: Exception) {
                handleOperationError("切换投屏状态失败", e, shouldCast) {
                    updateConnectionState(connection.connectionId) { conn -> conn.withCasting(false) }
                }
            }
        }
    }

    /**
     * 开始投屏 - WebSocket连接已建立，现在检查权限并启动服务
     */
    private fun startCasting(connection: Connection) {
        val connectionId = connection.connectionId

        // 清理该连接的断开标记，允许重连
        com.example.castapp.service.ReceivingService.clearDisconnectionFlag(connectionId)

        // 获取更新后的连接对象
        val updatedConnection = stateManager.connections.value?.find {
            it.connectionId == connection.connectionId
        } ?: connection

        val mediaProjectionManager = MediaProjectionManager.getInstance(getApplication())

        // Android 14+设备每次投屏都重新请求权限
        if (Build.VERSION.SDK_INT >= 34) {
            mediaProjectionManager.clearCachedPermission()
            _requestMediaProjection.value = Pair(updatedConnection, "SCREEN_CASTING")
        } else {
            // Android 14以下设备可以复用权限
            if (mediaProjectionManager.hasValidPermission()) {
                val cachedData = mediaProjectionManager.getCachedPermissionData()
                if (cachedData != null) {
                    handleUnifiedMediaProjectionResult(updatedConnection, "SCREEN_CASTING", true, cachedData.first, cachedData.second)
                } else {
                    _requestMediaProjection.value = Pair(updatedConnection, "SCREEN_CASTING")
                }
            } else {
                _requestMediaProjection.value = Pair(updatedConnection, "SCREEN_CASTING")
            }
        }
    }

    /**
     * 优先建立WebSocket连接
     */
    private suspend fun establishWebSocketConnectionFirst(
        connection: Connection,
        functionType: String
    ): Boolean {
        return try {
            val connectionId = connection.connectionId

            // 检查是否已有活跃的WebSocket连接
            if (webSocketManager.isConnectionActive(connectionId)) {
                return withContext(Dispatchers.IO) {
                    webSocketManager.startFunction(connection, functionType)
                }
            }

            _toastMessage.value = "正在连接到: ${connection.getDisplayText()}"

            // 在IO线程中建立网络连接
            withContext(Dispatchers.IO) {
                webSocketManager.startFunction(connection, functionType)
            }

        } catch (e: Exception) {
            AppLog.e("WebSocket连接建立异常", e)
            false
        }
    }

    /**
     * 停止投屏
     */
    private fun stopCasting(connection: Connection) {
        val intent = Intent(getApplication(), CastingService::class.java).apply {
            action = CastingService.ACTION_STOP_CASTING
            putExtra(CastingService.EXTRA_CONNECTION_ID, connection.connectionId)
        }
        getApplication<Application>().startService(intent)

        webSocketManager.stopFunction(connection, WebSocketManager.FUNCTION_VIDEO)
        updateConnectionState(connection.connectionId) { conn -> conn.withCasting(false) }
        _toastMessage.value = "停止投屏到: ${connection.getDisplayText()}"
    }

    /**
     * 处理统一MediaProjection权限结果
     */
    fun handleUnifiedMediaProjectionResult(
        connection: Connection?,
        featureType: String,
        granted: Boolean,
        resultCode: Int = 0,
        resultData: Intent? = null
    ) {
        viewModelScope.launch {
            _requestMediaProjection.value = null

            if (!granted) {
                val message = when (featureType) {
                    "SCREEN_CASTING" -> "需要屏幕录制权限才能投屏"
                    "MEDIA_AUDIO" -> "需要屏幕录制权限才能使用媒体音频功能"
                    else -> "需要屏幕录制权限"
                }
                _toastMessage.value = message

                connection?.let {
                    if (featureType == "SCREEN_CASTING") {
                        updateConnectionState(it.connectionId) { conn -> conn.withCasting(false) }
                    }
                }
                return@launch
            }

            if (connection == null || resultData == null) {
                _toastMessage.value = if (connection == null) "连接信息丢失，无法启动功能" else "权限数据异常，无法启动功能"
                if (featureType == "SCREEN_CASTING" && connection != null) {
                    updateConnectionState(connection.connectionId) { conn -> conn.withCasting(false) }
                }
                return@launch
            }

            val mediaProjectionManager = MediaProjectionManager.getInstance(getApplication())
            val managerFeatureType = when (featureType) {
                "SCREEN_CASTING" -> MediaProjectionManager.FEATURE_SCREEN_CASTING
                "MEDIA_AUDIO" -> MediaProjectionManager.FEATURE_MEDIA_AUDIO
                else -> MediaProjectionManager.FEATURE_SCREEN_CASTING
            }

            val success = mediaProjectionManager.startMediaProjectionWithPermission(resultCode, resultData, managerFeatureType)

            if (!success) {
                _toastMessage.value = "权限保存失败，无法启动功能"
                if (featureType == "SCREEN_CASTING") {
                    updateConnectionState(connection.connectionId) { conn -> conn.withCasting(false) }
                }
                return@launch
            }

            when (featureType) {
                "SCREEN_CASTING" -> {
                    pendingPermissionData[connection.connectionId] = Pair(resultCode, resultData)
                    startCastingServiceAfterPermission(connection)
                }
                "MEDIA_AUDIO" -> {
                    startMediaAudioAfterPermission(connection, resultCode, resultData)
                }
            }
        }
    }

    /**
     * 权限授权后启动投屏服务
     */
    private suspend fun startCastingServiceAfterPermission(connection: Connection) {
        try {
            val connectionId = connection.connectionId
            val permissionData = pendingPermissionData[connectionId]

            if (permissionData == null) {
                withContext(Dispatchers.Main) {
                    _toastMessage.value = "权限数据丢失，无法启动投屏"
                }
                updateConnectionState(connectionId) { conn -> conn.withCasting(false) }
                return
            }

            val (resultCode, resultData) = permissionData

            val intent = withContext(Dispatchers.Default) {
                Intent(getApplication(), CastingService::class.java).apply {
                    action = CastingService.ACTION_START_CASTING
                    putExtra(CastingService.EXTRA_TARGET_IP, connection.ipAddress)
                    putExtra(CastingService.EXTRA_TARGET_PORT, connection.port)
                    putExtra(CastingService.EXTRA_CONNECTION_ID, connectionId)
                    putExtra(CastingService.EXTRA_RESULT_CODE, resultCode)
                    putExtra(CastingService.EXTRA_RESULT_DATA, resultData)
                }
            }

            withContext(Dispatchers.Main) {
                startServiceWithIntent(intent)
                _toastMessage.value = "正在启动投屏到: ${connection.getDisplayText()}"
            }

            updateConnectionState(connection.connectionId) { conn -> conn.withCasting(true) }
            pendingPermissionData.remove(connectionId)

        } catch (e: Exception) {
            handleOperationError("启动投屏服务失败", e) {
                updateConnectionState(connection.connectionId) { conn -> conn.withCasting(false) }
                pendingPermissionData.remove(connection.connectionId)
            }
        }
    }

    /**
     * 切换媒体音频状态
     */
    fun toggleMediaAudio(connection: Connection, shouldEnable: Boolean) {
        viewModelScope.launch {
            try {
                if (shouldEnable) {
                    val webSocketConnected = establishWebSocketConnectionFirst(
                        connection = connection,
                        functionType = WebSocketManager.FUNCTION_MEDIA_AUDIO
                    )

                    if (!webSocketConnected) {
                        updateConnectionState(connection.connectionId) { conn ->
                            conn.withMediaAudio(false).withWebSocketConnection(false)
                        }
                        _toastMessage.value = "连接失败，无法启用媒体音频: ${connection.getDisplayText()}"
                        return@launch
                    }

                    continueMediaAudioEnable(connection)
                } else {
                    stopAudioService(connection.connectionId, AudioService.ACTION_STOP_MEDIA_AUDIO)
                    updateConnectionState(connection.connectionId) { conn -> conn.withMediaAudio(false) }
                    _toastMessage.value = "关闭媒体音频: ${connection.getDisplayText()}"
                }

            } catch (e: Exception) {
                handleOperationError("切换媒体音频状态失败", e, shouldEnable) {
                    updateConnectionState(connection.connectionId) { conn -> conn.withMediaAudio(false) }
                }
            }
        }
    }

    /**
     * 继续媒体音频启用流程（WebSocket连接已确认）
     */
    private fun continueMediaAudioEnable(connection: Connection) {
        try {
            if (needsMediaAudioPermission()) {
                _requestMediaProjection.value = Pair(connection, "MEDIA_AUDIO")
                return
            }

            val intent = Intent(getApplication(), AudioService::class.java).apply {
                action = AudioService.ACTION_START_MEDIA_AUDIO
                putExtra(AudioService.EXTRA_CONNECTION_ID, connection.connectionId)
            }

            startServiceWithIntent(intent)
            updateConnectionState(connection.connectionId) { conn -> conn.withMediaAudio(true) }
            _toastMessage.value = "开启媒体音频: ${connection.getDisplayText()}"

        } catch (e: Exception) {
            handleOperationError("继续媒体音频启用失败", e) {
                updateConnectionState(connection.connectionId) { conn -> conn.withMediaAudio(false) }
            }
        }
    }



    /**
     * 检查是否需要为媒体音频申请MediaProjection权限
     */
    private fun needsMediaAudioPermission(): Boolean {
        val mediaProjectionManager = MediaProjectionManager.getInstance(getApplication())
        return !mediaProjectionManager.hasValidPermission()
    }

    /**
     * 媒体音频权限授权后的处理
     */
    private fun startMediaAudioAfterPermission(connection: Connection, resultCode: Int, resultData: Intent) {
        try {
            val permissionIntent = Intent(getApplication(), AudioService::class.java).apply {
                action = AudioService.ACTION_HANDLE_MEDIA_AUDIO_PERMISSION
                putExtra(AudioService.EXTRA_RESULT_CODE, resultCode)
                putExtra(AudioService.EXTRA_RESULT_DATA, resultData)
            }

            startServiceWithIntent(permissionIntent)

            viewModelScope.launch {
                kotlinx.coroutines.delay(500) // 等待权限处理完成
                startMediaAudioServiceDirectly(connection)
            }

        } catch (e: Exception) {
            AppLog.e("启动媒体音频功能失败", e)
            _toastMessage.value = "启动媒体音频失败: ${e.message}"
        }
    }

    /**
     * 直接启动媒体音频服务（WebSocket连接已建立，权限已授权）
     */
    private fun startMediaAudioServiceDirectly(connection: Connection) {
        try {
            updateConnectionState(connection.connectionId) { conn -> conn.withMediaAudio(true) }

            val intent = Intent(getApplication(), AudioService::class.java).apply {
                action = AudioService.ACTION_START_MEDIA_AUDIO
                putExtra(AudioService.EXTRA_CONNECTION_ID, connection.connectionId)
            }

            getApplication<Application>().startService(intent)
            _toastMessage.value = "开启媒体音频: ${connection.getDisplayText()}"

        } catch (e: Exception) {
            handleOperationError("直接启动媒体音频服务失败", e) {
                updateConnectionState(connection.connectionId) { conn -> conn.withMediaAudio(false) }
            }
        }
    }

    /**
     * 切换麦克风音频状态
     */
    fun toggleMicAudio(connection: Connection, shouldEnable: Boolean) {
        viewModelScope.launch {
            try {
                if (shouldEnable) {
                    val webSocketConnected = establishWebSocketConnectionFirst(
                        connection = connection,
                        functionType = WebSocketManager.FUNCTION_MIC_AUDIO
                    )

                    if (!webSocketConnected) {
                        updateConnectionState(connection.connectionId) { conn ->
                            conn.withMicAudio(false).withWebSocketConnection(false)
                        }
                        _toastMessage.value = "连接失败，无法启用麦克风音频: ${connection.getDisplayText()}"
                        return@launch
                    }

                    continueMicAudioEnable(connection)
                } else {
                    stopAudioService(connection.connectionId, AudioService.ACTION_STOP_MIC_AUDIO)
                    updateConnectionState(connection.connectionId) { conn -> conn.withMicAudio(false) }
                    _toastMessage.value = "关闭麦克风音频: ${connection.getDisplayText()}"
                }

            } catch (e: Exception) {
                handleOperationError("切换麦克风音频状态失败", e, shouldEnable) {
                    updateConnectionState(connection.connectionId) { conn -> conn.withMicAudio(false) }
                }
            }
        }
    }

    /**
     * 继续麦克风音频启用流程（WebSocket连接已确认）
     */
    private fun continueMicAudioEnable(connection: Connection) {
        try {
            val intent = Intent(getApplication(), AudioService::class.java).apply {
                action = AudioService.ACTION_START_MIC_AUDIO
                putExtra(AudioService.EXTRA_CONNECTION_ID, connection.connectionId)
            }

            startServiceWithIntent(intent)
            updateConnectionState(connection.connectionId) { conn -> conn.withMicAudio(true) }
            _toastMessage.value = "开启麦克风音频: ${connection.getDisplayText()}"

        } catch (e: Exception) {
            handleOperationError("继续麦克风音频启用失败", e) {
                updateConnectionState(connection.connectionId) { conn -> conn.withMicAudio(false) }
            }
        }
    }



    /**
     * 更新码率
     */
    fun updateBitRate(bitrateMbps: Int) {
        viewModelScope.launch {
            try {
                if (bitrateMbps < 5 || bitrateMbps > 40) {
                    _toastMessage.value = "码率超出有效范围: ${bitrateMbps}Mbps"
                    return@launch
                }

                val bitrateInBps = bitrateMbps * 1_000_000
                val activeConnections = stateManager.connections.value?.filter { connection ->
                    stateManager.getConnection(connection.connectionId)?.isCasting ?: false
                } ?: emptyList()

                val intent = Intent(getApplication(), CastingService::class.java).apply {
                    action = CastingService.ACTION_UPDATE_BITRATE
                    putExtra(CastingService.EXTRA_BITRATE, bitrateInBps)
                }

                val result = getApplication<Application>().startService(intent)
                _toastMessage.value = if (result != null && activeConnections.isNotEmpty()) {
                    "码率已调整为: ${bitrateMbps}Mbps"
                } else {
                    "码率设置已保存: ${bitrateMbps}Mbps"
                }

            } catch (e: Exception) {
                handleOperationError("调整码率失败", e)
            }
        }
    }

    /**
     * 更新分辨率缩放比例（带重试机制）
     */
    fun updateResolutionScale(scalePercent: Int, retryCount: Int = 0) {
        viewModelScope.launch {
            try {
                if (scalePercent < 1 || scalePercent > 150) {
                    _resolutionAdjustmentState.value = ResolutionAdjustmentState.Failed(
                        scalePercent, "分辨率缩放比例超出有效范围", retryCount
                    )
                    return@launch
                }

                _resolutionAdjustmentState.value = ResolutionAdjustmentState.InProgress(scalePercent)

                val activeConnections = stateManager.connections.value?.filter { connection ->
                    stateManager.getConnection(connection.connectionId)?.isCasting ?: false
                } ?: emptyList()

                if (activeConnections.isEmpty()) {
                    _toastMessage.value = "分辨率设置已保存: $scalePercent%（将在下次投屏时生效）"
                    _resolutionAdjustmentState.value = ResolutionAdjustmentState.Success(scalePercent)
                    return@launch
                }

                val intent = Intent(getApplication(), CastingService::class.java).apply {
                    action = CastingService.ACTION_UPDATE_RESOLUTION
                    putExtra(CastingService.EXTRA_RESOLUTION_SCALE, scalePercent)
                    putExtra(CastingService.EXTRA_RETRY_COUNT, retryCount)
                }

                val result = getApplication<Application>().startService(intent)
                if (result == null) {
                    _resolutionAdjustmentState.value = ResolutionAdjustmentState.Success(scalePercent)
                }

            } catch (e: Exception) {
                _resolutionAdjustmentState.value = ResolutionAdjustmentState.Failed(
                    scalePercent, e.message ?: "未知错误", retryCount
                )
            }
        }
    }

    /**
     * 处理接收端发送的分辨率调整完成通知
     */
    fun handleResolutionAdjustmentComplete(scalePercent: Int, success: Boolean, error: String?) {
        viewModelScope.launch {
            if (success) {
                _resolutionAdjustmentState.value = ResolutionAdjustmentState.Success(scalePercent)
                _toastMessage.value = "分辨率已调整为: $scalePercent%"
            } else {
                _resolutionAdjustmentState.value = ResolutionAdjustmentState.Failed(
                    scalePercent, error ?: "接收端调整失败", 0
                )
                _toastMessage.value = "分辨率调整失败: ${error ?: "未知错误"}"
            }
        }
    }

    /**
     * 更新媒体音频音量
     */
    fun updateMediaAudioVolume(volume: Int) {
        updateAudioVolume(volume, AudioService.ACTION_UPDATE_MEDIA_VOLUME, "媒体音频")
    }

    /**
     * 更新麦克风音频音量
     */
    fun updateMicAudioVolume(volume: Int) {
        updateAudioVolume(volume, AudioService.ACTION_UPDATE_MIC_VOLUME, "麦克风音频")
    }



    /**
     * 更新音频音量的公共方法
     */
    private fun updateAudioVolume(volume: Int, action: String, audioType: String) {
        viewModelScope.launch {
            try {
                if (volume < 0 || volume > 100) {
                    _toastMessage.value = "音量超出有效范围: $volume%"
                    return@launch
                }

                val intent = Intent(getApplication(), AudioService::class.java).apply {
                    this.action = action
                    putExtra(AudioService.EXTRA_VOLUME, volume)
                }

                val result = getApplication<Application>().startService(intent)
                _toastMessage.value = if (result != null) {
                    "${audioType}音量已调整为: $volume%"
                } else {
                    "${audioType}音量设置已保存: $volume%"
                }

            } catch (e: Exception) {
                handleOperationError("调整${audioType}音量失败", e)
            }
        }
    }

    /**
     * 停止音频服务的公共方法
     */
    private fun stopAudioService(connectionId: String, action: String) {
        val intent = Intent(getApplication(), AudioService::class.java).apply {
            this.action = action
            putExtra(AudioService.EXTRA_CONNECTION_ID, connectionId)
        }
        getApplication<Application>().startService(intent)
    }

    /**
     * 启动服务的公共方法
     */
    private fun startServiceWithIntent(intent: Intent) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            getApplication<Application>().startForegroundService(intent)
        } else {
            getApplication<Application>().startService(intent)
        }
    }

    /**
     * 更新连接状态的公共方法
     */
    private fun updateConnectionState(connectionId: String, update: (Connection) -> Connection) {
        stateManager.updateConnection(connectionId, update)
        // 状态更新后广播到远程控制端
        broadcastConnectionListUpdate()
    }

    /**
     * 处理操作错误的公共方法
     */
    private fun handleOperationError(
        message: String,
        exception: Exception,
        shouldResetState: Boolean = false,
        resetAction: (() -> Unit)? = null
    ) {
        AppLog.e(message, exception)
        _toastMessage.value = "操作失败: ${exception.message}"
        if (shouldResetState) {
            resetAction?.invoke()
        }
    }

    /**
     * 设置远程控制服务器回调
     * 确保即使UI窗口关闭，远程控制仍能正常工作
     */
    private fun setupRemoteControlCallbacks() {
        val remoteSenderServer = com.example.castapp.remote.RemoteSenderServer.getInstance()

        remoteSenderServer.setUIUpdateCallbacks(
            onBitrateUpdate = { bitrateMbps ->
                AppLog.d("远程控制更新码率: ${bitrateMbps}Mbps")
                // 直接更新码率设置
                updateBitRate(bitrateMbps)
                // 🔥 关键修复：确保状态保存到SharedPreferences，即使UI窗口关闭
                saveSettingToPreferences("bitrate_mbps", bitrateMbps)
                // 通知UI更新
                _remoteControlUIUpdate.postValue(RemoteControlUIUpdate.BitrateUpdate(bitrateMbps))
            },
            onResolutionUpdate = { scalePercent ->
                AppLog.d("远程控制更新分辨率: ${scalePercent}%")
                // 直接更新分辨率设置
                updateResolutionScale(scalePercent)
                // 🔥 关键修复：确保分辨率状态保存，即使UI窗口关闭
                // 分辨率状态由ResolutionManager管理，updateResolutionScale已经会保存状态
                // 但为了确保一致性，我们也保存到SharedPreferences
                saveSettingToPreferences("resolution_scale", scalePercent)
                // 通知UI更新
                _remoteControlUIUpdate.postValue(RemoteControlUIUpdate.ResolutionUpdate(scalePercent))
            },
            onVolumeUpdate = { volumeType, volume ->
                AppLog.d("远程控制更新音量: $volumeType = ${volume}%")
                when (volumeType) {
                    "media" -> {
                        updateMediaAudioVolume(volume)
                        // 🔥 关键修复：确保状态保存到SharedPreferences，即使UI窗口关闭
                        saveSettingToPreferences("media_audio_volume", volume)
                    }
                    "mic" -> {
                        updateMicAudioVolume(volume)
                        // 🔥 关键修复：确保状态保存到SharedPreferences，即使UI窗口关闭
                        saveSettingToPreferences("mic_audio_volume", volume)
                    }
                }
                // 通知UI更新
                _remoteControlUIUpdate.postValue(RemoteControlUIUpdate.VolumeUpdate(volumeType, volume))
            },
            onConnectionToggle = { functionType, enabled, targetConnectionId ->
                AppLog.d("远程控制切换连接: $functionType = $enabled, 目标: $targetConnectionId")
                handleRemoteConnectionToggle(functionType, enabled, targetConnectionId)
                // 通知UI更新
                _remoteControlUIUpdate.postValue(RemoteControlUIUpdate.ConnectionToggle(functionType, enabled))
            },
            onConnectionManagementRequest = { action, ipAddress, port, targetConnectionId ->
                AppLog.d("远程连接管理请求: $action, $ipAddress:$port, 目标: $targetConnectionId")
                handleRemoteConnectionManagementRequest(action, ipAddress, port, targetConnectionId)
            }
        )

        AppLog.d("SenderViewModel已设置远程控制回调")
    }

    /**
     * 处理远程连接切换请求
     */
    private fun handleRemoteConnectionToggle(functionType: String, enabled: Boolean, targetConnectionId: String?) {
        viewModelScope.launch {
            try {
                AppLog.d("处理远程连接切换: $functionType = $enabled, 目标连接: $targetConnectionId")

                // 根据是否指定目标连接ID来决定操作范围
                val connections = if (targetConnectionId != null) {
                    // 只操作指定的连接
                    val targetConnection = stateManager.getConnection(targetConnectionId)
                    if (targetConnection != null) listOf(targetConnection) else emptyList()
                } else {
                    // 操作所有连接
                    stateManager.getAllConnections()
                }

                connections.forEach { connection ->
                    when (functionType) {
                        "video" -> {
                            if (connection.isCasting != enabled) {
                                AppLog.d("远程控制切换投屏: ${connection.getDisplayText()} -> $enabled")
                                toggleCasting(connection, enabled)
                            }
                        }
                        "media_audio" -> {
                            if (connection.isMediaAudioEnabled != enabled) {
                                AppLog.d("远程控制切换媒体音频: ${connection.getDisplayText()} -> $enabled")
                                toggleMediaAudio(connection, enabled)
                            }
                        }
                        "mic_audio" -> {
                            if (connection.isMicAudioEnabled != enabled) {
                                AppLog.d("远程控制切换麦克风音频: ${connection.getDisplayText()} -> $enabled")
                                toggleMicAudio(connection, enabled)
                            }
                        }
                        else -> {
                            AppLog.w("未知的功能类型: $functionType")
                        }
                    }
                }

                // 广播连接列表状态更新
                broadcastConnectionListUpdate()

            } catch (e: Exception) {
                AppLog.e("处理远程连接切换失败: $functionType = $enabled", e)
            }
        }
    }

    /**
     * 广播连接列表状态更新到所有远程控制端
     */
    private fun broadcastConnectionListUpdate() {
        try {
            val connections = stateManager.getAllConnections()
            val connectionData = connections.map { connection ->
                mapOf(
                    "connectionId" to connection.connectionId,
                    "ipAddress" to connection.ipAddress,
                    "port" to connection.port,
                    "isCasting" to connection.isCasting,
                    "isMediaAudioEnabled" to connection.isMediaAudioEnabled,
                    "isMicAudioEnabled" to connection.isMicAudioEnabled,
                    "isConnected" to connection.isConnected,
                    "webSocketConnected" to connection.webSocketConnected
                )
            }

            // 使用设置同步消息格式，包含连接列表数据
            val settings = mapOf("connections" to connectionData)
            val remoteSenderServer = com.example.castapp.remote.RemoteSenderServer.getInstance()
            val message = com.example.castapp.websocket.ControlMessage.createRemoteSettingsSync(
                "broadcast",
                settings
            )
            remoteSenderServer.sendMessageToAllClients(message)

            AppLog.d("广播连接列表更新: ${connections.size} 个连接")
        } catch (e: Exception) {
            AppLog.e("广播连接列表更新失败", e)
        }
    }

    /**
     * 广播连接添加事件到所有远程控制端
     */
    private fun broadcastConnectionAdded(connection: Connection) {
        try {
            val connectionData = mapOf(
                "connectionId" to connection.connectionId,
                "ipAddress" to connection.ipAddress,
                "port" to connection.port,
                "isCasting" to connection.isCasting,
                "isMediaAudioEnabled" to connection.isMediaAudioEnabled,
                "isMicAudioEnabled" to connection.isMicAudioEnabled,
                "isConnected" to connection.isConnected,
                "webSocketConnected" to connection.webSocketConnected
            )

            val remoteSenderServer = com.example.castapp.remote.RemoteSenderServer.getInstance()
            val message = com.example.castapp.websocket.ControlMessage.createRemoteConnectionAdded(
                "broadcast",
                connectionData
            )
            remoteSenderServer.sendMessageToAllClients(message)

            AppLog.d("广播连接添加事件: ${connection.getDisplayText()}")
        } catch (e: Exception) {
            AppLog.e("广播连接添加事件失败", e)
        }
    }

    /**
     * 广播连接更新事件到所有远程控制端
     */
    fun broadcastConnectionUpdated(connection: Connection) {
        try {
            val connectionData = mapOf(
                "connectionId" to connection.connectionId,
                "ipAddress" to connection.ipAddress,
                "port" to connection.port,
                "isCasting" to connection.isCasting,
                "isMediaAudioEnabled" to connection.isMediaAudioEnabled,
                "isMicAudioEnabled" to connection.isMicAudioEnabled,
                "isConnected" to connection.isConnected,
                "webSocketConnected" to connection.webSocketConnected
            )

            val remoteSenderServer = com.example.castapp.remote.RemoteSenderServer.getInstance()
            val message = com.example.castapp.websocket.ControlMessage.createRemoteConnectionUpdated(
                "broadcast",
                connectionData
            )
            remoteSenderServer.sendMessageToAllClients(message)

            AppLog.d("广播连接更新事件: ${connection.getDisplayText()}")
        } catch (e: Exception) {
            AppLog.e("广播连接更新事件失败", e)
        }
    }

    /**
     * 广播连接删除事件到所有远程控制端
     */
    private fun broadcastConnectionRemoved(connectionId: String) {
        try {
            val remoteSenderServer = com.example.castapp.remote.RemoteSenderServer.getInstance()
            val message = com.example.castapp.websocket.ControlMessage.createRemoteConnectionRemoved(
                "broadcast",
                connectionId
            )
            remoteSenderServer.sendMessageToAllClients(message)

            AppLog.d("广播连接删除事件: $connectionId")
        } catch (e: Exception) {
            AppLog.e("广播连接删除事件失败", e)
        }
    }

    /**
     * 处理远程连接管理请求
     */
    private fun handleRemoteConnectionManagementRequest(action: String, ipAddress: String, port: Int, targetConnectionId: String?) {
        viewModelScope.launch {
            try {
                when (action) {
                    "add" -> {
                        AppLog.d("处理远程添加连接请求: $ipAddress:$port")
                        addConnection(ipAddress, port)
                    }
                    "edit" -> {
                        if (targetConnectionId != null) {
                            AppLog.d("处理远程编辑连接请求: $targetConnectionId -> $ipAddress:$port")
                            val connection = stateManager.getConnection(targetConnectionId)
                            if (connection != null) {
                                val updatedConnection = connection.copy(
                                    ipAddress = ipAddress,
                                    port = port,
                                    lastUpdateTime = System.currentTimeMillis()
                                )
                                stateManager.updateExistingConnection(updatedConnection)
                                broadcastConnectionUpdated(updatedConnection)
                                _toastMessage.postValue("远程编辑连接成功: $ipAddress:$port")
                            } else {
                                _toastMessage.postValue("未找到要编辑的连接")
                            }
                        }
                    }
                    "delete" -> {
                        if (targetConnectionId != null) {
                            AppLog.d("处理远程删除连接请求: $targetConnectionId")
                            val connection = stateManager.getConnection(targetConnectionId)
                            if (connection != null) {
                                removeConnection(connection)
                            } else {
                                _toastMessage.postValue("未找到要删除的连接")
                            }
                        }
                    }
                    else -> {
                        AppLog.w("未知的连接管理操作: $action")
                    }
                }
            } catch (e: Exception) {
                AppLog.e("处理远程连接管理请求失败: $action", e)
                _toastMessage.postValue("远程连接管理操作失败: ${e.message}")
            }
        }
    }

    /**
     * 保存设置到SharedPreferences
     * 🔥 关键修复：确保远程控制状态变更能正确保存，即使UI窗口关闭
     */
    private fun saveSettingToPreferences(key: String, value: Any) {
        try {
            val sharedPrefs = getApplication<Application>().getSharedPreferences("cast_settings", Context.MODE_PRIVATE)
            sharedPrefs.edit().apply {
                when (value) {
                    is Int -> putInt(key, value)
                    is Boolean -> putBoolean(key, value)
                    is String -> putString(key, value)
                    is Float -> putFloat(key, value)
                    else -> AppLog.w("不支持的设置值类型: ${value::class.java.simpleName}")
                }
                apply() // 使用apply()异步保存，避免阻塞主线程
            }
            AppLog.d("远程控制状态已保存: $key = $value")
        } catch (e: Exception) {
            AppLog.e("保存远程控制状态失败: $key = $value", e)
        }
    }

    /**
     * 清理远程控制回调
     */
    private fun clearRemoteControlCallbacks() {
        val remoteSenderServer = com.example.castapp.remote.RemoteSenderServer.getInstance()
        remoteSenderServer.clearListeners()
        AppLog.d("SenderViewModel已清理远程控制回调")
    }

    override fun onCleared() {
        super.onCleared()

        // 清理远程控制回调
        clearRemoteControlCallbacks()

        // 清理分辨率调整回调
        unregisterResolutionAdjustmentCallback()

        AppLog.d("SenderViewModel资源清理完成")
    }
}
