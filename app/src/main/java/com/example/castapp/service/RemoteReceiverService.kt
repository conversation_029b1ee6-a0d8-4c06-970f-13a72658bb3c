package com.example.castapp.service

import android.app.Service
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Build
import android.os.IBinder
import com.example.castapp.utils.NotificationManager as AppNotificationManager
import com.example.castapp.remote.RemoteReceiverControlServer
import com.example.castapp.manager.StateManager
import com.example.castapp.utils.AppLog

/**
 * 🎛️ 远程接收端控制服务
 * 独立的前台服务，专门管理7777端口WebSocket连接
 * 完全独立于音视频服务的生命周期
 * 
 * 主要功能：
 * - 管理7777端口WebSocket服务器
 * - 处理远程接收端设置控制消息
 * - 提供持续的双向控制同步
 * - 不受音视频服务状态影响
 */
class RemoteReceiverService : Service() {

    companion object {
        // Service Actions
        const val ACTION_START_REMOTE_CONTROL = "com.example.castapp.START_REMOTE_CONTROL"
        const val ACTION_STOP_REMOTE_CONTROL = "com.example.castapp.STOP_REMOTE_CONTROL"
        
        // 服务状态
        @Volatile
        private var serviceInstance: RemoteReceiverService? = null
        
        /**
         * 检查服务是否运行中
         */
        fun isServiceRunning(): Boolean {
            return serviceInstance != null
        }
        
        /**
         * 获取服务实例
         */
        fun getInstance(): RemoteReceiverService? {
            return serviceInstance
        }
    }

    // 远程控制服务器
    private var remoteControlServer: RemoteReceiverControlServer? = null

    // 状态管理器
    private lateinit var stateManager: StateManager

    // 运行状态
    private var isRunning = false

    override fun onCreate() {
        super.onCreate()
        serviceInstance = this

        // 初始化状态管理器
        stateManager = StateManager.getInstance(application)

        // 创建通知渠道
        AppNotificationManager.createNotificationChannel(this, AppNotificationManager.NotificationType.REMOTE_CONTROL)

        AppLog.service("【远程控制服务】onCreate - 服务创建")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_REMOTE_CONTROL -> {
                startRemoteControlServer()
            }
            ACTION_STOP_REMOTE_CONTROL -> {
                stopRemoteControlServer()
            }
        }
        
        // 返回START_STICKY确保服务被系统杀死后能自动重启
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    /**
     * 启动远程控制服务器
     */
    private fun startRemoteControlServer() {
        if (isRunning) {
            AppLog.service("【远程控制服务】服务器已在运行")
            return
        }

        try {
            AppLog.service("【远程控制服务】启动远程控制服务器")

            // 启动前台服务
            val stopPendingIntent = AppNotificationManager.createServiceStopPendingIntent(
                this,
                RemoteReceiverService::class.java,
                ACTION_STOP_REMOTE_CONTROL
            )
            val notification = AppNotificationManager.createRemoteControlNotification(
                this,
                "远程控制服务运行中",
                stopPendingIntent
            )
            // Android 14+要求指定前台服务类型
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                startForeground(
                    AppNotificationManager.NotificationType.REMOTE_CONTROL.notificationId,
                    notification,
                    ServiceInfo.FOREGROUND_SERVICE_TYPE_SPECIAL_USE
                )
                AppLog.service("【远程控制服务】前台服务已启动（Android 14+，使用specialUse类型）")
            } else {
                startForeground(
                    AppNotificationManager.NotificationType.REMOTE_CONTROL.notificationId,
                    notification
                )
                AppLog.service("【远程控制服务】前台服务已启动")
            }

            // 创建并启动远程控制服务器
            remoteControlServer = RemoteReceiverControlServer.getInstance()

            // 设置应用上下文
            remoteControlServer!!.setApplicationContext(this)

            if (remoteControlServer!!.startServer()) {
                isRunning = true

                // 通知状态管理器
                stateManager.updateServiceState("fixed_websocket", true, 7777)

                AppLog.service("【远程控制服务】远程控制服务器启动成功")
            } else {
                throw Exception("远程控制服务器启动失败")
            }

        } catch (e: Exception) {
            AppLog.e("【远程控制服务】启动远程控制服务器失败", e)
            stopRemoteControlServer()
        }
    }

    /**
     * 停止远程控制服务器
     */
    private fun stopRemoteControlServer() {
        if (!isRunning) {
            AppLog.service("【远程控制服务】服务器未运行")
            return
        }

        try {
            AppLog.service("【远程控制服务】停止远程控制服务器")

            // 停止远程控制服务器
            remoteControlServer?.stopServer()
            remoteControlServer = null

            isRunning = false

            // 通知状态管理器
            stateManager.updateServiceState("fixed_websocket", false, 0)

            // 停止前台服务
            stopForeground(STOP_FOREGROUND_REMOVE)
            stopSelf()

            AppLog.service("【远程控制服务】远程控制服务器已停止")

        } catch (e: Exception) {
            AppLog.e("【远程控制服务】停止远程控制服务器失败", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        serviceInstance = null
        
        // 确保服务器完全停止
        if (isRunning) {
            remoteControlServer?.stopServer()
            remoteControlServer = null
            isRunning = false
        }
        
        AppLog.service("【远程控制服务】onDestroy - 服务销毁")
    }

    /**
     * 获取远程控制服务器实例
     */
    fun getRemoteControlServer(): RemoteReceiverControlServer? {
        return remoteControlServer
    }
}
