package com.example.castapp.service

import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Build
import android.os.IBinder
import com.example.castapp.ui.StopwatchWindow
import com.example.castapp.utils.NotificationManager as AppNotificationManager
import com.example.castapp.utils.AppLog

/**
 * 悬浮秒表服务
 * 负责管理系统级悬浮窗的生命周期
 */
class FloatingStopwatchService : Service() {
    companion object {
        const val ACTION_START_FLOATING_STOPWATCH = "action_start_floating_stopwatch"
        const val ACTION_STOP_FLOATING_STOPWATCH = "action_stop_floating_stopwatch"

        /**
         * 启动悬浮秒表服务
         */
        fun startService(context: Context) {
            val intent = Intent(context, FloatingStopwatchService::class.java).apply {
                action = ACTION_START_FLOATING_STOPWATCH
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
    }

    private var floatingWindow: StopwatchWindow? = null

    override fun onCreate() {
        super.onCreate()
        AppLog.service("悬浮秒表服务创建")
        // 使用统一的通知管理器创建通知渠道
        AppNotificationManager.createNotificationChannel(this, AppNotificationManager.NotificationType.FLOATING_STOPWATCH)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_FLOATING_STOPWATCH -> {
                startFloatingStopwatch()
            }
            ACTION_STOP_FLOATING_STOPWATCH -> {
                stopFloatingStopwatch()
            }
        }
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    /**
     * 启动悬浮秒表
     */
    private fun startFloatingStopwatch() {
        if (floatingWindow != null) return

        try {
            val notification = AppNotificationManager.createFloatingStopwatchNotification(this)

            // Android 14+要求指定前台服务类型
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                startForeground(
                    AppNotificationManager.NotificationType.FLOATING_STOPWATCH.notificationId,
                    notification,
                    ServiceInfo.FOREGROUND_SERVICE_TYPE_SPECIAL_USE
                )
                AppLog.service("悬浮秒表前台服务已启动（Android 14+，使用specialUse类型）")
            } else {
                startForeground(
                    AppNotificationManager.NotificationType.FLOATING_STOPWATCH.notificationId,
                    notification
                )
                AppLog.service("悬浮秒表前台服务已启动")
            }

            floatingWindow = StopwatchWindow(this) {
                stopFloatingStopwatch()
            }
            floatingWindow?.show()

            AppLog.service("悬浮秒表启动")
        } catch (e: Exception) {
            AppLog.e("启动悬浮秒表失败", e)
            stopSelf()
        }
    }

    /**
     * 停止悬浮秒表
     */
    private fun stopFloatingStopwatch() {
        try {
            floatingWindow?.hide()
            floatingWindow = null
            stopForeground(STOP_FOREGROUND_REMOVE)
            stopSelf()
            AppLog.service("悬浮秒表停止")
        } catch (e: Exception) {
            AppLog.e("停止悬浮秒表失败", e)
        }
    }

    // 通知创建方法已移至统一的NotificationManager

    override fun onDestroy() {
        super.onDestroy()
        try {
            floatingWindow?.hide()
            floatingWindow = null
        } catch (e: Exception) {
            AppLog.e("清理悬浮窗失败", e)
        }
        AppLog.service("悬浮秒表服务销毁")
    }
}
