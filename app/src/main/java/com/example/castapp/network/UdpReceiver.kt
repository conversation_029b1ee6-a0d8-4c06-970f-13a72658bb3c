package com.example.castapp.network

import android.os.Build
import java.net.DatagramPacket
import java.net.DatagramSocket
import java.net.SocketTimeoutException
import java.util.concurrent.atomic.AtomicBoolean
import com.example.castapp.utils.ResourceManager
import com.example.castapp.utils.ResourceManager.safeExecute
import com.example.castapp.utils.AppLog

/**
 * 数据视图接口 - 零拷贝数据访问
 */
interface DataView {
    val size: Int
    val offset: Int
    fun toByteArray(): ByteArray
    fun copyTo(dest: ByteArray, destOffset: Int = 0, length: Int = size)
    fun getByte(index: Int): Byte

    // 🚀 零拷贝优化：支持源偏移量的拷贝方法
    fun copyTo(dest: ByteArray, destOffset: Int, srcOffset: Int, length: Int)
    fun copyTo(dest: java.nio.ByteBuffer, srcOffset: Int, length: Int)
}



/**
 * UDP接收器 - 零拷贝优化版本
 */
class UdpReceiver(
    private val port: Int,
    private val onDataReceived: (DataView) -> Unit
) {
    companion object {
        private const val SOCKET_TIMEOUT = 1 // 🚀 激进优化：减少到1ms，最大化响应性
        private const val RECEIVE_BUFFER_SIZE = 512 * 1024 // 🚀 减少到512KB，极限降低缓冲延迟

        // 🎯 简化监控：只保留关键性能指标
        private const val HIGH_LATENCY_THRESHOLD = 50 // 高延迟警告阈值50ms
        // 🚀 CPU优化：增加统计报告间隔，减少日志频率
        private const val STATS_REPORT_INTERVAL = 120000 // 统计报告间隔120秒
    }

    private var socket: DatagramSocket? = null
    private val isRunning = AtomicBoolean(false)
    private var receiveThread: Thread? = null

    // 🚀 智能缓冲区管理器
    private val smartBufferManager = SmartBufferManager.getInstance()

    // 🎯 简化监控：只保留关键统计指标
    private var packetsReceived = 0L
    private var receiveErrors = 0L
    private var lastStatsTime = System.currentTimeMillis()

    /**
     * 启动接收器
     */
    fun start(): Boolean {
        return safeExecute("启动UDP接收器") {
            socket = DatagramSocket(port).apply {
                soTimeout = SOCKET_TIMEOUT

                // 🚀 激进的接收缓冲区优化：最小化缓冲延迟
                receiveBufferSize = RECEIVE_BUFFER_SIZE

                // 🚀 超激进的网络优化设置
                trafficClass = 0x10 or 0x02 // IPTOS_LOWDELAY + IPTOS_THROUGHPUT
                reuseAddress = true

                // 🚀 尝试设置最激进的网络优化
                safeExecute("设置超激进socket选项") {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        // API 33+ 使用新的setOption方法
                        setOption(java.net.StandardSocketOptions.SO_RCVBUF, RECEIVE_BUFFER_SIZE)
                        setOption(java.net.StandardSocketOptions.SO_REUSEADDR, true)
                        // 尝试启用更多低延迟选项
                        try {
                            setOption(java.net.StandardSocketOptions.SO_BROADCAST, false)
                        } catch (_: Exception) {
                            // 某些选项可能不支持，忽略
                        }
                    }
                }

                // 验证缓冲区大小设置
                safeExecute("验证激进缓冲区设置") {
                    receiveBufferSize = RECEIVE_BUFFER_SIZE
                    AppLog.network("🚀 激进接收缓冲区: ${receiveBufferSize}字节 (目标: ${RECEIVE_BUFFER_SIZE})")
                }
            }
            isRunning.set(true)
            startReceiveLoop()
            AppLog.network("UDP接收器启动成功，监听端口: $port, 接收缓冲区: ${socket?.receiveBufferSize}字节")
            true
        } ?: false
    }

    /**
     * 停止接收器
     */
    fun stop() {
        isRunning.set(false)
        receiveThread?.interrupt()

        // 使用资源管理器安全关闭Socket
        ResourceManager.safeCloseDatagramSocket(socket, "UDP接收器Socket")
        socket = null
        receiveThread = null
        AppLog.network("UDP接收器已停止")
    }

    /**
     * 启动接收循环
     */
    private fun startReceiveLoop() {
        receiveThread = Thread({
            // 🚀 激进的线程优先级：最大化网络接收性能
            Thread.currentThread().priority = Thread.MAX_PRIORITY
            Thread.currentThread().name = "UdpReceiver-UltraLowLatency-$port"

            // 🚀 激进的进程优先级：网络接收最高优先级
            try {
                android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_URGENT_AUDIO)
            } catch (e: Exception) {
                // 回退到次高优先级
                try {
                    android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_MORE_FAVORABLE)
                    AppLog.w("使用次高优先级: ${e.message}")
                } catch (e2: Exception) {
                    AppLog.w("无法设置激进接收线程优先级: ${e2.message}")
                }
            }

            // 🎯 简化监控：只保留基础统计
            var consecutiveTimeouts = 0

            while (isRunning.get() && !Thread.currentThread().isInterrupted) {
                try {
                    // 🚀 智能缓冲区管理：获取智能缓冲区
                    val smartBuffer = smartBufferManager.acquireBuffer()

                    val packet = DatagramPacket(smartBuffer.data, smartBuffer.data.size)
                    val receiveStartTime = System.nanoTime()

                    // 直接调用socket.receive，无协程开销
                    socket?.receive(packet)

                    val receiveTime = (System.nanoTime() - receiveStartTime) / 1_000_000
                    consecutiveTimeouts = 0 // 重置超时计数
                    packetsReceived++

                    // 🚀 完全零拷贝：创建智能DataView，自动管理缓冲区生命周期
                    val receivedDataView = SmartDataView(
                        smartBuffer = smartBuffer,
                        offset = packet.offset,
                        size = packet.length
                    )

                    // 🎯 简化延迟监控：只在严重延迟时警告
                    if (receiveTime > HIGH_LATENCY_THRESHOLD) {
                        AppLog.w("UDP接收延迟过高: ${receiveTime}ms")
                    }

                    // 🎯 简化统计报告：每30秒输出基础统计
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastStatsTime > STATS_REPORT_INTERVAL) {
                        val packetsPerSecond = packetsReceived * 1000 / (currentTime - lastStatsTime)
                        AppLog.network("UDP接收统计: ${packetsPerSecond}包/秒, 总接收: ${packetsReceived}包, 错误: ${receiveErrors}次")

                        // 重置统计计数器
                        packetsReceived = 0
                        receiveErrors = 0
                        lastStatsTime = currentTime
                    }

                    // 🎯 零拷贝回调：直接传递数据视图，无任何拷贝开销
                    onDataReceived(receivedDataView)

                } catch (_: SocketTimeoutException) {
                    // 超时是正常的，但连续超时太多次需要让出CPU
                    consecutiveTimeouts++
                    if (consecutiveTimeouts > 20) {
                        Thread.yield() // 让出CPU
                        consecutiveTimeouts = 0
                    }
                    continue
                } catch (e: Exception) {
                    if (isRunning.get() && !Thread.currentThread().isInterrupted) {
                        receiveErrors++
                        AppLog.e("接收数据时出错", e)
                    }
                    break
                }
            }
        }, "UdpReceiver-$port")

        receiveThread?.start()
    }


}
