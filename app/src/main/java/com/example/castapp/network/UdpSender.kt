package com.example.castapp.network

import kotlinx.coroutines.*
import java.net.DatagramPacket
import java.net.DatagramSocket
import java.net.InetAddress
import java.util.concurrent.ConcurrentLinkedQueue
import com.example.castapp.utils.ResourceManager
import com.example.castapp.utils.ResourceManager.safeExecute
import com.example.castapp.utils.AppLog

/**
 * UDP发送器 - 零拷贝批量发送优化版本
 */
class UdpSender(
    private val targetIp: String,
    private val targetPort: Int
) {
    companion object {
        // 🔥 激进降温优化：网络参数调优
        private const val SOCKET_TIMEOUT = 500 // 🚀 进一步减少到0.5秒，降低阻塞时间
        private const val PACKET_POOL_SIZE = 64 // 🚀 大幅增加池大小，减少内存分配
        private const val BATCH_SIZE = 32 // 🚀 增大批量，减少系统调用频率
        private const val DIRECT_SEND_POOL_SIZE = 16 // 🚀 扩大直接发送池

        // 🔥 新增：智能批量控制
        private const val MIN_BATCH_SIZE = 4 // 最小批量大小
        private const val MAX_BATCH_SIZE = 64 // 最大批量大小

        // 🔥 新增：CPU友好的延迟控制
        private const val IDLE_DELAY_MS = 2L // 空闲时延迟2ms，降低CPU占用
        private const val BUSY_DELAY_MS = 0L // 繁忙时无延迟，保持吞吐量
    }

    private var socket: DatagramSocket? = null
    private var targetAddress: InetAddress? = null
    private val sendQueue = ConcurrentLinkedQueue<ByteArray>()
    private var isRunning = false
    private var sendJob: Job? = null

    // 使用专用的协程作用域，便于统一管理和取消
    private val senderScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // 🔥 智能队列管理：动态调整队列大小
    private var maxQueueSize = 1000
    private var queueOverflowCount = 0

    // 🔥 新增：自适应批量控制
    private var currentBatchSize = BATCH_SIZE
    private var consecutiveEmptyPolls = 0

    // 🔥 新增：性能统计
    private var totalPacketsSent = 0L
    private var totalBytesSent = 0L
    private var lastStatsTime = System.currentTimeMillis()

    // 🚀 零拷贝DatagramPacket池 - 预分配固定大小缓冲区
    private val packetPool = Array(PACKET_POOL_SIZE) {
        // 预分配最大包大小的缓冲区，避免运行时重新分配
        DatagramPacket(ByteArray(1500), 1500)  // 标准以太网MTU
    }
    private var packetPoolIndex = 0

    // 🚀 专用直接发送池 - 超低延迟路径专用
    private val directSendPool = Array(DIRECT_SEND_POOL_SIZE) {
        DatagramPacket(ByteArray(1500), 1500)
    }
    private var directPoolIndex = 0

    /**
     * 获取预分配的DatagramPacket对象，避免数据拷贝
     */
    private fun getPacketFromPool(): DatagramPacket {
        synchronized(packetPool) {
            val packet = packetPool[packetPoolIndex]
            packetPoolIndex = (packetPoolIndex + 1) % PACKET_POOL_SIZE
            return packet
        }
    }

    /**
     * 🚀 获取专用直接发送池的包 - 超低延迟路径
     */
    private fun getDirectPacketFromPool(): DatagramPacket {
        synchronized(directSendPool) {
            val packet = directSendPool[directPoolIndex]
            directPoolIndex = (directPoolIndex + 1) % DIRECT_SEND_POOL_SIZE
            return packet
        }
    }

    /**
     * 启动发送器
     */
    fun start(): Boolean {
        return safeExecute("启动UDP发送器") {
            targetAddress = InetAddress.getByName(targetIp)
            socket = DatagramSocket().apply {
                soTimeout = SOCKET_TIMEOUT

                // 🔥 激进降温优化：平衡性能与发热的缓冲区配置
                sendBufferSize = 256 * 1024 // 适度增加到256KB，减少系统调用频率
                receiveBufferSize = 64 * 1024 // 设置接收缓冲区，避免默认值过大

                // 🔥 优化网络配置：降低CPU开销
                trafficClass = 0x02 // 只设置IPTOS_THROUGHPUT，移除LOWDELAY减少CPU压力
                reuseAddress = true

                // 🔥 激进Socket优化：最大化网络效率
                safeExecute("设置降温优化Socket选项") {
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                        setOption(java.net.StandardSocketOptions.SO_SNDBUF, 512 * 1024) // 增大发送缓冲区
                        setOption(java.net.StandardSocketOptions.SO_RCVBUF, 64 * 1024)  // 控制接收缓冲区
                        setOption(java.net.StandardSocketOptions.SO_REUSEADDR, true)
                        // 🔥 尝试启用批量发送优化
                        try {
                            setOption(java.net.StandardSocketOptions.SO_KEEPALIVE, false) // 禁用keepalive减少开销
                        } catch (e: Exception) {
                            AppLog.w("无法设置SO_KEEPALIVE: ${e.message}")
                        }
                    }
                }

                // 🔥 新增：尝试设置更多性能优化选项
                safeExecute("设置高级网络优化") {
                    try {
                        // 尝试禁用Nagle算法等效功能（UDP本身没有，但某些实现可能有类似机制）
                        broadcast = false // 禁用广播，减少不必要的网络开销
                    } catch (e: Exception) {
                        AppLog.w("设置高级网络选项失败: ${e.message}")
                    }
                }
            }
            isRunning = true
            startSendLoop()
            AppLog.network("UDP发送器启动成功: $targetIp:$targetPort, 发送缓冲区: ${socket?.sendBufferSize}字节, 批量大小: $BATCH_SIZE")
            true
        } ?: false
    }

    /**
     * 停止发送器 - 🚀 优化同步机制，避免竞态条件
     */
    fun stop() {
        AppLog.network("开始停止UDP发送器...")

        // 🚀 优化：先设置停止标志
        isRunning = false

        // 🚀 优化：等待协程任务完成，避免竞态条件
        sendJob?.let { job ->
            safeExecute("取消UDP发送协程") {
                job.cancel()
                // 🚀 短暂等待协程完成，避免Socket关闭时还在发送
                try {
                    runBlocking {
                        withTimeout(1000) { // 最多等待1秒
                            job.join()
                        }
                    }
                    AppLog.network("UDP发送协程已安全停止")
                } catch (e: Exception) {
                    AppLog.network("UDP发送协程强制停止: ${e.message}")
                }
            }
        }
        sendJob = null

        // 🚀 优化：在协程停止后再关闭Socket
        ResourceManager.safeCloseDatagramSocket(socket, "UDP发送器Socket")
        socket = null

        // 清空发送队列
        sendQueue.clear()
        AppLog.network("UDP发送器已停止")
    }

    /**
     * 完全清理发送器资源，包括协程作用域
     * 在发送器不再使用时调用
     */
    fun cleanup() {
        AppLog.network("开始清理UdpSender资源...")

        // 先停止发送器
        stop()

        // 取消协程作用域
        try {
            senderScope.cancel()
            AppLog.network("UdpSender协程作用域已取消")
        } catch (e: Exception) {
            AppLog.e("取消UdpSender协程作用域时发生异常", e)
        }

        AppLog.network("UdpSender资源清理完成")
    }

    /**
     * 🔥 智能发送数据（动态队列管理）
     */
    fun send(data: ByteArray): Boolean {
        return if (isRunning) {
            // 🔥 动态调整队列大小：根据发送压力自适应
            val currentQueueSize = sendQueue.size
            if (currentQueueSize >= maxQueueSize) {
                // 🔥 智能丢弃策略：丢弃最旧的包
                sendQueue.poll()
                queueOverflowCount++

                // 🔥 自适应队列大小：如果频繁溢出，适度增加队列大小
                if (queueOverflowCount % 500 == 0) {
                    maxQueueSize = minOf(maxQueueSize + 200, 2000) // 最大不超过2000
                    AppLog.w("UDP队列溢出频繁，调整队列大小到: $maxQueueSize (已丢弃 $queueOverflowCount 包)")
                }

                // 🔥 减少日志频率，降低CPU开销
                if (queueOverflowCount % 2000 == 0) {
                    AppLog.w("UDP发送队列持续溢出，已丢弃 $queueOverflowCount 个数据包")
                }
            }

            // 🔥 智能批量提示：当队列较满时，增加批量大小
            if (currentQueueSize > maxQueueSize * 0.7) {
                currentBatchSize = minOf(currentBatchSize + 4, MAX_BATCH_SIZE)
            }

            sendQueue.offer(data)
        } else {
            false
        }
    }

    /**
     * 🔥 智能批量发送多个数据包（降温优化版本）
     */
    fun sendBatch(dataList: List<ByteArray>) {
        if (isRunning && dataList.isNotEmpty()) {
            safeExecute("智能批量发送UDP数据") {
                // 🔥 直接调用优化的批量发送方法
                sendPacketsBatchOptimized(dataList)
            }
        }
    }

    /**
     * 🚀 池化包直接发送（为RtpSender池化包优化）- 🚀 修复竞态条件
     * 使用预分配的DatagramPacket池，但避免数据拷贝
     */
    fun sendDirectZeroCopy(data: ByteArray, offset: Int, length: Int): Boolean {
        return if (isRunning) {
            safeExecute("池化包零拷贝发送") {
                // 🚀 修复竞态条件：检查Socket状态
                val socket = this.socket
                val targetAddr = this.targetAddress

                if (socket == null || socket.isClosed) {
                    AppLog.network("UDP Socket已关闭，跳过零拷贝发送")
                    return@safeExecute false
                }

                if (targetAddr == null) {
                    AppLog.network("目标地址未设置，跳过零拷贝发送")
                    return@safeExecute false
                }

                try {
                    // 🚀 使用专用直接发送池，但避免数据拷贝
                    val packet = getDirectPacketFromPool()
                    if (length <= packet.data.size) {
                        // 🚀 最小化拷贝：只拷贝实际需要的数据
                        System.arraycopy(data, offset, packet.data, 0, length)
                        packet.length = length
                        packet.address = targetAddr
                        packet.port = targetPort

                        socket.send(packet)
                        true
                    } else {
                        // 数据过大，直接创建包
                        val largePacket = DatagramPacket(data, offset, length, targetAddr, targetPort)
                        socket.send(largePacket)
                        true
                    }
                } catch (e: java.net.SocketException) {
                    if (e.message?.contains("Socket is closed") == true) {
                        AppLog.network("Socket已关闭，零拷贝发送失败")
                        false
                    } else {
                        throw e // 重新抛出其他Socket异常
                    }
                }
            } ?: false
        } else {
            false
        }
    }

    /**
     * 启动发送循环（批量优化版本）
     */
    private fun startSendLoop() {
        sendJob = senderScope.launch {
            // 设置较低的线程优先级以减少CPU占用和发热
            Thread.currentThread().priority = Thread.MIN_PRIORITY + 2

            // 设置后台进程优先级
            try {
                android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_BACKGROUND)
            } catch (e: Exception) {
                AppLog.w("无法设置UDP发送线程进程优先级: ${e.message}")
            }

            AppLog.network("UDP批量发送循环已启动，批量大小: $BATCH_SIZE")

            while (isRunning && isActive) {
                try {
                    // 🔥 智能批量收集：动态调整批量大小
                    val batchData = mutableListOf<ByteArray>()
                    val batchStartTime = System.currentTimeMillis()

                    // 首先尝试获取一个数据包
                    val firstData = sendQueue.poll()
                    if (firstData != null) {
                        batchData.add(firstData)
                        consecutiveEmptyPolls = 0 // 重置空轮询计数

                        // 🔥 智能收集：根据当前批量大小动态收集
                        var collected = 1
                        while (collected < currentBatchSize && isRunning && isActive) {
                            val nextData = sendQueue.poll()
                            if (nextData != null) {
                                batchData.add(nextData)
                                collected++
                            } else {
                                break
                            }
                        }

                        // 🔥 批量发送并更新统计
                        sendPacketsBatchOptimized(batchData)
                        updatePerformanceStats()

                        // 🔥 自适应批量大小调整
                        adaptBatchSize(System.currentTimeMillis() - batchStartTime)

                    } else {
                        // 🔥 智能空闲处理：根据连续空轮询次数调整延迟
                        consecutiveEmptyPolls++
                        val delayTime = if (consecutiveEmptyPolls > 10) {
                            IDLE_DELAY_MS // 连续空轮询较多时，增加延迟降低CPU占用
                        } else {
                            BUSY_DELAY_MS // 刚开始空轮询时，保持低延迟
                        }

                        if (delayTime > 0) {
                            delay(delayTime)
                        }

                        // 🔥 长时间空闲时，减小批量大小节省内存
                        if (consecutiveEmptyPolls > 50) {
                            currentBatchSize = maxOf(currentBatchSize - 2, MIN_BATCH_SIZE)
                        }
                    }
                } catch (e: Exception) {
                    if (isRunning) {
                        AppLog.e("智能批量发送数据时出错", e)
                    }
                    // 🔥 异常时短暂延迟，避免CPU飙升
                    delay(IDLE_DELAY_MS)
                }
            }
        }
    }

    /**
     * 🔥 优化的批量发送数据包（降温版本）- 🚀 修复竞态条件
     * 减少系统调用，降低CPU开销
     */
    private fun sendPacketsBatchOptimized(dataList: List<ByteArray>) {
        safeExecute("智能批量发送UDP数据包") {
            // 🚀 修复竞态条件：检查运行状态和Socket状态
            if (!isRunning) {
                AppLog.network("UDP发送器已停止，跳过批量发送")
                return@safeExecute
            }

            val socket = this.socket
            val targetAddr = this.targetAddress

            // 🚀 双重检查：确保Socket未关闭
            if (socket == null || socket.isClosed) {
                AppLog.network("UDP Socket已关闭，跳过批量发送")
                return@safeExecute
            }

            if (targetAddr == null) {
                AppLog.network("目标地址未设置，跳过批量发送")
                return@safeExecute
            }

            var totalBytes = 0

            // 🔥 零拷贝批量发送：优化内存访问模式
            for (data in dataList) {
                // 🚀 每次发送前检查状态，避免竞态条件
                if (!isRunning || socket.isClosed) {
                    AppLog.network("发送过程中检测到停止信号，中断批量发送")
                    break
                }

                try {
                    val packet = getPacketFromPool()

                    // 检查数据大小，确保不超过预分配缓冲区
                    if (data.size <= packet.data.size) {
                        // 🔥 零拷贝核心：使用copyInto，减少内存分配
                        data.copyInto(packet.data, 0, 0, data.size)
                        packet.length = data.size
                        packet.address = targetAddr
                        packet.port = targetPort

                        // 🔥 批量发送：减少系统调用开销
                        socket.send(packet)
                        totalBytes += data.size
                    } else {
                        // 🔥 大包处理：直接发送，避免额外拷贝
                        val largePacket = DatagramPacket(data, data.size, targetAddr, targetPort)
                        socket.send(largePacket)
                        totalBytes += data.size

                        // 🔥 减少大包警告频率
                        if (System.currentTimeMillis() - lastStatsTime > 30000) {
                            AppLog.w("发现大数据包: ${data.size} > ${packet.data.size}")
                        }
                    }
                } catch (e: java.net.SocketException) {
                    if (e.message?.contains("Socket is closed") == true) {
                        AppLog.network("Socket已关闭，停止发送")
                        break
                    } else {
                        throw e // 重新抛出其他Socket异常
                    }
                }
            }

            // 🔥 更新发送统计
            totalBytesSent += totalBytes
            totalPacketsSent += dataList.size
        }
    }

    /**
     * 🔥 新增：自适应批量大小调整
     */
    private fun adaptBatchSize(batchTime: Long) {
        // 🔥 根据发送时间和队列状态动态调整批量大小
        val queueSize = sendQueue.size

        when {
            // 队列积压严重，增加批量大小
            queueSize > maxQueueSize * 0.8 -> {
                currentBatchSize = minOf(currentBatchSize + 4, MAX_BATCH_SIZE)
            }
            // 队列较空且发送时间短，可以减小批量大小节省内存
            queueSize < maxQueueSize * 0.2 && batchTime < 1 -> {
                currentBatchSize = maxOf(currentBatchSize - 2, MIN_BATCH_SIZE)
            }
            // 发送时间过长，减小批量大小
            batchTime > 10 -> {
                currentBatchSize = maxOf(currentBatchSize - 1, MIN_BATCH_SIZE)
            }
        }
    }

    /**
     * 🔥 新增：性能统计更新
     */
    private fun updatePerformanceStats() {
        val currentTime = System.currentTimeMillis()

        // 🔥 每30秒输出一次性能统计，减少日志开销
        if (currentTime - lastStatsTime > 30000) {
            val timeDiff = (currentTime - lastStatsTime) / 1000.0
            val packetsPerSecond = if (timeDiff > 0) totalPacketsSent / timeDiff else 0.0
            val mbpsRate = if (timeDiff > 0) (totalBytesSent * 8) / (timeDiff * 1_000_000) else 0.0

            AppLog.network("UDP发送性能: ${String.format("%.1f", packetsPerSecond)}包/秒, " +
                    "${String.format("%.2f", mbpsRate)}Mbps, 当前批量大小: $currentBatchSize")

            // 重置统计
            totalPacketsSent = 0
            totalBytesSent = 0
            lastStatsTime = currentTime
        }
    }
}
