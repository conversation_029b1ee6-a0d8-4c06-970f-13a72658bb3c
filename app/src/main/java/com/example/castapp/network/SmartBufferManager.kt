package com.example.castapp.network

import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong
import com.example.castapp.utils.AppLog

/**
 * 智能缓冲区管理器 - 零拷贝优化v3
 * 
 * 解决UDP缓冲区池与DataView生命周期冲突的终极方案
 * 通过引用计数和延迟回收实现真正的零拷贝
 */
class SmartBufferManager private constructor() {
    companion object {        
        // 缓冲区配置
        private const val BUFFER_SIZE = 65536 // 64KB
        private const val INITIAL_POOL_SIZE = 16
        private const val MAX_POOL_SIZE = 32
        
        // 单例实例
        @Volatile
        private var INSTANCE: SmartBufferManager? = null
        
        fun getInstance(): SmartBufferManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SmartBufferManager().also { INSTANCE = it }
            }
        }
    }
    
    // 智能缓冲区类
    class SmartBuffer(
        val data: ByteArray,
        val id: Long,
        private val manager: SmartBufferManager
    ) {
        internal val refCount = AtomicInteger(1)
        private var isReleased = false
        
        /**
         * 🚀 CPU优化：增加引用计数 - 减少异常检查
         */
        fun retain(): SmartBuffer {
            // 🚀 CPU优化：移除热路径中的异常检查，提高性能
            refCount.incrementAndGet()
            return this
        }
        
        /**
         * 🚀 CPU优化：减少引用计数 - 简化热路径逻辑
         */
        fun release() {
            if (isReleased) return

            val count = refCount.decrementAndGet()
            if (count == 0) {
                isReleased = true
                manager.recycleBuffer(this)
            }
            // 🚀 CPU优化：移除异常计数检查，减少热路径开销
        }
        
        /**
         * 获取当前引用计数
         */
        fun getRefCount(): Int = refCount.get()
        
        /**
         * 检查是否已释放
         */
        fun isReleased(): Boolean = isReleased
    }
    
    // 缓冲区池和统计
    private val availableBuffers = ConcurrentLinkedQueue<SmartBuffer>()
    private val bufferIdGenerator = AtomicLong(0)
    private val totalBuffersCreated = AtomicLong(0)
    private val totalBuffersRecycled = AtomicLong(0)
    private val currentPoolSize = AtomicInteger(0)
    
    init {
        // 预分配初始缓冲区
        repeat(INITIAL_POOL_SIZE) {
            val buffer = createNewBuffer()
            availableBuffers.offer(buffer)
            currentPoolSize.incrementAndGet()
        }
        AppLog.network("智能缓冲区管理器初始化完成，初始池大小: $INITIAL_POOL_SIZE")
    }
    
    /**
     * 获取一个智能缓冲区
     */
    fun acquireBuffer(): SmartBuffer {
        // 尝试从池中获取
        val buffer = availableBuffers.poll()
        return if (buffer != null) {
            currentPoolSize.decrementAndGet()
            buffer.retain() // 重新激活
        } else {
            // 池为空，创建新缓冲区
            createNewBuffer()
        }
    }

    /**
     * 🚀 获取指定大小的智能缓冲区（用于大数据处理）
     */
    fun acquireBuffer(requiredSize: Int): SmartBuffer {
        return if (requiredSize <= BUFFER_SIZE) {
            // 标准大小，使用池化缓冲区
            acquireBuffer()
        } else {
            // 大数据，创建临时缓冲区（不池化）
            val id = bufferIdGenerator.incrementAndGet()
            val data = ByteArray(requiredSize)
            totalBuffersCreated.incrementAndGet()
            SmartBuffer(data, id, this)
        }
    }
    
    /**
     * 创建新的智能缓冲区
     */
    private fun createNewBuffer(): SmartBuffer {
        val id = bufferIdGenerator.incrementAndGet()
        val data = ByteArray(BUFFER_SIZE)
        totalBuffersCreated.incrementAndGet()
        
        return SmartBuffer(data, id, this)
    }
    
    /**
     * 回收缓冲区到池中
     */
    internal fun recycleBuffer(buffer: SmartBuffer) {
        if (currentPoolSize.get() < MAX_POOL_SIZE) {
            // 重置引用计数并放回池中
            buffer.refCount.set(0) // 设为0，等待下次acquire时重新激活
            availableBuffers.offer(buffer)
            currentPoolSize.incrementAndGet()
            totalBuffersRecycled.incrementAndGet()
        } else {
            // 池已满，直接丢弃
            totalBuffersRecycled.incrementAndGet()
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        availableBuffers.clear()
        currentPoolSize.set(0)
        AppLog.network("智能缓冲区管理器已清理")
    }
}

/**
 * 智能DataView - 支持引用计数的零拷贝数据视图
 */
class SmartDataView(
    internal val smartBuffer: SmartBufferManager.SmartBuffer,
    override val offset: Int,
    override val size: Int
) : DataView {
    
    init {
        // 增加缓冲区引用计数
        smartBuffer.retain()
    }
    
    override fun toByteArray(): ByteArray {
        if (smartBuffer.isReleased()) {
            throw IllegalStateException("底层缓冲区已被释放")
        }

        // 🚀 零拷贝优化：完整数组直接返回引用，部分数组高效切片
        return if (offset == 0 && size == smartBuffer.data.size) {
            smartBuffer.data // 直接返回引用，零拷贝
        } else {
            smartBuffer.data.copyOfRange(offset, offset + size) // 高效切片
        }
    }
    
    override fun copyTo(dest: ByteArray, destOffset: Int, length: Int) {
        if (smartBuffer.isReleased()) {
            throw IllegalStateException("底层缓冲区已被释放")
        }

        // 🚀 零拷贝优化：使用copyInto替代System.arraycopy
        val copyLength = minOf(length, size, dest.size - destOffset)
        smartBuffer.data.copyInto(dest, destOffset, offset, offset + copyLength)
    }
    
    override fun getByte(index: Int): Byte {
        require(index in 0 until size) { "Index $index out of bounds [0, $size)" }

        if (smartBuffer.isReleased()) {
            throw IllegalStateException("底层缓冲区已被释放")
        }

        return smartBuffer.data[offset + index]
    }

    // 🚀 零拷贝优化：支持源偏移量的拷贝方法
    override fun copyTo(dest: ByteArray, destOffset: Int, srcOffset: Int, length: Int) {
        if (smartBuffer.isReleased()) {
            throw IllegalStateException("底层缓冲区已被释放")
        }

        require(srcOffset >= 0) { "srcOffset不能为负数: $srcOffset" }
        require(srcOffset + length <= size) { "源偏移量超出范围: srcOffset=$srcOffset, length=$length, size=$size" }

        val copyLength = minOf(length, dest.size - destOffset)
        smartBuffer.data.copyInto(dest, destOffset, offset + srcOffset, offset + srcOffset + copyLength)
    }

    override fun copyTo(dest: java.nio.ByteBuffer, srcOffset: Int, length: Int) {
        if (smartBuffer.isReleased()) {
            throw IllegalStateException("底层缓冲区已被释放")
        }

        require(srcOffset >= 0) { "srcOffset不能为负数: $srcOffset" }
        require(srcOffset + length <= size) { "源偏移量超出范围: srcOffset=$srcOffset, length=$length, size=$size" }

        // 🚀 零拷贝优化：直接从底层数组写入ByteBuffer
        dest.put(smartBuffer.data, offset + srcOffset, length)
    }
    
    /**
     * 释放对底层缓冲区的引用
     */
    fun release() {
        smartBuffer.release()
    }

    /**
     * 检查底层缓冲区是否仍然有效
     */
    fun isValid(): Boolean = !smartBuffer.isReleased()

    /**
     * 🚀 零拷贝优化：获取直接ByteBuffer视图（如果可能）
     * 用于高效的批量数据传输
     */
    fun getDirectByteBuffer(): java.nio.ByteBuffer? {
        return if (isValid()) {
            // 创建底层数组的ByteBuffer视图
            val buffer = java.nio.ByteBuffer.wrap(smartBuffer.data, offset, size)
            buffer.asReadOnlyBuffer() // 返回只读视图确保安全
        } else {
            null
        }
    }

    /**
     * 🚀 零拷贝优化：获取直接数组访问（用于回退方案）
     * 返回底层数组的引用，调用者需要使用offset和size
     */
    fun getDirectByteArray(): ByteArray? {
        return if (isValid()) {
            smartBuffer.data
        } else {
            null
        }
    }
}
