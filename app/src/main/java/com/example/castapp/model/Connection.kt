package com.example.castapp.model

import java.util.UUID

/**
 * 统一连接管理模型 - 集成版
 * 合并了Connection、ConnectionState的所有功能
 * 统一ID架构：使用connectionId作为唯一标识符
 */
data class Connection(
    val connectionId: String,
    val ipAddress: String,
    val port: Int,

    // 集成状态管理（原ConnectionState功能）
    val isConnected: Boolean = false,
    val isCasting: Boolean = false,
    val isMediaAudioEnabled: Boolean = false,
    val isMicAudioEnabled: Boolean = false,
    val webSocketConnected: Boolean = false,
    val lastUpdateTime: Long = System.currentTimeMillis(),

    // 扩展状态信息
    val rtpSenderActive: Boolean = false,
    val videoEncoderActive: Boolean = false,
    val mediaProjectionActive: Boolean = false,
    val errorMessage: String? = null
) {

    /**
     * 获取显示文本
     */
    fun getDisplayText(): String = "$ipAddress:$port"

    /**
     * 获取状态摘要文本
     */
    fun getStatusSummary(): String {
        return if (isCasting) "投屏中" else "未投屏"
    }

    /**
     * 统一SSRC生成方法
     * 直接从connectionId生成SSRC，无需映射表
     */
    fun getSSRC(): Long = connectionId.hashCode().toLong() and 0xFFFFFFFFL

    /**
     * 获取WebSocket端口（UDP端口+1）
     */
    fun getWebSocketPort(): Int = port + 1

    /**
     * 获取媒体音频端口（UDP端口+2）
     */
    fun getMediaAudioPort(): Int = port + 2

    /**
     * 获取麦克风音频端口（UDP端口+3）
     */
    fun getMicAudioPort(): Int = port + 3

    // === 状态更新方法（替代ConnectionState的with*方法） ===



    /**
     * 更新投屏状态
     */
    fun withCasting(casting: Boolean): Connection = copy(
        isCasting = casting,
        lastUpdateTime = System.currentTimeMillis()
    )

    /**
     * 更新媒体音频状态
     */
    fun withMediaAudio(enabled: Boolean): Connection = copy(
        isMediaAudioEnabled = enabled,
        lastUpdateTime = System.currentTimeMillis()
    )

    /**
     * 更新麦克风音频状态
     */
    fun withMicAudio(enabled: Boolean): Connection = copy(
        isMicAudioEnabled = enabled,
        lastUpdateTime = System.currentTimeMillis()
    )

    /**
     * 更新WebSocket连接状态
     */
    fun withWebSocketConnection(connected: Boolean): Connection = copy(
        webSocketConnected = connected,
        lastUpdateTime = System.currentTimeMillis()
    )

    /**
     * 更新错误信息
     */
    fun withError(error: String?): Connection = copy(
        errorMessage = error,
        lastUpdateTime = System.currentTimeMillis()
    )

    /**
     * 更新基础连接状态
     */
    fun withConnection(connected: Boolean): Connection = copy(
        isConnected = connected,
        lastUpdateTime = System.currentTimeMillis()
    )

    companion object {
        /**
         * 生成连接ID
         */
        fun generateConnectionId(): String = UUID.randomUUID().toString()

        /**
         * 创建新连接实例
         */
        fun create(ipAddress: String, port: Int): Connection {
            return Connection(
                connectionId = generateConnectionId(),
                ipAddress = ipAddress,
                port = port
            )
        }
    }
}
