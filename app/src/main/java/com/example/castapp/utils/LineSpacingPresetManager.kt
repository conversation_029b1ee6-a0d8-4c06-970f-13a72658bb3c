package com.example.castapp.utils

import android.content.Context
import android.content.SharedPreferences
import com.example.castapp.utils.AppLog

/**
 * 行间距预设管理器
 * 管理预设和自定义行间距列表，提供持久化存储功能
 * 完全复用字间距架构的实现模式
 */
object LineSpacingPresetManager {
    
    private const val PREFS_NAME = "line_spacing_presets"
    private const val KEY_CUSTOM_LINE_SPACINGS = "custom_line_spacings"
    
    // 预设行间距列表（单位：dp）
    val PRESET_LINE_SPACINGS = listOf(0.0f, 1.0f, 1.5f, 2.0f)
    
    private lateinit var sharedPreferences: SharedPreferences
    private val listeners = mutableSetOf<LineSpacingPresetListener>()
    
    /**
     * 行间距预设监听器接口
     */
    interface LineSpacingPresetListener {
        fun onLineSpacingAdded(lineSpacing: Float) {}
        fun onLineSpacingDeleted(lineSpacing: Float) {}
        fun onLineSpacingListReset() {}
    }
    
    /**
     * 初始化管理器
     */
    fun initialize(context: Context) {
        sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        AppLog.d("【行间距预设管理器】初始化完成")
    }
    
    /**
     * 获取自定义行间距列表
     */
    private fun getCustomLineSpacings(): List<Float> {
        val customLineSpacingsString = sharedPreferences.getString(KEY_CUSTOM_LINE_SPACINGS, "") ?: ""
        
        return if (customLineSpacingsString.isBlank()) {
            emptyList()
        } else {
            try {
                customLineSpacingsString.split(",")
                    .map { it.trim().toFloat() }
                    .filter { it >= 0.0f && it <= 10.0f } // 行间距范围限制
                    .sorted()
            } catch (e: Exception) {
                AppLog.e("【行间距预设管理器】解析自定义行间距失败", e)
                emptyList()
            }
        }
    }
    
    /**
     * 保存自定义行间距列表
     */
    private fun saveCustomLineSpacings(lineSpacings: List<Float>) {
        val lineSpacingsString = lineSpacings.joinToString(",")
        sharedPreferences.edit()
            .putString(KEY_CUSTOM_LINE_SPACINGS, lineSpacingsString)
            .apply()
        
        AppLog.d("【行间距预设管理器】自定义行间距已保存: $lineSpacingsString")
    }
    
    /**
     * 获取完整的行间距列表（预设 + 自定义）
     */
    fun getAllLineSpacings(): List<Float> {
        val customLineSpacings = getCustomLineSpacings()
        val allLineSpacings = (PRESET_LINE_SPACINGS + customLineSpacings).distinct().sorted()
        
        AppLog.d("【行间距预设管理器】获取完整行间距列表: $allLineSpacings")
        return allLineSpacings
    }
    
    /**
     * 获取行间距选项字符串列表（用于Spinner显示）
     */
    fun getAllLineSpacingOptions(): List<String> {
        return getAllLineSpacings().map { "${it}dp" }
    }
    
    /**
     * 获取行间距值列表（用于Spinner逻辑处理）
     */
    fun getAllLineSpacingValues(): List<Float> {
        return getAllLineSpacings()
    }
    
    /**
     * 添加自定义行间距
     */
    fun addCustomLineSpacing(lineSpacing: Float): Boolean {
        try {
            // 检查是否已存在
            if (getAllLineSpacings().contains(lineSpacing)) {
                AppLog.d("【行间距预设管理器】行间距${lineSpacing}dp已存在")
                return false
            }
            
            // 检查行间距范围
            if (lineSpacing < 0.0f || lineSpacing > 10.0f) {
                AppLog.w("【行间距预设管理器】行间距${lineSpacing}dp超出有效范围(0.0 ~ 10.0)")
                return false
            }
            
            // 添加到自定义列表
            val customLineSpacings = getCustomLineSpacings().toMutableList()
            customLineSpacings.add(lineSpacing)
            customLineSpacings.sort()
            
            // 保存到存储
            saveCustomLineSpacings(customLineSpacings)
            
            // 通知监听器
            notifyLineSpacingAdded(lineSpacing)
            
            AppLog.d("【行间距预设管理器】已添加自定义行间距: ${lineSpacing}dp")
            return true
            
        } catch (e: Exception) {
            AppLog.e("【行间距预设管理器】添加自定义行间距失败", e)
            return false
        }
    }
    
    /**
     * 删除自定义行间距
     */
    fun deleteCustomLineSpacing(lineSpacing: Float): Boolean {
        try {
            // 检查是否为预设行间距
            if (PRESET_LINE_SPACINGS.contains(lineSpacing)) {
                AppLog.w("【行间距预设管理器】无法删除预设行间距: ${lineSpacing}dp")
                return false
            }
            
            // 从自定义列表中删除
            val customLineSpacings = getCustomLineSpacings().toMutableList()
            val removed = customLineSpacings.remove(lineSpacing)
            
            if (removed) {
                // 保存到存储
                saveCustomLineSpacings(customLineSpacings)
                
                // 通知监听器
                notifyLineSpacingDeleted(lineSpacing)
                
                AppLog.d("【行间距预设管理器】已删除自定义行间距: ${lineSpacing}dp")
                return true
            } else {
                AppLog.w("【行间距预设管理器】行间距${lineSpacing}dp不存在于自定义列表中")
                return false
            }
            
        } catch (e: Exception) {
            AppLog.e("【行间距预设管理器】删除自定义行间距失败", e)
            return false
        }
    }
    
    /**
     * 重置为默认行间距设置（清除所有自定义行间距）
     */
    fun resetToDefault() {
        try {
            // 清除自定义行间距
            saveCustomLineSpacings(emptyList())
            
            // 通知监听器
            notifyLineSpacingListReset()
            
            AppLog.d("【行间距预设管理器】已重置为默认行间距设置")
            
        } catch (e: Exception) {
            AppLog.e("【行间距预设管理器】重置默认设置失败", e)
        }
    }
    
    /**
     * 检查行间距是否为预设行间距
     */
    fun isPresetLineSpacing(lineSpacing: Float): Boolean {
        return PRESET_LINE_SPACINGS.contains(lineSpacing)
    }
    
    /**
     * 添加行间距列表变化监听器
     */
    fun addListener(listener: LineSpacingPresetListener) {
        listeners.add(listener)
        AppLog.d("【行间距预设管理器】已添加监听器，当前监听器数量: ${listeners.size}")
    }
    
    /**
     * 移除行间距列表变化监听器
     */
    fun removeListener(listener: LineSpacingPresetListener) {
        listeners.remove(listener)
        AppLog.d("【行间距预设管理器】已移除监听器，当前监听器数量: ${listeners.size}")
    }
    
    // 通知方法
    private fun notifyLineSpacingAdded(lineSpacing: Float) {
        listeners.forEach { it.onLineSpacingAdded(lineSpacing) }
    }
    
    private fun notifyLineSpacingDeleted(lineSpacing: Float) {
        listeners.forEach { it.onLineSpacingDeleted(lineSpacing) }
    }
    
    private fun notifyLineSpacingListReset() {
        listeners.forEach { it.onLineSpacingListReset() }
    }
}
