package com.example.castapp.utils

import kotlinx.coroutines.*
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 定期清理任务管理器
 * 用于定期清理各种缓存、失效引用和临时数据，防止内存泄漏
 */
class PeriodicCleanupTask private constructor() {

    companion object {
        // 🚀 CPU优化：增加清理间隔，减少清理任务频率
        private const val CLEANUP_INTERVAL_MS = 300000L // 5分钟清理间隔
        private const val DEEP_CLEANUP_INTERVAL_MS = 900000L // 15分钟深度清理间隔

        @Volatile
        private var INSTANCE: PeriodicCleanupTask? = null

        fun getInstance(): PeriodicCleanupTask {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: PeriodicCleanupTask().also { INSTANCE = it }
            }
        }

        /**
         * 清理单例实例 - 🚀 根源优化：只清理引用，不调用cleanup()避免重复
         */
        fun clearInstance() {
            synchronized(this) {
                if (INSTANCE != null) {
                    AppLog.cleanup("清理PeriodicCleanupTask单例引用")
                    INSTANCE = null
                } else {
                    AppLog.cleanup("PeriodicCleanupTask单例引用已清理")
                }
            }
        }
    }
    private val isRunning = AtomicBoolean(false)
    private val cleanupScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var cleanupJob: Job? = null
    private var deepCleanupJob: Job? = null
    
    // 清理任务注册表
    private val cleanupTasks = ConcurrentHashMap<String, WeakReference<CleanupTask>>()
    private val deepCleanupTasks = ConcurrentHashMap<String, WeakReference<DeepCleanupTask>>()
    
    // 清理统计
    private var lastCleanupTime = 0L
    private var lastDeepCleanupTime = 0L
    private var cleanupCount = 0L
    private var deepCleanupCount = 0L
    
    interface CleanupTask {
        fun cleanup()
        fun getTaskName(): String
    }
    
    interface DeepCleanupTask {
        fun deepCleanup()
        fun getTaskName(): String
    }
    
    /**
     * 开始定期清理
     */
    fun startPeriodicCleanup() {
        if (isRunning.compareAndSet(false, true)) {
            AppLog.cleanup("开始定期清理任务")

            // 启动常规清理任务
            cleanupJob = cleanupScope.launch {
                while (isActive && isRunning.get()) {
                    try {
                        performCleanup()
                        delay(CLEANUP_INTERVAL_MS)
                    } catch (e: CancellationException) {
                        // 协程被正常取消，这是预期行为
                        AppLog.cleanup("定期清理任务已取消")
                        throw e // 重新抛出以正确处理取消
                    } catch (e: Exception) {
                        AppLog.e("定期清理任务异常", e)
                        delay(CLEANUP_INTERVAL_MS)
                    }
                }
            }

            // 启动深度清理任务
            deepCleanupJob = cleanupScope.launch {
                while (isActive && isRunning.get()) {
                    try {
                        delay(DEEP_CLEANUP_INTERVAL_MS)
                        performDeepCleanup()
                    } catch (e: CancellationException) {
                        // 协程被正常取消，这是预期行为
                        AppLog.cleanup("深度清理任务已取消")
                        throw e // 重新抛出以正确处理取消
                    } catch (e: Exception) {
                        AppLog.e("深度清理任务异常", e)
                        delay(DEEP_CLEANUP_INTERVAL_MS)
                    }
                }
            }
        }
    }
    
    /**
     * 停止定期清理
     */
    fun stopPeriodicCleanup() {
        if (isRunning.compareAndSet(true, false)) {
            AppLog.cleanup("停止定期清理任务")
            cleanupJob?.cancel()
            deepCleanupJob?.cancel()
            cleanupJob = null
            deepCleanupJob = null
        }
    }

    /**
     * 注册清理任务
     */
    fun registerCleanupTask(taskId: String, task: CleanupTask) {
        cleanupTasks[taskId] = WeakReference(task)
        AppLog.cleanup("注册清理任务: $taskId - ${task.getTaskName()}")
    }

    /**
     * 注册深度清理任务
     */
    fun registerDeepCleanupTask(taskId: String, task: DeepCleanupTask) {
        deepCleanupTasks[taskId] = WeakReference(task)
        AppLog.cleanup("注册深度清理任务: $taskId - ${task.getTaskName()}")
    }
    

    
    /**
     * 执行常规清理
     */
    private fun performCleanup() {
        val startTime = System.currentTimeMillis()
        var tasksExecuted = 0
        
        // 清理失效的任务引用
        cleanupInvalidReferences()
        
        // 执行所有注册的清理任务
        cleanupTasks.values.forEach { ref ->
            ref.get()?.let { task ->
                try {
                    task.cleanup()
                    tasksExecuted++
                } catch (e: Exception) {
                    AppLog.e("执行清理任务失败: ${task.getTaskName()}", e)
                }
            }
        }

        lastCleanupTime = System.currentTimeMillis()
        cleanupCount++

        val duration = lastCleanupTime - startTime
        AppLog.cleanup("常规清理完成: 执行${tasksExecuted}个任务, 耗时${duration}ms")
    }
    
    /**
     * 执行深度清理
     */
    private fun performDeepCleanup() {
        val startTime = System.currentTimeMillis()
        var tasksExecuted = 0

        AppLog.cleanup("开始深度清理...")

        // 清理失效的任务引用
        cleanupInvalidReferences()

        // 执行所有注册的深度清理任务
        deepCleanupTasks.values.forEach { ref ->
            ref.get()?.let { task ->
                try {
                    task.deepCleanup()
                    tasksExecuted++
                } catch (e: Exception) {
                    AppLog.e("执行深度清理任务失败: ${task.getTaskName()}", e)
                }
            }
        }

        // 强制垃圾回收
        System.gc()
        System.runFinalization()
        System.gc()

        lastDeepCleanupTime = System.currentTimeMillis()
        deepCleanupCount++

        val duration = lastDeepCleanupTime - startTime
        AppLog.cleanup("深度清理完成: 执行${tasksExecuted}个任务, 耗时${duration}ms")
    }
    
    /**
     * 清理失效的引用
     */
    private fun cleanupInvalidReferences() {
        val invalidCleanupTasks = cleanupTasks.filter { it.value.get() == null }.keys
        invalidCleanupTasks.forEach { cleanupTasks.remove(it) }
        
        val invalidDeepCleanupTasks = deepCleanupTasks.filter { it.value.get() == null }.keys
        invalidDeepCleanupTasks.forEach { deepCleanupTasks.remove(it) }
        
        if (invalidCleanupTasks.isNotEmpty() || invalidDeepCleanupTasks.isNotEmpty()) {
            AppLog.cleanup("清理失效引用: 常规任务${invalidCleanupTasks.size}个, 深度任务${invalidDeepCleanupTasks.size}个")
        }
    }



    /**
     * 清理资源
     */
    fun cleanup() {
        AppLog.cleanup("清理PeriodicCleanupTask资源...")
        stopPeriodicCleanup()

        try {
            cleanupScope.cancel()
            cleanupTasks.clear()
            deepCleanupTasks.clear()
            AppLog.cleanup("PeriodicCleanupTask资源清理完成")
        } catch (e: Exception) {
            AppLog.e("清理PeriodicCleanupTask资源时发生异常", e)
        }
    }
}
