package com.example.castapp.utils

import android.content.Context
import android.graphics.Paint
import android.text.style.LineHeightSpan
import android.util.TypedValue

/**
 * 🎨 自定义行间距样式
 * 用于为文字添加行间距效果，通过调整行高实现
 */
class LineSpacingSpan(
    private val lineSpacingExtra: Float, // 行间距值，单位为dp
    private val context: Context? = null
) : LineHeightSpan {

    override fun chooseHeight(
        text: CharSequence?,
        start: Int,
        end: Int,
        spanstartv: Int,
        v: Int,
        fm: Paint.FontMetricsInt?
    ) {
        if (fm == null || lineSpacingExtra == 0.0f) return

        // 计算原始行高
        val originalHeight = fm.descent - fm.ascent

        // 正确的dp到px转换
        val extraSpacingPixels = if (context != null) {
            TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP,
                lineSpacingExtra,
                context.resources.displayMetrics
            ).toInt()
        } else {
            // 备用方案：使用近似转换（假设密度为3）
            (lineSpacingExtra * 3).toInt()
        }

        // 将额外间距平均分配到上下
        val topExtra = extraSpacingPixels / 2
        val bottomExtra = extraSpacingPixels - topExtra

        // 调整字体度量（增加行间距）
        fm.ascent -= topExtra
        fm.top -= topExtra
        fm.descent += bottomExtra
        fm.bottom += bottomExtra

        AppLog.v("【行间距Span】应用行间距: ${lineSpacingExtra}dp, 原始高度: $originalHeight, 额外间距: $extraSpacingPixels")
    }

    /**
     * 获取行间距值
     */
    fun getLineSpacing(): Float = lineSpacingExtra
}
