package com.example.castapp.utils

import android.graphics.Canvas
import android.graphics.Paint
import android.text.style.ReplacementSpan

/**
 * 🎨 自定义字间距样式
 * 用于为文字添加字间距效果，通过调整字符间的间距实现
 */
class LetterSpacingSpan(
    private val letterSpacing: Float // 字间距值，单位为em（相对于字体大小）
) : ReplacementSpan() {

    override fun getSize(paint: Paint, text: CharSequence?, start: Int, end: Int, fm: Paint.FontMetricsInt?): Int {
        if (text == null || start >= end) return 0
        
        // 计算文字的基础宽度
        val baseWidth = paint.measureText(text, start, end)
        
        // 计算字间距的额外宽度
        val charCount = end - start
        val extraSpacing = if (charCount > 1) {
            // 字间距 = 字体大小 × letterSpacing × (字符数 - 1)
            paint.textSize * letterSpacing * (charCount - 1)
        } else {
            0f
        }
        
        return (baseWidth + extraSpacing).toInt()
    }

    override fun draw(
        canvas: Canvas, text: CharSequence?, start: Int, end: Int,
        x: Float, top: Int, y: Int, bottom: Int, paint: Paint
    ) {
        if (text == null || start >= end) return

        val textToDraw = text.subSequence(start, end)
        val charCount = textToDraw.length
        
        if (charCount == 1) {
            // 单个字符，直接绘制
            canvas.drawText(textToDraw.toString(), x, y.toFloat(), paint)
        } else {
            // 多个字符，逐个绘制并添加间距
            var currentX = x
            val spacingPixels = paint.textSize * letterSpacing
            
            for (i in 0 until charCount) {
                val char = textToDraw[i].toString()
                canvas.drawText(char, currentX, y.toFloat(), paint)
                
                // 计算下一个字符的位置
                val charWidth = paint.measureText(char)
                currentX += charWidth
                
                // 添加字间距（最后一个字符后不添加）
                if (i < charCount - 1) {
                    currentX += spacingPixels
                }
            }
        }
    }

    /**
     * 获取字间距值
     */
    fun getLetterSpacing(): Float = letterSpacing
}
