package com.example.castapp.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.core.graphics.toColorInt

/**
 * 🎨 自定义色板管理器
 * 负责用户自定义颜色的保存、删除和管理
 */
class ColorPaletteManager(context: Context) {

    companion object {
        private const val PREFS_NAME = "color_palette_prefs"
        private const val KEY_CUSTOM_COLORS = "custom_colors"
        private const val MAX_CUSTOM_COLORS = 12 // 最多保存12个自定义颜色
        private const val COLOR_SEPARATOR = "," // 颜色值分隔符
    }

    private val sharedPrefs: SharedPreferences =
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    /**
     * 🎨 获取所有自定义颜色
     * @return 自定义颜色列表（ARGB格式）
     */
    fun getCustomColors(): List<Int> {
        return try {
            val colorsString = sharedPrefs.getString(KEY_CUSTOM_COLORS, "") ?: ""
            if (colorsString.isEmpty()) {
                emptyList()
            } else {
                colorsString.split(COLOR_SEPARATOR)
                    .mapNotNull { colorStr ->
                        try {
                            colorStr.toLong().toInt()
                        } catch (e: Exception) {
                            AppLog.w("🎨 解析颜色值失败: $colorStr", e)
                            null
                        }
                    }
            }
        } catch (e: Exception) {
            AppLog.e("🎨 获取自定义颜色失败", e)
            emptyList()
        }
    }

    /**
     * 🎨 保存颜色到自定义色板
     * @param color 要保存的颜色（ARGB格式）
     * @return 是否保存成功
     */
    fun saveColor(color: Int): Boolean {
        return try {
            val currentColors = getCustomColors().toMutableList()
            
            // 检查颜色是否已存在
            if (currentColors.contains(color)) {
                AppLog.d("🎨 颜色已存在于自定义色板中: ${String.format("#%08X", color)}")
                return false
            }
            
            // 如果达到最大数量，移除最旧的颜色
            if (currentColors.size >= MAX_CUSTOM_COLORS) {
                currentColors.removeAt(0)
                AppLog.d("🎨 自定义色板已满，移除最旧颜色")
            }
            
            // 添加新颜色到末尾
            currentColors.add(color)
            
            // 保存到SharedPreferences
            val colorsString = currentColors.joinToString(COLOR_SEPARATOR) { it.toLong().toString() }
            sharedPrefs.edit {
                putString(KEY_CUSTOM_COLORS, colorsString)
            }
            
            AppLog.d("🎨 颜色保存成功: ${String.format("#%08X", color)}, 总数: ${currentColors.size}")
            true
        } catch (e: Exception) {
            AppLog.e("🎨 保存颜色失败", e)
            false
        }
    }

    /**
     * 🎨 从自定义色板删除颜色
     * @param color 要删除的颜色（ARGB格式）
     * @return 是否删除成功
     */
    fun deleteColor(color: Int): Boolean {
        return try {
            val currentColors = getCustomColors().toMutableList()
            
            val removed = currentColors.remove(color)
            if (!removed) {
                AppLog.d("🎨 颜色不存在于自定义色板中: ${String.format("#%08X", color)}")
                return false
            }
            
            // 保存更新后的颜色列表
            val colorsString = currentColors.joinToString(COLOR_SEPARATOR) { it.toLong().toString() }
            sharedPrefs.edit {
                putString(KEY_CUSTOM_COLORS, colorsString)
            }
            
            AppLog.d("🎨 颜色删除成功: ${String.format("#%08X", color)}, 剩余: ${currentColors.size}")
            true
        } catch (e: Exception) {
            AppLog.e("🎨 删除颜色失败", e)
            false
        }
    }

    /**
     * 🎨 检查颜色是否已保存
     * @param color 要检查的颜色（ARGB格式）
     * @return 是否已保存
     */
    fun isColorSaved(color: Int): Boolean {
        return getCustomColors().contains(color)
    }

    /**
     * 🎨 获取自定义颜色数量
     */
    fun getColorCount(): Int {
        return getCustomColors().size
    }

    /**
     * 🎨 是否可以保存更多颜色
     */
    fun canSaveMoreColors(): Boolean {
        return getColorCount() < MAX_CUSTOM_COLORS
    }

    /**
     * 🎨 获取默认推荐颜色
     * 当用户没有自定义颜色时显示的推荐色板
     */
    fun getRecommendedColors(): List<Int> {
        return listOf(
            "#FF6B6B".toColorInt(), // 红色
            "#4ECDC4".toColorInt(), // 青色
            "#45B7D1".toColorInt(), // 蓝色
            "#96CEB4".toColorInt(), // 绿色
            "#FFEAA7".toColorInt(), // 黄色
            "#DDA0DD".toColorInt(), // 紫色
            "#98D8C8".toColorInt(), // 薄荷绿
            "#F7DC6F".toColorInt(), // 金色
            "#BB8FCE".toColorInt(), // 淡紫色
            "#85C1E9".toColorInt(), // 天蓝色
            "#F8C471".toColorInt(), // 橙色
            "#82E0AA".toColorInt()  // 浅绿色
        )
    }
}
