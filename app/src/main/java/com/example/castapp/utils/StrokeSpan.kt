package com.example.castapp.utils

import android.graphics.Canvas
import android.graphics.Paint
import android.text.Spannable
import android.text.style.ForegroundColorSpan
import android.text.style.ReplacementSpan

/**
 * 🎨 自定义文字描边样式
 * 用于为文字添加描边效果，实现双重绘制：先描边后填充
 */
class StrokeSpan(
    private val strokeWidth: Float,
    private val strokeColor: Int
) : ReplacementSpan() {

    override fun getSize(paint: Paint, text: CharSequence?, start: Int, end: Int, fm: Paint.FontMetricsInt?): Int {
        // 返回文字的宽度，加上描边宽度的额外空间
        val textWidth = paint.measureText(text, start, end)
        return (textWidth + strokeWidth * 2).toInt()
    }

    override fun draw(
        canvas: Canvas, text: CharSequence?, start: Int, end: Int,
        x: Float, top: Int, y: Int, bottom: Int, paint: Paint
    ) {
        if (text == null) return

        val textToDraw = text.subSequence(start, end).toString()

        // 保存原始Paint状态
        val originalColor = paint.color
        val originalStyle = paint.style
        val originalStrokeWidth = paint.strokeWidth

        // 检查是否有ForegroundColorSpan设置的颜色
        val fillColor = getForegroundColor(text, start, end) ?: originalColor

        // 第一次绘制：描边
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = strokeWidth
        paint.color = strokeColor
        canvas.drawText(textToDraw, x + strokeWidth, y.toFloat(), paint)

        // 第二次绘制：填充（使用检测到的文字颜色）
        paint.style = Paint.Style.FILL
        paint.color = fillColor
        canvas.drawText(textToDraw, x + strokeWidth, y.toFloat(), paint)

        // 恢复原始Paint状态
        paint.color = originalColor
        paint.style = originalStyle
        paint.strokeWidth = originalStrokeWidth

        AppLog.d("【StrokeSpan】绘制描边文字: '$textToDraw', 描边宽度=${strokeWidth}px, 描边颜色=${String.format("#%08X", strokeColor)}, 文字颜色=${String.format("#%08X", fillColor)}")
    }

    /**
     * 获取文字范围内的ForegroundColorSpan颜色
     */
    private fun getForegroundColor(text: CharSequence, start: Int, end: Int): Int? {
        if (text !is Spannable) return null

        try {
            val colorSpans = text.getSpans(start, end, ForegroundColorSpan::class.java)

            // 查找覆盖当前范围的颜色样式
            for (span in colorSpans) {
                val spanStart = text.getSpanStart(span)
                val spanEnd = text.getSpanEnd(span)

                // 检查颜色样式是否覆盖了当前范围
                if (spanStart <= start && spanEnd >= end) {
                    AppLog.d("【StrokeSpan】检测到ForegroundColorSpan颜色: ${String.format("#%08X", span.foregroundColor)}")
                    return span.foregroundColor
                }
            }
        } catch (e: Exception) {
            AppLog.w("【StrokeSpan】获取ForegroundColor失败", e)
        }

        return null
    }

    /**
     * 获取描边宽度
     */
    fun getStrokeWidth(): Float = strokeWidth

    /**
     * 获取描边颜色
     */
    fun getStrokeColor(): Int = strokeColor

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is StrokeSpan) return false
        return strokeWidth == other.strokeWidth && strokeColor == other.strokeColor
    }

    override fun hashCode(): Int {
        var result = strokeWidth.hashCode()
        result = 31 * result + strokeColor
        return result
    }

    override fun toString(): String {
        return "StrokeSpan(strokeWidth=$strokeWidth, strokeColor=${String.format("#%08X", strokeColor)})"
    }
}
