package com.example.castapp.utils

import android.content.Context
import android.content.SharedPreferences
import android.graphics.Typeface
import androidx.core.content.edit
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.io.File
import java.lang.ref.WeakReference

/**
 * 字体预设管理器
 * 负责管理系统预设字体和用户自定义字体
 * 提供字体的添加、删除、获取等功能
 */
object FontPresetManager {
    
    private const val PREFS_NAME = "font_preset_prefs"
    private const val KEY_CUSTOM_FONTS = "custom_fonts"
    private const val FONTS_DIR = "custom_fonts"
    
    // 系统预设字体
    private val PRESET_FONTS = listOf(
        FontItem("Roboto", "default", true, null, Typeface.DEFAULT)
    )

    private var contextRef: WeakReference<Context>? = null
    private var sharedPreferences: SharedPreferences? = null
    private val gson = Gson()
    
    // 字体列表变化监听器
    private val listeners = mutableSetOf<FontPresetListener>()
    
    /**
     * 字体数据项
     */
    data class FontItem(
        val name: String,           // 字体显示名称
        val fontFamily: String,     // 字体族名称
        val isPreset: Boolean,      // 是否为预设字体
        val filePath: String? = null, // 自定义字体文件路径
        @Transient private val presetTypeface: Typeface? = null    // 系统预设字体对象 - 不序列化
    ) {
        /**
         * 获取字体对象（延迟加载）
         */
        fun loadTypeface(): Typeface? {
            return if (isPreset) {
                presetTypeface // 系统预设字体
            } else {
                // 自定义字体，从文件路径加载
                filePath?.let { path ->
                    try {
                        Typeface.createFromFile(path)
                    } catch (e: Exception) {
                        AppLog.e("【字体预设管理器】加载自定义字体失败: $path", e)
                        null
                    }
                }
            }
        }
    }

    /**
     * 用于序列化的简化字体数据
     */
    private data class SerializableFontItem(
        val name: String,
        val fontFamily: String,
        val filePath: String
    )

    /**
     * 字体预设列表变化监听器接口
     */
    interface FontPresetListener {
        /**
         * 字体被添加时调用
         */
        fun onFontAdded(fontItem: FontItem)

        /**
         * 字体被删除时调用
         */
        fun onFontDeleted(fontItem: FontItem)

        /**
         * 字体名称被更新时调用
         */
        fun onFontNameUpdated(oldFontItem: FontItem, newFontItem: FontItem)

        /**
         * 字体列表被重置时调用
         */
        fun onFontListReset()
    }
    
    /**
     * 安全获取Context
     */
    private fun getContext(): Context? = contextRef?.get()

    /**
     * 安全获取SharedPreferences
     */
    private fun getSharedPreferences(): SharedPreferences? = sharedPreferences

    /**
     * 初始化管理器
     */
    fun initialize(context: Context) {
        this.contextRef = WeakReference(context.applicationContext)
        this.sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

        // 确保字体目录存在
        ensureFontsDirectoryExists()

        AppLog.d("【字体预设管理器】初始化完成")
    }
    
    /**
     * 获取完整的字体列表（预设 + 自定义）
     */
    fun getAllFonts(): List<FontItem> {
        val customFonts = getCustomFonts()
        val allFonts = PRESET_FONTS + customFonts
        
        AppLog.d("【字体预设管理器】获取完整字体列表: ${allFonts.map { it.name }}")
        return allFonts
    }

    
    /**
     * 根据字体名称获取字体项
     */
    fun getFontByName(name: String): FontItem? {
        return getAllFonts().find { it.name == name }
    }
    
    /**
     * 获取自定义字体列表
     */
    private fun getCustomFonts(): List<FontItem> {
        try {
            val prefs = getSharedPreferences() ?: return emptyList()
            val json = prefs.getString(KEY_CUSTOM_FONTS, "[]")
            val type = object : TypeToken<List<SerializableFontItem>>() {}.type
            val serializableFonts = gson.fromJson<List<SerializableFontItem>>(json, type) ?: emptyList()

            // 转换为FontItem并验证字体文件是否存在
            val validFonts = serializableFonts.mapNotNull { serializableFont ->
                val file = File(serializableFont.filePath)
                if (file.exists()) {
                    FontItem(
                        name = serializableFont.name,
                        fontFamily = serializableFont.fontFamily,
                        isPreset = false,
                        filePath = serializableFont.filePath
                    )
                } else {
                    AppLog.w("【字体预设管理器】字体文件不存在，已跳过: ${serializableFont.filePath}")
                    null
                }
            }

            // 如果有无效字体被过滤掉，更新存储
            if (validFonts.size != serializableFonts.size) {
                saveCustomFonts(validFonts)
                AppLog.d("【字体预设管理器】已清理无效字体，剩余: ${validFonts.size}")
            }

            return validFonts

        } catch (e: Exception) {
            AppLog.e("【字体预设管理器】获取自定义字体失败", e)
            return emptyList()
        }
    }
    
    /**
     * 保存自定义字体列表
     */
    private fun saveCustomFonts(fonts: List<FontItem>) {
        try {
            val prefs = getSharedPreferences() ?: return

            // 转换为可序列化的格式
            val serializableFonts = fonts.filter { !it.isPreset && it.filePath != null }
                .map { font ->
                    SerializableFontItem(
                        name = font.name,
                        fontFamily = font.fontFamily,
                        filePath = font.filePath!!
                    )
                }

            val json = gson.toJson(serializableFonts)
            prefs.edit {
                putString(KEY_CUSTOM_FONTS, json)
            }

            AppLog.d("【字体预设管理器】已保存自定义字体: ${fonts.map { it.name }}")

        } catch (e: Exception) {
            AppLog.e("【字体预设管理器】保存自定义字体失败", e)
        }
    }
    
    /**
     * 添加自定义字体
     */
    fun addCustomFont(name: String, fontFile: File): Boolean {
        try {
            val context = getContext() ?: return false

            AppLog.d("【字体预设管理器】开始添加字体: $name, 文件: ${fontFile.absolutePath}")

            // 检查输入文件是否存在
            if (!fontFile.exists()) {
                AppLog.e("【字体预设管理器】字体文件不存在: ${fontFile.absolutePath}")
                return false
            }

            AppLog.d("【字体预设管理器】字体文件存在，大小: ${fontFile.length()} bytes")

            // 检查字体名称是否已存在
            if (getAllFonts().any { it.name == name }) {
                AppLog.w("【字体预设管理器】字体名称已存在: $name")
                return false
            }

            // 创建字体目录
            val fontsDir = File(context.filesDir, FONTS_DIR)
            if (!fontsDir.exists()) {
                val created = fontsDir.mkdirs()
                AppLog.d("【字体预设管理器】字体目录创建结果: $created, 路径: ${fontsDir.absolutePath}")
            }

            // 复制字体文件到应用目录
            val targetFile = File(fontsDir, "${System.currentTimeMillis()}_${fontFile.name}")
            AppLog.d("【字体预设管理器】目标文件路径: ${targetFile.absolutePath}")

            fontFile.copyTo(targetFile, overwrite = true)
            AppLog.d("【字体预设管理器】文件复制完成，目标文件大小: ${targetFile.length()} bytes")

            // 验证字体文件
            try {
                AppLog.d("【字体预设管理器】开始验证字体文件")
                Typeface.createFromFile(targetFile)
                AppLog.d("【字体预设管理器】字体文件验证成功")
            } catch (e: Exception) {
                targetFile.delete()
                AppLog.e("【字体预设管理器】无效的字体文件: ${fontFile.name}", e)
                return false
            }
            
            // 创建字体项
            val fontItem = FontItem(
                name = name,
                fontFamily = name.lowercase().replace(" ", "_"),
                isPreset = false,
                filePath = targetFile.absolutePath
            )
            
            // 添加到自定义列表
            val customFonts = getCustomFonts().toMutableList()
            customFonts.add(fontItem)
            saveCustomFonts(customFonts)
            
            // 通知监听器
            notifyFontAdded(fontItem)
            
            AppLog.d("【字体预设管理器】已添加自定义字体: $name")
            return true
            
        } catch (e: Exception) {
            AppLog.e("【字体预设管理器】添加自定义字体失败", e)
            return false
        }
    }
    
    /**
     * 删除自定义字体
     */
    fun deleteCustomFont(fontItem: FontItem): Boolean {
        try {
            if (fontItem.isPreset) {
                AppLog.w("【字体预设管理器】不能删除预设字体: ${fontItem.name}")
                return false
            }

            // 删除字体文件
            fontItem.filePath?.let { path ->
                val file = File(path)
                if (file.exists()) {
                    file.delete()
                }
            }

            // 从自定义列表中移除
            val customFonts = getCustomFonts().toMutableList()
            customFonts.removeAll { it.name == fontItem.name }
            saveCustomFonts(customFonts)

            // 通知监听器
            notifyFontDeleted(fontItem)

            AppLog.d("【字体预设管理器】已删除自定义字体: ${fontItem.name}")
            return true

        } catch (e: Exception) {
            AppLog.e("【字体预设管理器】删除自定义字体失败", e)
            return false
        }
    }

    /**
     * 更新自定义字体名称
     */
    fun updateCustomFontName(oldFontItem: FontItem, newName: String): Boolean {
        try {
            if (oldFontItem.isPreset) {
                AppLog.w("【字体预设管理器】不能修改预设字体名称: ${oldFontItem.name}")
                return false
            }

            // 检查新名称是否为空
            if (newName.isBlank()) {
                AppLog.w("【字体预设管理器】字体名称不能为空")
                return false
            }

            // 检查新名称是否已存在（排除当前字体）
            val existingFonts = getAllFonts().filter { it.name != oldFontItem.name }
            if (existingFonts.any { it.name == newName }) {
                AppLog.w("【字体预设管理器】字体名称已存在: $newName")
                return false
            }

            // 创建新的字体项
            val newFontItem = FontItem(
                name = newName,
                fontFamily = newName.lowercase().replace(" ", "_"),
                isPreset = false,
                filePath = oldFontItem.filePath
            )

            // 更新自定义字体列表
            val customFonts = getCustomFonts().toMutableList()
            val index = customFonts.indexOfFirst { it.name == oldFontItem.name }
            if (index >= 0) {
                customFonts[index] = newFontItem
                saveCustomFonts(customFonts)

                // 通知监听器
                notifyFontNameUpdated(oldFontItem, newFontItem)

                AppLog.d("【字体预设管理器】已更新字体名称: ${oldFontItem.name} -> $newName")
                return true
            } else {
                AppLog.w("【字体预设管理器】未找到要更新的字体: ${oldFontItem.name}")
                return false
            }

        } catch (e: Exception) {
            AppLog.e("【字体预设管理器】更新字体名称失败", e)
            return false
        }
    }
    
    /**
     * 重置为默认字体设置（清除所有自定义字体）
     */
    fun resetToDefault() {
        try {
            val context = getContext() ?: return

            // 删除所有自定义字体文件
            val fontsDir = File(context.filesDir, FONTS_DIR)
            if (fontsDir.exists()) {
                fontsDir.listFiles()?.forEach { it.delete() }
            }

            // 清除自定义字体
            saveCustomFonts(emptyList())

            // 通知监听器
            notifyFontListReset()

            AppLog.d("【字体预设管理器】已重置为默认字体设置")

        } catch (e: Exception) {
            AppLog.e("【字体预设管理器】重置默认设置失败", e)
        }
    }

    
    /**
     * 确保字体目录存在
     */
    private fun ensureFontsDirectoryExists() {
        val context = getContext() ?: return
        val fontsDir = File(context.filesDir, FONTS_DIR)
        if (!fontsDir.exists()) {
            fontsDir.mkdirs()
        }
    }
    
    /**
     * 通知字体添加
     */
    private fun notifyFontAdded(fontItem: FontItem) {
        listeners.forEach { listener ->
            try {
                listener.onFontAdded(fontItem)
            } catch (e: Exception) {
                AppLog.e("【字体预设管理器】通知字体添加失败", e)
            }
        }
    }
    
    /**
     * 通知字体删除
     */
    private fun notifyFontDeleted(fontItem: FontItem) {
        listeners.forEach { listener ->
            try {
                listener.onFontDeleted(fontItem)
            } catch (e: Exception) {
                AppLog.e("【字体预设管理器】通知字体删除失败", e)
            }
        }
    }

    /**
     * 通知字体名称更新
     */
    private fun notifyFontNameUpdated(oldFontItem: FontItem, newFontItem: FontItem) {
        listeners.forEach { listener ->
            try {
                listener.onFontNameUpdated(oldFontItem, newFontItem)
            } catch (e: Exception) {
                AppLog.e("【字体预设管理器】通知字体名称更新失败", e)
            }
        }
    }

    /**
     * 通知字体列表重置
     */
    private fun notifyFontListReset() {
        listeners.forEach { listener ->
            try {
                listener.onFontListReset()
            } catch (e: Exception) {
                AppLog.e("【字体预设管理器】通知字体列表重置失败", e)
            }
        }
    }
}
