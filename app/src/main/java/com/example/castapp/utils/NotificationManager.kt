package com.example.castapp.utils

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import com.example.castapp.R
import com.example.castapp.ui.MainActivity

/**
 * 通知管理器
 * 消除各服务中重复的通知创建代码
 */
object NotificationManager {
    /**
     * 通知类型枚举
     */
    enum class NotificationType(
        val notificationId: Int,
        val channelId: String,
        val channelName: String,
        val channelDescription: String,
        val importance: Int = android.app.NotificationManager.IMPORTANCE_LOW
    ) {
        CASTING(
            notificationId = 1001,
            channelId = "casting_channel",
            channelName = "投屏服务",
            channelDescription = "投屏服务运行状态通知"
        ),
        RECEIVING(
            notificationId = 1002,
            channelId = "receiving_channel", 
            channelName = "接收服务",
            channelDescription = "接收服务运行状态通知"
        ),
        AUDIO_STREAMING(
            notificationId = 2001,
            channelId = "audio_streaming_channel",
            channelName = "音频流服务", 
            channelDescription = "音频流传输服务"
        ),
        FLOATING_STOPWATCH(
            notificationId = 2003,
            channelId = "floating_stopwatch_channel",
            channelName = "悬浮秒表",
            channelDescription = "悬浮秒表运行状态"
        ),
        REMOTE_CONTROL(
            notificationId = 3001,
            channelId = "remote_control_channel",
            channelName = "远程控制服务",
            channelDescription = "远程接收端控制服务运行状态通知"
        )
    }

    /**
     * 通知配置类
     */
    data class NotificationConfig(
        val title: String,
        val content: String,
        val iconRes: Int = R.drawable.ic_notification,
        val isOngoing: Boolean = true,
        val priority: Int = NotificationCompat.PRIORITY_LOW,
        val showBadge: Boolean = false,
        val actions: List<NotificationAction> = emptyList(),
        val contentIntent: PendingIntent? = null
    )

    /**
     * 通知动作配置类
     */
    data class NotificationAction(
        val iconRes: Int,
        val title: String,
        val pendingIntent: PendingIntent
    )

    /**
     * 创建通知渠道
     */
    fun createNotificationChannel(context: Context, type: NotificationType) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                type.channelId,
                type.channelName,
                type.importance
            ).apply {
                description = type.channelDescription
                setShowBadge(false)
            }

            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) 
                as android.app.NotificationManager
            notificationManager.createNotificationChannel(channel)
            
            AppLog.d("创建通知渠道: ${type.channelName}")
        }
    }

    /**
     * 创建通知
     */
    fun createNotification(
        context: Context, 
        type: NotificationType, 
        config: NotificationConfig
    ): Notification {
        val builder = NotificationCompat.Builder(context, type.channelId)
            .setContentTitle(config.title)
            .setContentText(config.content)
            .setSmallIcon(config.iconRes)
            .setOngoing(config.isOngoing)
            .setPriority(config.priority)

        // 添加内容点击意图
        config.contentIntent?.let { builder.setContentIntent(it) }

        // 添加动作按钮
        config.actions.forEach { action ->
            builder.addAction(action.iconRes, action.title, action.pendingIntent)
        }

        return builder.build()
    }


    /**
     * 创建默认的主界面PendingIntent
     */
    fun createMainActivityPendingIntent(context: Context): PendingIntent {
        val intent = Intent(context, MainActivity::class.java)
        return PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    /**
     * 创建服务停止PendingIntent
     */
    fun createServiceStopPendingIntent(
        context: Context,
        serviceClass: Class<out Service>,
        stopAction: String
    ): PendingIntent {
        val intent = Intent(context, serviceClass).apply {
            action = stopAction
        }
        return PendingIntent.getService(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    /**
     * 便捷方法：创建投屏服务通知
     */
    fun createCastingNotification(context: Context, content: String): Notification {
        val config = NotificationConfig(
            title = "投屏服务",
            content = content
        )
        return createNotification(context, NotificationType.CASTING, config)
    }

    /**
     * 便捷方法：创建接收服务通知（带停止按钮）
     */
    fun createReceivingNotification(
        context: Context, 
        content: String,
        stopPendingIntent: PendingIntent
    ): Notification {
        val config = NotificationConfig(
            title = "接收服务",
            content = content,
            actions = listOf(
                NotificationAction(
                    iconRes = R.drawable.ic_notification,
                    title = "停止服务",
                    pendingIntent = stopPendingIntent
                )
            )
        )
        return createNotification(context, NotificationType.RECEIVING, config)
    }

    /**
     * 便捷方法：创建音频流服务通知
     */
    fun createAudioStreamingNotification(context: Context, title: String): Notification {
        val config = NotificationConfig(
            title = title,
            content = "音频流传输中..."
        )
        return createNotification(context, NotificationType.AUDIO_STREAMING, config)
    }

    /**
     * 便捷方法：创建悬浮秒表通知
     */
    fun createFloatingStopwatchNotification(context: Context): Notification {
        val config = NotificationConfig(
            title = context.getString(R.string.floating_stopwatch),
            content = context.getString(R.string.stopwatch_running),
            iconRes = R.drawable.ic_stopwatch,
            contentIntent = createMainActivityPendingIntent(context)
        )
        return createNotification(context, NotificationType.FLOATING_STOPWATCH, config)
    }

    /**
     * 便捷方法：创建远程控制服务通知（带停止按钮）
     */
    fun createRemoteControlNotification(
        context: Context,
        content: String,
        stopPendingIntent: PendingIntent
    ): Notification {
        val config = NotificationConfig(
            title = "远程控制服务",
            content = content,
            actions = listOf(
                NotificationAction(
                    iconRes = R.drawable.ic_notification,
                    title = "停止服务",
                    pendingIntent = stopPendingIntent
                )
            )
        )
        return createNotification(context, NotificationType.REMOTE_CONTROL, config)
    }
}
