package com.example.castapp.utils

import android.os.Debug
import kotlinx.coroutines.*
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * 内存监控管理器
 * 用于监控应用内存使用情况，检测内存泄漏和性能问题
 */
class MemoryMonitor private constructor() {

    companion object {
        // 🚀 CPU优化：增加内存监控间隔，减少监控频率
        private const val MONITOR_INTERVAL_MS = 120000L // 120秒监控间隔
        private const val MEMORY_WARNING_THRESHOLD = 0.8 // 内存警告阈值80%
        private const val MEMORY_CRITICAL_THRESHOLD = 0.9 // 内存危险阈值90%
        private const val GC_SUGGESTION_THRESHOLD = 50 * 1024 * 1024 // 50MB增长触发GC建议

        @Volatile
        private var INSTANCE: MemoryMonitor? = null

        fun getInstance(): MemoryMonitor {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: MemoryMonitor().also { INSTANCE = it }
            }
        }

        /**
         * 清理单例实例 - 🚀 根源优化：只清理引用，不调用cleanup()避免重复
         */
        fun clearInstance() {
            synchronized(this) {
                if (INSTANCE != null) {
                    AppLog.memory("清理MemoryMonitor单例引用")
                    INSTANCE = null
                } else {
                    AppLog.memory("MemoryMonitor单例引用已清理")
                }
            }
        }
    }
    private val isMonitoring = AtomicBoolean(false)
    private val monitorScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var monitorJob: Job? = null
    
    // 内存使用历史记录
    private val memoryHistory = mutableListOf<MemorySnapshot>()
    private val maxHistorySize = 20 // 保留最近20次记录
    
    // 内存泄漏检测
    private val lastMemoryUsage = AtomicLong(0)
    private val memoryGrowthCounter = AtomicLong(0)
    
    // 监听器管理
    private val listeners = ConcurrentHashMap.newKeySet<WeakReference<MemoryMonitorListener>>()
    
    data class MemorySnapshot(
        val timestamp: Long,
        val usedMemoryMB: Long,
        val maxMemoryMB: Long,
        val freeMemoryMB: Long,
        val nativeHeapSizeMB: Long,
        val nativeHeapAllocatedMB: Long,
        val memoryUsagePercent: Double
    )
    
    interface MemoryMonitorListener {
        fun onMemoryWarning(snapshot: MemorySnapshot)
        fun onMemoryCritical(snapshot: MemorySnapshot)
        fun onMemoryLeakSuspected(growthMB: Long, consecutiveGrowths: Long)
        fun onMemoryStable(snapshot: MemorySnapshot)
    }
    
    /**
     * 开始内存监控
     */
    fun startMonitoring() {
        if (isMonitoring.compareAndSet(false, true)) {
            AppLog.memory("开始内存监控，监控间隔: ${MONITOR_INTERVAL_MS}ms")
            
            monitorJob = monitorScope.launch {
                while (isActive && isMonitoring.get()) {
                    try {
                        val snapshot = captureMemorySnapshot()
                        analyzeMemoryUsage(snapshot)

                        // 保存历史记录
                        synchronized(memoryHistory) {
                            memoryHistory.add(snapshot)
                            if (memoryHistory.size > maxHistorySize) {
                                memoryHistory.removeAt(0)
                            }
                        }

                        delay(MONITOR_INTERVAL_MS)
                    } catch (e: CancellationException) {
                        // 协程被正常取消，这是预期行为
                        AppLog.memory("内存监控已取消")
                        throw e // 重新抛出以正确处理取消
                    } catch (e: Exception) {
                        AppLog.e("内存监控异常", e)
                        delay(MONITOR_INTERVAL_MS)
                    }
                }
            }
        }
    }
    
    /**
     * 停止内存监控
     */
    fun stopMonitoring() {
        if (isMonitoring.compareAndSet(true, false)) {
            AppLog.memory("停止内存监控")
            monitorJob?.cancel()
            monitorJob = null
        }
    }
    
    /**
     * 捕获内存快照
     */
    private fun captureMemorySnapshot(): MemorySnapshot {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory()
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        val usedMemory = totalMemory - freeMemory
        
        val nativeHeapSize = Debug.getNativeHeapSize()
        val nativeHeapAllocated = Debug.getNativeHeapAllocatedSize()
        
        val memoryUsagePercent = usedMemory.toDouble() / maxMemory.toDouble()
        
        return MemorySnapshot(
            timestamp = System.currentTimeMillis(),
            usedMemoryMB = usedMemory / (1024 * 1024),
            maxMemoryMB = maxMemory / (1024 * 1024),
            freeMemoryMB = freeMemory / (1024 * 1024),
            nativeHeapSizeMB = nativeHeapSize / (1024 * 1024),
            nativeHeapAllocatedMB = nativeHeapAllocated / (1024 * 1024),
            memoryUsagePercent = memoryUsagePercent
        )
    }
    
    /**
     * 分析内存使用情况
     */
    private fun analyzeMemoryUsage(snapshot: MemorySnapshot) {
        val currentUsage = snapshot.usedMemoryMB
        val lastUsage = lastMemoryUsage.get()
        
        // 检测内存增长
        if (lastUsage > 0) {
            val growth = currentUsage - lastUsage
            
            if (growth > GC_SUGGESTION_THRESHOLD / (1024 * 1024)) {
                // 内存显著增长
                val consecutiveGrowths = memoryGrowthCounter.incrementAndGet()
                AppLog.w("检测到内存增长: ${growth}MB, 连续增长次数: $consecutiveGrowths")
                
                if (consecutiveGrowths >= 3) {
                    // 连续3次增长，可能存在内存泄漏
                    notifyMemoryLeakSuspected(growth, consecutiveGrowths)
                }
            } else if (growth <= 0) {
                // 内存稳定或减少，重置计数器
                memoryGrowthCounter.set(0)
                notifyMemoryStable(snapshot)
            }
        }
        
        lastMemoryUsage.set(currentUsage)
        
        // 🚀 CPU优化：简化内存使用阈值检查，减少字符串格式化
        when {
            snapshot.memoryUsagePercent >= MEMORY_CRITICAL_THRESHOLD -> {
                val percent = (snapshot.memoryUsagePercent * 100).toInt()
                AppLog.e("内存使用达到危险水平: ${percent}%")
                notifyMemoryCritical(snapshot)
            }
            snapshot.memoryUsagePercent >= MEMORY_WARNING_THRESHOLD -> {
                val percent = (snapshot.memoryUsagePercent * 100).toInt()
                AppLog.w("内存使用达到警告水平: ${percent}%")
                notifyMemoryWarning(snapshot)
            }
            else -> {
                val percent = (snapshot.memoryUsagePercent * 100).toInt()
                AppLog.memory("内存使用正常: ${snapshot.usedMemoryMB}MB/${snapshot.maxMemoryMB}MB (${percent}%)")
            }
        }
    }
    
    /**
     * 添加监听器
     */
    fun addListener(listener: MemoryMonitorListener) {
        listeners.add(WeakReference(listener))
        cleanupListeners()
    }
    

    
    /**
     * 清理失效的监听器引用
     */
    private fun cleanupListeners() {
        listeners.removeAll { it.get() == null }
    }
    
    /**
     * 通知内存警告
     */
    private fun notifyMemoryWarning(snapshot: MemorySnapshot) {
        cleanupListeners()
        listeners.forEach { ref ->
            ref.get()?.onMemoryWarning(snapshot)
        }
    }
    
    /**
     * 通知内存危险
     */
    private fun notifyMemoryCritical(snapshot: MemorySnapshot) {
        cleanupListeners()
        listeners.forEach { ref ->
            ref.get()?.onMemoryCritical(snapshot)
        }
    }
    
    /**
     * 通知疑似内存泄漏
     */
    private fun notifyMemoryLeakSuspected(growthMB: Long, consecutiveGrowths: Long) {
        cleanupListeners()
        listeners.forEach { ref ->
            ref.get()?.onMemoryLeakSuspected(growthMB, consecutiveGrowths)
        }
    }
    
    /**
     * 通知内存稳定
     */
    private fun notifyMemoryStable(snapshot: MemorySnapshot) {
        cleanupListeners()
        listeners.forEach { ref ->
            ref.get()?.onMemoryStable(snapshot)
        }
    }
    

    
    /**
     * 强制执行垃圾回收
     */
    fun forceGarbageCollection() {
        AppLog.memory("强制执行垃圾回收")
        System.gc()
        System.runFinalization()
        System.gc()
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        AppLog.memory("清理MemoryMonitor资源...")
        stopMonitoring()

        try {
            monitorScope.cancel()
            listeners.clear()
            memoryHistory.clear()
            AppLog.memory("MemoryMonitor资源清理完成")
        } catch (e: Exception) {
            AppLog.e("清理MemoryMonitor资源时发生异常", e)
        }
    }
}
