package com.example.castapp.utils

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * 全局字间距预设管理器
 * 负责管理所有文本窗口共享的字间距预设列表
 * 提供持久化存储和实时同步功能
 */
class LetterSpacingPresetManager private constructor(context: Context) {
    
    companion object {
        private const val PREF_NAME = "letter_spacing_preset_preferences"
        private const val KEY_CUSTOM_LETTER_SPACINGS = "custom_letter_spacings"
        
        // 预设字间距（不可删除）
        val PRESET_LETTER_SPACINGS = listOf(0.0f, 0.1f, 0.2f, 0.3f, 0.4f, 0.5f)
        
        // 单例实例
        @Volatile
        private var INSTANCE: LetterSpacingPresetManager? = null
        
        /**
         * 获取单例实例
         * 使用 applicationContext 避免内存泄漏
         */
        fun getInstance(context: Context): LetterSpacingPresetManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: LetterSpacingPresetManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    
    private val gson = Gson()
    
    // 字间距列表变化监听器
    private val listeners = mutableSetOf<LetterSpacingPresetListener>()
    
    /**
     * 字间距预设列表变化监听器接口
     */
    interface LetterSpacingPresetListener {
        /**
         * 字间距被添加时调用
         */
        fun onLetterSpacingAdded(letterSpacing: Float)
        
        /**
         * 字间距被删除时调用
         */
        fun onLetterSpacingDeleted(letterSpacing: Float)
        
        /**
         * 字间距列表被重置时调用
         */
        fun onLetterSpacingListReset()
    }
    
    /**
     * 获取完整的字间距列表（预设 + 自定义）
     */
    fun getAllLetterSpacings(): List<Float> {
        val customLetterSpacings = getCustomLetterSpacings()
        val allLetterSpacings = (PRESET_LETTER_SPACINGS + customLetterSpacings).distinct().sorted()
        
        AppLog.d("【字间距预设管理器】获取完整字间距列表: $allLetterSpacings")
        return allLetterSpacings
    }
    
    /**
     * 获取字间距选项字符串列表（用于Spinner显示）
     */
    fun getAllLetterSpacingOptions(): List<String> {
        return getAllLetterSpacings().map { "${it}em" }
    }
    
    /**
     * 获取自定义字间距列表
     */
    private fun getCustomLetterSpacings(): List<Float> {
        return try {
            val json = sharedPreferences.getString(KEY_CUSTOM_LETTER_SPACINGS, null)
            if (json != null) {
                val type = object : TypeToken<List<Float>>() {}.type
                gson.fromJson(json, type) ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            AppLog.e("【字间距预设管理器】获取自定义字间距失败", e)
            emptyList()
        }
    }
    
    /**
     * 保存自定义字间距列表
     */
    private fun saveCustomLetterSpacings(letterSpacings: List<Float>) {
        try {
            val json = gson.toJson(letterSpacings)
            sharedPreferences.edit()
                .putString(KEY_CUSTOM_LETTER_SPACINGS, json)
                .apply()
            
            AppLog.d("【字间距预设管理器】自定义字间距已保存: $letterSpacings")
            
        } catch (e: Exception) {
            AppLog.e("【字间距预设管理器】保存自定义字间距失败", e)
        }
    }
    
    /**
     * 添加自定义字间距
     */
    fun addCustomLetterSpacing(letterSpacing: Float): Boolean {
        try {
            // 检查是否已存在
            if (getAllLetterSpacings().contains(letterSpacing)) {
                AppLog.d("【字间距预设管理器】字间距${letterSpacing}em已存在")
                return false
            }
            
            // 检查字间距范围
            if (letterSpacing < -0.5f || letterSpacing > 2.0f) {
                AppLog.w("【字间距预设管理器】字间距${letterSpacing}em超出有效范围(-0.5 ~ 2.0)")
                return false
            }
            
            // 添加到自定义列表
            val customLetterSpacings = getCustomLetterSpacings().toMutableList()
            customLetterSpacings.add(letterSpacing)
            customLetterSpacings.sort()
            
            // 保存到存储
            saveCustomLetterSpacings(customLetterSpacings)
            
            // 通知监听器
            notifyLetterSpacingAdded(letterSpacing)
            
            AppLog.d("【字间距预设管理器】已添加自定义字间距: ${letterSpacing}em")
            return true
            
        } catch (e: Exception) {
            AppLog.e("【字间距预设管理器】添加自定义字间距失败", e)
            return false
        }
    }
    
    /**
     * 删除自定义字间距
     */
    fun deleteCustomLetterSpacing(letterSpacing: Float): Boolean {
        try {
            // 检查是否为预设字间距
            if (PRESET_LETTER_SPACINGS.contains(letterSpacing)) {
                AppLog.w("【字间距预设管理器】无法删除预设字间距: ${letterSpacing}em")
                return false
            }
            
            // 从自定义列表中删除
            val customLetterSpacings = getCustomLetterSpacings().toMutableList()
            val removed = customLetterSpacings.remove(letterSpacing)
            
            if (removed) {
                // 保存到存储
                saveCustomLetterSpacings(customLetterSpacings)
                
                // 通知监听器
                notifyLetterSpacingDeleted(letterSpacing)
                
                AppLog.d("【字间距预设管理器】已删除自定义字间距: ${letterSpacing}em")
                return true
            } else {
                AppLog.w("【字间距预设管理器】字间距${letterSpacing}em不存在于自定义列表中")
                return false
            }
            
        } catch (e: Exception) {
            AppLog.e("【字间距预设管理器】删除自定义字间距失败", e)
            return false
        }
    }
    
    /**
     * 重置为默认字间距设置（清除所有自定义字间距）
     */
    fun resetToDefault() {
        try {
            // 清除自定义字间距
            saveCustomLetterSpacings(emptyList())
            
            // 通知监听器
            notifyLetterSpacingListReset()
            
            AppLog.d("【字间距预设管理器】已重置为默认字间距设置")
            
        } catch (e: Exception) {
            AppLog.e("【字间距预设管理器】重置默认设置失败", e)
        }
    }
    
    /**
     * 检查字间距是否为预设字间距
     */
    fun isPresetLetterSpacing(letterSpacing: Float): Boolean {
        return PRESET_LETTER_SPACINGS.contains(letterSpacing)
    }
    
    /**
     * 添加字间距列表变化监听器
     */
    fun addListener(listener: LetterSpacingPresetListener) {
        listeners.add(listener)
        AppLog.d("【字间距预设管理器】已添加监听器，当前监听器数量: ${listeners.size}")
    }
    
    /**
     * 移除字间距列表变化监听器
     */
    fun removeListener(listener: LetterSpacingPresetListener) {
        listeners.remove(listener)
        AppLog.d("【字间距预设管理器】已移除监听器，当前监听器数量: ${listeners.size}")
    }
    
    // ========== 私有通知方法 ==========
    
    private fun notifyLetterSpacingAdded(letterSpacing: Float) {
        listeners.forEach { listener ->
            try {
                listener.onLetterSpacingAdded(letterSpacing)
            } catch (e: Exception) {
                AppLog.e("【字间距预设管理器】通知监听器添加事件失败", e)
            }
        }
    }
    
    private fun notifyLetterSpacingDeleted(letterSpacing: Float) {
        listeners.forEach { listener ->
            try {
                listener.onLetterSpacingDeleted(letterSpacing)
            } catch (e: Exception) {
                AppLog.e("【字间距预设管理器】通知监听器删除事件失败", e)
            }
        }
    }
    
    private fun notifyLetterSpacingListReset() {
        listeners.forEach { listener ->
            try {
                listener.onLetterSpacingListReset()
            } catch (e: Exception) {
                AppLog.e("【字间距预设管理器】通知监听器重置事件失败", e)
            }
        }
    }
}
