package com.example.castapp.ui.adapter

import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.utils.AppLog
import com.example.castapp.utils.ColorUtils

/**
 * 🎨 自定义色板适配器
 * 用于显示用户保存的自定义颜色
 */
class CustomColorPaletteAdapter(
    private var colors: MutableList<Int> = mutableListOf(),
    private val onColorSelected: (Int) -> Unit,
    private val onColorLongClick: (Int) -> Unit
) : RecyclerView.Adapter<CustomColorPaletteAdapter.ColorViewHolder>() {

    companion object {
        private const val STROKE_WIDTH_DP = 1 // 边框宽度
    }

    private var selectedColor: Int? = null

    class ColorViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val colorView: View = itemView.findViewById(R.id.view_color_item)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ColorViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_color_palette, parent, false)
        return ColorViewHolder(view)
    }

    override fun onBindViewHolder(holder: ColorViewHolder, position: Int) {
        val color = colors[position]
        
        // 创建圆形背景drawable
        val drawable = GradientDrawable().apply {
            shape = GradientDrawable.OVAL
            setColor(color)
            
            // 如果是选中的颜色，添加边框
            if (color == selectedColor) {
                setStroke(
                    (STROKE_WIDTH_DP * holder.itemView.context.resources.displayMetrics.density).toInt(),
                    ColorUtils.getContrastColor(color)
                )
            }
        }
        
        holder.colorView.background = drawable
        
        // 设置点击事件
        holder.colorView.setOnClickListener {
            AppLog.d("🎨 选择自定义颜色: ${ColorUtils.colorToArgbHex(color)}")
            setSelectedColor(color)
            onColorSelected(color)
        }
        
        // 设置长按事件（用于删除）
        holder.colorView.setOnLongClickListener {
            AppLog.d("🎨 长按自定义颜色: ${ColorUtils.colorToArgbHex(color)}")
            onColorLongClick(color)
            true
        }
    }

    override fun getItemCount(): Int = colors.size

    /**
     * 🎨 更新颜色列表
     */
    fun updateColors(newColors: List<Int>) {
        colors.clear()
        colors.addAll(newColors)
        notifyDataSetChanged()
        AppLog.d("🎨 自定义色板更新: ${colors.size}个颜色")
    }

    /**
     * 🎨 添加颜色
     */
    fun addColor(color: Int) {
        if (!colors.contains(color)) {
            colors.add(color)
            notifyItemInserted(colors.size - 1)
            AppLog.d("🎨 添加自定义颜色: ${ColorUtils.colorToArgbHex(color)}")
        }
    }

    /**
     * 🎨 删除颜色
     */
    fun removeColor(color: Int) {
        val index = colors.indexOf(color)
        if (index != -1) {
            colors.removeAt(index)
            notifyItemRemoved(index)
            AppLog.d("🎨 删除自定义颜色: ${ColorUtils.colorToArgbHex(color)}")
        }
    }

    /**
     * 🎨 设置选中的颜色
     */
    fun setSelectedColor(color: Int?) {
        val oldSelectedColor = selectedColor
        selectedColor = color
        
        // 刷新之前选中的颜色
        if (oldSelectedColor != null) {
            val oldIndex = colors.indexOf(oldSelectedColor)
            if (oldIndex != -1) {
                notifyItemChanged(oldIndex)
            }
        }
        
        // 刷新当前选中的颜色
        if (color != null) {
            val newIndex = colors.indexOf(color)
            if (newIndex != -1) {
                notifyItemChanged(newIndex)
            }
        }
    }

    /**
     * 🎨 获取当前选中的颜色
     */
    fun getSelectedColor(): Int? = selectedColor

}
