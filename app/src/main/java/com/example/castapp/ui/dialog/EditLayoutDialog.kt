package com.example.castapp.ui.dialog

import android.app.AlertDialog
import android.content.Context
import android.view.LayoutInflater
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import com.example.castapp.R
import com.example.castapp.database.entity.WindowLayoutEntity
import com.example.castapp.manager.LayoutManager
import com.example.castapp.utils.ToastUtils
import com.example.castapp.utils.AppLog

/**
 * 编辑布局名称对话框
 * 用于修改已保存布局的名称
 */
class EditLayoutDialog(
    private val context: Context,
    private val layout: WindowLayoutEntity,
    private val onEditSuccess: (() -> Unit)? = null
) {
    
    private var dialog: AlertDialog? = null
    private val layoutManager = LayoutManager.getInstance()
    
    /**
     * 显示编辑布局名称对话框
     */
    fun show() {
        try {
            val view = LayoutInflater.from(context).inflate(R.layout.dialog_edit_layout, null)

            // 获取视图组件
            val layoutNameInput = view.findViewById<EditText>(R.id.layout_name_input)
            val currentNameText = view.findViewById<TextView>(R.id.current_name_text)
            val saveButton = view.findViewById<Button>(R.id.btn_save)
            val cancelButton = view.findViewById<Button>(R.id.btn_cancel)

            // 设置当前布局名称
            currentNameText.text = layout.layoutName
            layoutNameInput.setText(layout.layoutName)
            layoutNameInput.selectAll()

            // 创建对话框
            val builder = AlertDialog.Builder(context)
                .setView(view)
                .setCancelable(true)

            dialog = builder.create()

            // 设置按钮点击监听器
            saveButton.setOnClickListener {
                handleSaveLayoutName(layoutNameInput, saveButton)
            }

            cancelButton.setOnClickListener {
                AppLog.d("用户取消编辑布局名称")
                dialog?.dismiss()
            }

            dialog?.show()

            // 自动弹出键盘
            layoutNameInput.requestFocus()

            AppLog.d("编辑布局名称对话框已显示，当前布局: ${layout.layoutName}")

        } catch (e: Exception) {
            AppLog.e("显示编辑布局名称对话框失败", e)
            ToastUtils.showToast(context, "显示对话框失败")
        }
    }
    
    /**
     * 处理保存布局名称逻辑
     */
    private fun handleSaveLayoutName(layoutNameInput: EditText, saveButton: Button) {
        val newLayoutName = layoutNameInput.text.toString().trim()
        
        // 验证输入
        if (newLayoutName.isEmpty()) {
            ToastUtils.showToast(context, "请输入布局名称")
            return
        }

        if (newLayoutName.length > 50) {
            ToastUtils.showToast(context, "布局名称不能超过50个字符")
            return
        }
        
        // 如果名称没有变化，直接关闭对话框
        if (newLayoutName == layout.layoutName) {
            AppLog.d("布局名称未发生变化，直接关闭对话框")
            dialog?.dismiss()
            return
        }
        
        // 禁用保存按钮，防止重复点击
        saveButton.isEnabled = false

        AppLog.d("开始更新布局名称: ${layout.layoutName} -> $newLayoutName")

        // 调用布局管理器更新布局名称
        layoutManager.updateLayoutName(layout.id, newLayoutName) { success, message ->
            // 重新启用保存按钮
            saveButton.isEnabled = true
            
            if (success) {
                AppLog.d("布局名称更新成功: $newLayoutName")
                ToastUtils.showToast(context, "布局名称更新成功")
                dialog?.dismiss()
                onEditSuccess?.invoke()
            } else {
                AppLog.w("布局名称更新失败: $message")
                ToastUtils.showToast(context, message)
                // 更新失败时不关闭对话框，让用户可以修改名称
            }
        }
    }
}
