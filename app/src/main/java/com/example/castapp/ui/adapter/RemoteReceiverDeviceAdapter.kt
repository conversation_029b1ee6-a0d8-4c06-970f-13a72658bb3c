package com.example.castapp.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.model.RemoteReceiverConnection

/**
 * 远程接收端设备列表适配器
 */
class RemoteReceiverDeviceAdapter(
    private val receivers: MutableList<RemoteReceiverConnection>,
    private val onConnectClick: (RemoteReceiverConnection) -> Unit,
    private val onControlClick: (RemoteReceiverConnection) -> Unit,
    private val onEditClick: (RemoteReceiverConnection) -> Unit,
    private val onDeleteClick: (RemoteReceiverConnection) -> Unit
) : RecyclerView.Adapter<RemoteReceiverDeviceAdapter.ViewHolder>() {

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val deviceIcon: ImageView = itemView.findViewById(R.id.device_icon)
        val deviceNameText: TextView = itemView.findViewById(R.id.device_name_text)
        val deviceAddressText: TextView = itemView.findViewById(R.id.device_address_text)
        val connectionStatusText: TextView = itemView.findViewById(R.id.connection_status_text)
        val connectButton: Button = itemView.findViewById(R.id.connect_button)
        val controlButton: Button = itemView.findViewById(R.id.control_button)
        val editButton: Button = itemView.findViewById(R.id.edit_button)
        val deleteButton: Button = itemView.findViewById(R.id.delete_button)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_remote_receiver, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val receiver = receivers[position]
        
        // 设置设备信息
        holder.deviceNameText.text = receiver.deviceName
        holder.deviceAddressText.text = receiver.getDisplayText()

        // 设置设备图标颜色 - 连接成功时变为绿色
        holder.deviceIcon.imageTintList = holder.itemView.context.getColorStateList(
            if (receiver.isConnected) android.R.color.holo_green_light
            else android.R.color.darker_gray
        )

        // 设置连接状态
        holder.connectionStatusText.text = receiver.getStatusText()
        holder.connectionStatusText.setTextColor(
            if (receiver.isConnected) {
                holder.itemView.context.getColor(android.R.color.holo_green_dark)
            } else {
                holder.itemView.context.getColor(android.R.color.darker_gray)
            }
        )

        // 设置连接按钮文字和颜色
        holder.connectButton.text = if (receiver.isConnected) "断开" else "连接"
        holder.connectButton.backgroundTintList = holder.itemView.context.getColorStateList(
            if (receiver.isConnected) android.R.color.holo_red_light
            else android.R.color.holo_green_light
        )

        // 控制按钮只在已连接时可用
        holder.controlButton.isEnabled = receiver.isConnected
        holder.controlButton.alpha = if (receiver.isConnected) 1.0f else 0.5f

        // 编辑按钮在已连接时禁用，防止编辑活跃连接
        holder.editButton.isEnabled = !receiver.isConnected
        holder.editButton.alpha = if (receiver.isConnected) 0.5f else 1.0f

        // 删除按钮在已连接时禁用，防止意外删除活跃连接
        holder.deleteButton.isEnabled = !receiver.isConnected
        holder.deleteButton.alpha = if (receiver.isConnected) 0.5f else 1.0f

        // 设置点击事件
        holder.connectButton.setOnClickListener {
            onConnectClick(receiver)
        }

        holder.controlButton.setOnClickListener {
            if (receiver.isConnected) {
                onControlClick(receiver)
            }
        }

        holder.editButton.setOnClickListener {
            if (!receiver.isConnected) {
                onEditClick(receiver)
            }
        }

        holder.deleteButton.setOnClickListener {
            if (!receiver.isConnected) {
                onDeleteClick(receiver)
            }
        }
    }

    override fun getItemCount(): Int = receivers.size

    /**
     * 更新接收端状态
     */
    fun updateReceiver(updatedReceiver: RemoteReceiverConnection) {
        val index = receivers.indexOfFirst { it.id == updatedReceiver.id }
        if (index != -1) {
            receivers[index] = updatedReceiver
            notifyItemChanged(index)
        }
    }

    /**
     * 添加接收端
     */
    fun addReceiver(receiver: RemoteReceiverConnection) {
        receivers.add(receiver)
        notifyItemInserted(receivers.size - 1)
    }

    /**
     * 删除接收端
     */
    fun removeReceiver(receiver: RemoteReceiverConnection) {
        val index = receivers.indexOfFirst { it.id == receiver.id }
        if (index != -1) {
            receivers.removeAt(index)
            notifyItemRemoved(index)
        }
    }
}
