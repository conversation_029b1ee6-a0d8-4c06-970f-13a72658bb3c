package com.example.castapp.ui.dialog

import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.ImageButton
import android.widget.RadioButton
import android.widget.RadioGroup
import com.example.castapp.R
import com.example.castapp.utils.AppLog

/**
 * 保存选项对话框
 * 用于选择保存布局参数的模式
 */
class SaveOptionsDialog(
    private val context: Context,
    private val onSaveOptionSelected: (isUpdateMode: Boolean) -> Unit
) : Dialog(context) {

    // UI组件
    private lateinit var radioGroupSaveMode: RadioGroup
    private lateinit var radioUpdateExisting: RadioButton
    private lateinit var radioReplaceAll: RadioButton
    private lateinit var btnConfirm: Button
    private lateinit var btnCancel: Button
    private lateinit var btnClose: ImageButton

    init {
        initDialog()
    }

    /**
     * 初始化对话框
     */
    private fun initDialog() {
        try {
            val view = LayoutInflater.from(context).inflate(R.layout.dialog_save_options, null)
            
            // 初始化视图
            initViews(view)
            setupClickListeners()
            
            setContentView(view)
            setCancelable(true)
            setCanceledOnTouchOutside(true)
            
            AppLog.d("保存选项对话框初始化完成")
        } catch (e: Exception) {
            AppLog.e("初始化保存选项对话框失败", e)
        }
    }

    /**
     * 初始化视图组件
     */
    private fun initViews(view: View) {
        radioGroupSaveMode = view.findViewById(R.id.radio_group_save_mode)
        radioUpdateExisting = view.findViewById(R.id.radio_update_existing)
        radioReplaceAll = view.findViewById(R.id.radio_replace_all)
        btnConfirm = view.findViewById(R.id.btn_confirm)
        btnCancel = view.findViewById(R.id.btn_cancel)
        btnClose = view.findViewById(R.id.btn_close)
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        btnConfirm.setOnClickListener {
            handleConfirm()
        }

        btnCancel.setOnClickListener {
            AppLog.d("用户取消保存操作")
            dismiss()
        }

        btnClose.setOnClickListener {
            AppLog.d("用户关闭保存选项对话框")
            dismiss()
        }
    }

    /**
     * 处理确认操作
     */
    private fun handleConfirm() {
        try {
            val isUpdateMode = radioUpdateExisting.isChecked
            val modeText = if (isUpdateMode) "更新已有布局参数" else "清空已有布局参数并重新保存"
            
            AppLog.d("用户选择保存模式: $modeText")
            
            // 回调选择结果
            onSaveOptionSelected(isUpdateMode)
            
            // 关闭对话框
            dismiss()
            
        } catch (e: Exception) {
            AppLog.e("处理保存选项确认失败", e)
        }
    }
}
