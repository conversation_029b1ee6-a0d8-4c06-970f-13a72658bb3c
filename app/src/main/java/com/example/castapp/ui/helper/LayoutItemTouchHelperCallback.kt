package com.example.castapp.ui.helper

import android.graphics.Canvas
import android.os.Handler
import android.os.Looper
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.utils.AppLog

/**
 * 布局列表拖拽排序的ItemTouchHelper回调类
 * 实现长按拖拽排序功能
 */
class LayoutItemTouchHelperCallback(
    private val adapter: ItemTouchHelperAdapter
) : ItemTouchHelper.Callback() {

    // 主线程Handler，用于可靠的延迟执行
    private val mainHandler = Handler(Looper.getMainLooper())

    /**
     * 拖拽排序适配器接口
     */
    interface ItemTouchHelperAdapter {
        /**
         * 当两个item位置交换时调用
         * @param fromPosition 起始位置
         * @param toPosition 目标位置
         * @return 是否成功移动
         */
        fun onItemMove(fromPosition: Int, toPosition: Int): Boolean
        
        /**
         * 当拖拽完成时调用，用于保存最终的排序结果
         * @param fromPosition 起始位置
         * @param toPosition 最终位置
         */
        fun onItemMoveCompleted(fromPosition: Int, toPosition: Int)
    }

    // 记录拖拽的起始位置
    private var dragFromPosition = -1
    private var dragToPosition = -1

    /**
     * 设置支持的移动方向
     * 只支持上下拖拽，不支持左右滑动
     */
    override fun getMovementFlags(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder
    ): Int {
        val dragFlags = ItemTouchHelper.UP or ItemTouchHelper.DOWN
        val swipeFlags = 0 // 不支持滑动删除
        return makeMovementFlags(dragFlags, swipeFlags)
    }

    /**
     * 当item被拖拽移动时调用
     */
    override fun onMove(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        val fromPosition = viewHolder.bindingAdapterPosition
        val toPosition = target.bindingAdapterPosition
        
        // 记录拖拽位置
        if (dragFromPosition == -1) {
            dragFromPosition = fromPosition
        }
        dragToPosition = toPosition
        
        AppLog.d("拖拽移动: $fromPosition -> $toPosition")
        
        // 通知适配器移动item
        return adapter.onItemMove(fromPosition, toPosition)
    }

    /**
     * 不支持滑动删除
     */
    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
        // 不实现滑动删除功能
    }

    /**
     * 是否启用长按拖拽
     */
    override fun isLongPressDragEnabled(): Boolean {
        return true
    }

    /**
     * 是否启用滑动删除
     */
    override fun isItemViewSwipeEnabled(): Boolean {
        return false
    }

    /**
     * 设置拖拽阈值为50%，实现半程拖拽交换
     */
    override fun getMoveThreshold(viewHolder: RecyclerView.ViewHolder): Float {
        return 0.5f
    }

    /**
     * 优化拖拽目标选择，实现半程交换效果
     */
    override fun chooseDropTarget(
        selected: RecyclerView.ViewHolder,
        dropTargets: List<RecyclerView.ViewHolder>,
        curX: Int,
        curY: Int
    ): RecyclerView.ViewHolder? {
        // 如果只有一个目标，直接返回
        if (dropTargets.size == 1) {
            return dropTargets[0]
        }
        // 否则使用默认逻辑
        return super.chooseDropTarget(selected, dropTargets, curX, curY)
    }

    /**
     * 当拖拽状态改变时调用
     */
    override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
        super.onSelectedChanged(viewHolder, actionState)

        when (actionState) {
            ItemTouchHelper.ACTION_STATE_DRAG -> {
                // 开始拖拽，设置透明度
                viewHolder?.itemView?.alpha = 0.7f
                AppLog.d("开始拖拽布局项")
            }
            ItemTouchHelper.ACTION_STATE_IDLE -> {
                // 拖拽结束，只有在实际发生位置改变时才触发保存
                if (dragFromPosition != -1 && dragToPosition != -1 && dragFromPosition != dragToPosition) {
                    AppLog.d("拖拽完成，位置改变: $dragFromPosition -> $dragToPosition")

                    // 保存拖拽位置到局部变量，避免在延迟执行时被重置
                    val finalFromPosition = dragFromPosition
                    val finalToPosition = dragToPosition

                    // 使用Handler延迟执行，确保动画完成且不依赖viewHolder生命周期
                    mainHandler.post {
                        AppLog.d("延迟执行数据保存: $finalFromPosition -> $finalToPosition")
                        adapter.onItemMoveCompleted(finalFromPosition, finalToPosition)
                    }
                } else {
                    AppLog.d("拖拽完成，位置未改变")
                }
                // 重置拖拽位置记录
                dragFromPosition = -1
                dragToPosition = -1
            }
        }
    }

    /**
     * 当拖拽结束，清除视图状态时调用
     */
    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        super.clearView(recyclerView, viewHolder)
        
        // 恢复透明度
        viewHolder.itemView.alpha = 1.0f
        AppLog.d("清除拖拽视图状态")
    }

    /**
     * 自定义拖拽绘制，添加阴影效果
     */
    override fun onChildDraw(
        c: Canvas,
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        dX: Float,
        dY: Float,
        actionState: Int,
        isCurrentlyActive: Boolean
    ) {
        if (actionState == ItemTouchHelper.ACTION_STATE_DRAG && isCurrentlyActive) {
            // 拖拽时添加轻微的阴影效果
            viewHolder.itemView.elevation = 8f
        }
        
        super.onChildDraw(c, recyclerView, viewHolder, dX, dY, actionState, isCurrentlyActive)
    }
}
