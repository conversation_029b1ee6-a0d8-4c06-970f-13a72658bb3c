package com.example.castapp.ui.dialog

import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.appcompat.widget.SwitchCompat
import androidx.fragment.app.DialogFragment
import com.example.castapp.R
import com.example.castapp.manager.RemoteConnectionManager
import com.example.castapp.manager.RemoteReceiverManager
import com.example.castapp.model.RemoteReceiverConnection
import com.example.castapp.utils.AppLog
import com.example.castapp.websocket.ControlMessage

/**
 * 🎛️ 远程接收端设置控制对话框
 * 独立的控制面板，专门用于远程控制接收端的各种设置
 * 
 * 主要功能：
 * - 远程控制音视频服务开关
 * - 远程切换播放模式（扬声器/听筒）
 * - 远程调整音量
 * - 设置状态同步
 */
class RemoteReceiverSettingsControlDialog : DialogFragment() {

    companion object {
        private const val ARG_RECEIVER_CONNECTION = "receiver_connection"

        fun newInstance(receiverConnection: RemoteReceiverConnection): RemoteReceiverSettingsControlDialog {
            val fragment = RemoteReceiverSettingsControlDialog()
            val args = Bundle()
            args.putSerializable(ARG_RECEIVER_CONNECTION, receiverConnection)
            fragment.arguments = args
            return fragment
        }
    }

    // UI 控件
    private lateinit var dialogTitle: TextView
    private lateinit var closeButton: ImageButton
    private lateinit var connectionStatusText: TextView
    private lateinit var remoteAudioVideoSwitch: SwitchCompat
    private lateinit var remoteAudioOutputModeGroup: RadioGroup
    private lateinit var remoteSpeakerModeRadio: RadioButton
    private lateinit var remoteEarpieceModeRadio: RadioButton
    private lateinit var remoteVolumeSeekBar: SeekBar
    private lateinit var remoteVolumeText: TextView

    // 数据
    private lateinit var remoteReceiverConnection: RemoteReceiverConnection
    private val connectionManager = RemoteConnectionManager.getInstance()
    private val receiverManager = RemoteReceiverManager.getInstance()

    // 当前设置状态
    private var currentAudioVideoEnabled = false
    private var currentIsSpeakerMode = true
    private var currentVolume = 80

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.DialogTheme)
        
        // 获取传入的接收端连接信息（兼容不同API版本）
        remoteReceiverConnection = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arguments?.getSerializable(ARG_RECEIVER_CONNECTION, RemoteReceiverConnection::class.java)
        } else {
            @Suppress("DEPRECATION")
            arguments?.getSerializable(ARG_RECEIVER_CONNECTION) as? RemoteReceiverConnection
        } ?: throw IllegalArgumentException("RemoteReceiverConnection is required")
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_remote_receiver_settings_control, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initViews(view)
        setupClickListeners()
        updateUI()

        // 注册到管理器以接收设置同步消息
        receiverManager.registerReceiverSettingsControlDialog(remoteReceiverConnection.id, this)
        AppLog.d("【双向同步】控制面板已注册，连接ID: ${remoteReceiverConnection.id}")

        // 🔄 关键：窗口打开时立即请求当前状态进行初始同步
        requestCurrentSettingsWithDelay()
    }

    override fun onDestroy() {
        super.onDestroy()
        // 注销设置同步监听
        receiverManager.unregisterReceiverSettingsControlDialog(remoteReceiverConnection.id)
        AppLog.d("【双向同步】控制面板已注销，连接ID: ${remoteReceiverConnection.id}")
    }

    /**
     * 初始化视图控件
     */
    private fun initViews(view: View) {
        dialogTitle = view.findViewById(R.id.dialog_title)
        closeButton = view.findViewById(R.id.close_dialog_button)
        connectionStatusText = view.findViewById(R.id.connection_status_text)
        remoteAudioVideoSwitch = view.findViewById(R.id.remote_audio_video_switch)
        remoteAudioOutputModeGroup = view.findViewById(R.id.remote_audio_output_mode_group)
        remoteSpeakerModeRadio = view.findViewById(R.id.remote_speaker_mode_radio)
        remoteEarpieceModeRadio = view.findViewById(R.id.remote_earpiece_mode_radio)
        remoteVolumeSeekBar = view.findViewById(R.id.remote_volume_seekbar)
        remoteVolumeText = view.findViewById(R.id.remote_volume_text)
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        // 关闭按钮
        closeButton.setOnClickListener {
            dismiss()
        }

        // 音视频服务开关 - 实时发送
        remoteAudioVideoSwitch.setOnCheckedChangeListener { _, isChecked ->
            currentAudioVideoEnabled = isChecked
            AppLog.d("【双向同步】音视频服务开关监听器触发: $isChecked，isUpdatingFromSync: $isUpdatingFromSync")
            // 🔄 只有非同步更新时才发送消息
            if (!isUpdatingFromSync) {
                AppLog.d("【双向同步】发送音视频服务开关控制消息: $isChecked")
                sendAudioVideoToggle(isChecked)
            } else {
                AppLog.d("【双向同步】跳过发送音视频服务开关控制消息（同步更新中）")
            }
        }

        // 播放模式选择 - 实时发送
        remoteAudioOutputModeGroup.setOnCheckedChangeListener { _, checkedId ->
            currentIsSpeakerMode = checkedId == R.id.remote_speaker_mode_radio
            AppLog.d("【双向同步】播放模式监听器触发: ${if (currentIsSpeakerMode) "扬声器" else "听筒"}，isUpdatingFromSync: $isUpdatingFromSync")
            // 🔄 只有非同步更新时才发送消息
            if (!isUpdatingFromSync) {
                AppLog.d("【双向同步】发送播放模式切换控制消息: ${if (currentIsSpeakerMode) "扬声器" else "听筒"}")
                sendPlaybackModeChange(currentIsSpeakerMode)
            } else {
                AppLog.d("【双向同步】跳过发送播放模式切换控制消息（同步更新中）")
            }
        }

        // 音量调整
        remoteVolumeSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    currentVolume = progress
                    remoteVolumeText.text = "${progress}%"
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                AppLog.d("远程音量调整: $currentVolume%")
                // 🔄 只有非同步更新时才发送消息
                if (!isUpdatingFromSync) {
                    sendVolumeChange(currentVolume)
                }
            }
        })

        // 移除按钮监听器 - 改为实时发送
        // 同步设置按钮和应用设置按钮已移除
    }

    /**
     * 更新UI显示
     */
    private fun updateUI() {
        // 更新标题
        dialogTitle.text = "🎛️ 远程设置控制 - ${remoteReceiverConnection.deviceName}"
        
        // 更新连接状态
        if (remoteReceiverConnection.isConnected) {
            connectionStatusText.text = "已连接"
            connectionStatusText.setTextColor(resources.getColor(android.R.color.holo_green_dark, null))
            
            // 启用所有控件
            enableAllControls(true)
        } else {
            connectionStatusText.text = "未连接"
            connectionStatusText.setTextColor(resources.getColor(android.R.color.holo_red_dark, null))
            
            // 禁用所有控件
            enableAllControls(false)
            Toast.makeText(requireContext(), "设备未连接，控制功能不可用", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 启用/禁用所有控件
     */
    private fun enableAllControls(enabled: Boolean) {
        remoteAudioVideoSwitch.isEnabled = enabled
        remoteSpeakerModeRadio.isEnabled = enabled
        remoteEarpieceModeRadio.isEnabled = enabled
        remoteVolumeSeekBar.isEnabled = enabled

        val alpha = if (enabled) 1.0f else 0.5f
        remoteAudioVideoSwitch.alpha = alpha
        remoteAudioOutputModeGroup.alpha = alpha
        remoteVolumeSeekBar.alpha = alpha
    }

    /**
     * 🔄 延迟请求当前设置状态（避免UI初始化冲突）
     */
    private fun requestCurrentSettingsWithDelay() {
        // 延迟500ms确保UI完全初始化后再请求状态
        view?.postDelayed({
            requestCurrentSettings()
        }, 500)
    }

    /**
     * 请求当前设置状态
     */
    private fun requestCurrentSettings() {
        if (!remoteReceiverConnection.isConnected) {
            AppLog.d("设备未连接，跳过状态同步")
            return
        }

        AppLog.d("【双向同步】请求远程接收端当前设置状态")
        val client = connectionManager.getReceiverClient(remoteReceiverConnection.id)
        if (client != null) {
            // 🔧 修复：使用WebSocket连接的实际连接ID，而不是receiver.id
            val actualConnectionId = client.getActualConnectionId()
            val message = ControlMessage.createRemoteReceiverSettingsRequest(actualConnectionId)
            if (client.sendMessage(message)) {
                AppLog.d("【双向同步】设置状态请求已发送，使用连接ID: $actualConnectionId")
            } else {
                AppLog.w("【双向同步】设置状态请求发送失败")
            }
        }
    }

    /**
     * 发送音视频服务开关控制
     */
    private fun sendAudioVideoToggle(enabled: Boolean) {
        if (!remoteReceiverConnection.isConnected) {
            Toast.makeText(requireContext(), "设备未连接", Toast.LENGTH_SHORT).show()
            return
        }

        val client = connectionManager.getReceiverClient(remoteReceiverConnection.id)
        if (client == null) {
            Toast.makeText(requireContext(), "连接客户端不可用", Toast.LENGTH_SHORT).show()
            return
        }

        // 🔧 修复：使用WebSocket连接的实际连接ID，而不是receiver.id
        val actualConnectionId = client.getActualConnectionId()
        val message = ControlMessage.createRemoteReceiverAudioVideoToggle(actualConnectionId, enabled)
        if (client.sendMessage(message)) {
            AppLog.d("音视频服务开关控制消息发送成功: $enabled，使用连接ID: $actualConnectionId")
        } else {
            Toast.makeText(requireContext(), "音视频服务开关控制失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 发送播放模式切换控制
     */
    private fun sendPlaybackModeChange(isSpeakerMode: Boolean) {
        if (!remoteReceiverConnection.isConnected) {
            Toast.makeText(requireContext(), "设备未连接", Toast.LENGTH_SHORT).show()
            return
        }

        val client = connectionManager.getReceiverClient(remoteReceiverConnection.id)
        if (client == null) {
            Toast.makeText(requireContext(), "连接客户端不可用", Toast.LENGTH_SHORT).show()
            return
        }

        // 🔧 修复：使用WebSocket连接的实际连接ID，而不是receiver.id
        val actualConnectionId = client.getActualConnectionId()
        val message = ControlMessage.createRemoteReceiverPlaybackModeChange(actualConnectionId, isSpeakerMode)
        if (client.sendMessage(message)) {
            AppLog.d("播放模式切换控制消息发送成功: ${if (isSpeakerMode) "扬声器" else "听筒"}，使用连接ID: $actualConnectionId")
        } else {
            Toast.makeText(requireContext(), "播放模式切换控制失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 发送音量调整控制
     */
    private fun sendVolumeChange(volume: Int) {
        if (!remoteReceiverConnection.isConnected) {
            Toast.makeText(requireContext(), "设备未连接", Toast.LENGTH_SHORT).show()
            return
        }

        val client = connectionManager.getReceiverClient(remoteReceiverConnection.id)
        if (client == null) {
            Toast.makeText(requireContext(), "连接客户端不可用", Toast.LENGTH_SHORT).show()
            return
        }

        // 🔧 修复：使用WebSocket连接的实际连接ID，而不是receiver.id
        val actualConnectionId = client.getActualConnectionId()
        val message = ControlMessage.createRemoteReceiverVolumeChange(actualConnectionId, volume)
        if (client.sendMessage(message)) {
            AppLog.d("音量调整控制消息发送成功: $volume%，使用连接ID: $actualConnectionId")
        } else {
            Toast.makeText(requireContext(), "音量调整控制失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 处理接收到的设置同步消息
     */
    fun onSettingsSyncReceived(settings: Map<String, Any>) {
        AppLog.d("【双向同步】控制面板收到设置同步消息: $settings")

        activity?.runOnUiThread {
            val source = settings["source"] as? String ?: "unknown"
            val action = settings["action"] as? String ?: "unknown"
            AppLog.d("【双向同步】处理设置同步消息，来源: $source，动作: $action")

            // 🔄 关键：暂时禁用监听器，避免反向同步时触发重复发送
            disableListenersTemporarily {
                updateUIFromSettings(settings)
            }

            if (action == "settings_request") {
                AppLog.d("【双向同步】初始状态同步完成")
                Toast.makeText(requireContext(), "🔄 初始状态已同步", Toast.LENGTH_SHORT).show()
            } else {
                val message = when (source) {
                    "local_operation" -> "🔄 接收端本地操作已同步"
                    "remote_operation" -> "✅ 远程操作已确认"
                    else -> "🔄 设置已同步"
                }
                Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 🔄 从设置数据更新UI（不触发监听器）
     */
    private fun updateUIFromSettings(settings: Map<String, Any>) {
        AppLog.d("【双向同步】开始更新UI，isUpdatingFromSync: $isUpdatingFromSync")

        // 更新音视频服务状态
        settings["audio_video_enabled"]?.let { enabled ->
            if (enabled is Boolean) {
                currentAudioVideoEnabled = enabled
                AppLog.d("【双向同步】准备更新音视频服务开关: $enabled，当前isUpdatingFromSync: $isUpdatingFromSync")

                // 🔧 修复：直接设置状态，isUpdatingFromSync标识会阻止监听器发送消息
                remoteAudioVideoSwitch.isChecked = enabled

                AppLog.d("【双向同步】音视频服务状态更新完成: $enabled")
            }
        }

        // 更新播放模式
        settings["is_speaker_mode"]?.let { isSpeaker ->
            if (isSpeaker is Boolean) {
                currentIsSpeakerMode = isSpeaker
                AppLog.d("【双向同步】准备更新播放模式: ${if (isSpeaker) "扬声器" else "听筒"}，当前isUpdatingFromSync: $isUpdatingFromSync")

                // 🔧 修复：直接设置状态，isUpdatingFromSync标识会阻止监听器发送消息
                if (isSpeaker) {
                    remoteAudioOutputModeGroup.check(R.id.remote_speaker_mode_radio)
                } else {
                    remoteAudioOutputModeGroup.check(R.id.remote_earpiece_mode_radio)
                }

                AppLog.d("【双向同步】播放模式更新完成: ${if (isSpeaker) "扬声器" else "听筒"}")
            }
        }

        // 更新音量
        settings["volume"]?.let { volume ->
            when (volume) {
                is Int -> {
                    currentVolume = volume
                    remoteVolumeSeekBar.progress = volume
                    remoteVolumeText.text = "${volume}%"
                    AppLog.d("【双向同步】更新音量: $volume%")
                }
                is Double -> {
                    currentVolume = volume.toInt()
                    remoteVolumeSeekBar.progress = currentVolume
                    remoteVolumeText.text = "${currentVolume}%"
                    AppLog.d("【双向同步】更新音量: $currentVolume%")
                }
            }
        }

        AppLog.d("【双向同步】UI更新完成")
    }

    // 🔄 标识是否正在进行反向同步更新（避免重复发送）
    private var isUpdatingFromSync = false

    /**
     * 🔄 暂时禁用监听器执行操作（避免反向同步时重复发送）
     */
    private fun disableListenersTemporarily(action: () -> Unit) {
        // 设置标识，避免监听器触发时重复发送
        isUpdatingFromSync = true

        try {
            // 执行UI更新操作
            action()
        } finally {
            // 恢复标识
            isUpdatingFromSync = false
        }
    }
}
