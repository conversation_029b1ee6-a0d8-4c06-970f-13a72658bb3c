package com.example.castapp.ui.windowsettings

import android.content.Context
import android.graphics.Rect
import android.graphics.RectF
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ViewConfiguration
import android.widget.Button
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import com.example.castapp.R
import com.example.castapp.ui.windowsettings.interfaces.CropStateListener
import com.example.castapp.ui.view.CropOverlayView
import com.example.castapp.utils.AppLog
import kotlin.math.abs

/**
 * 裁剪功能管理器 - 统一版本
 * 🎯 合并了UnifiedCropManager的功能，提供完整的裁剪管理能力
 *
 * 核心功能：
 * 1. 裁剪UI交互管理（CropOverlayView、控制按钮）
 * 2. 统一裁剪逻辑处理（clipBounds机制）
 * 3. 裁剪状态管理和监听
 * 4. 圆角和边框同步回调
 * 5. 设备信息显示和按钮位置保存
 *
 * 设计理念：
 * - 使用Android原生的clipBounds机制，避免手动计算
 * - 统一处理所有窗口类型（投屏/视频/摄像头/图片）
 * - 消除位置跳动和复杂的偏移计算
 * - 简化状态管理，只保留必要的裁剪状态
 */
class CropManager(
    private val context: Context,
    private val container: FrameLayout,
    private val connectionId: String
) {

    // 窗口尺寸信息（用于统一裁剪逻辑）
    private var windowWidth: Int = 0
    private var windowHeight: Int = 0

    // 🎯 统一裁剪状态（合并原有状态和UnifiedCropManager状态）
    private var cropRectRatio: RectF? = null
    private var isCroppedWindow = false
    private var isCropping = false

    // 🎯 保存进入裁剪模式时的原始状态（用于取消操作）
    private var originalCropRectRatio: RectF? = null
    private var originalIsCroppedWindow = false

    // 裁剪UI组件
    private var cropOverlay: CropOverlayView? = null
    private var cropControlButtons: LinearLayout? = null

    // 设备信息（用于显示）
    private var deviceName: String? = null
    private var ipAddress: String = ""
    private var port: Int = 0

    // 按钮位置保存（绝对屏幕坐标）
    private var buttonAbsoluteX = -1f
    private var buttonAbsoluteY = -1f

    // 🎯 统一监听器（合并原有监听器和UnifiedCropManager监听器）
    private var cropStateListener: CropStateListener? = null
    private var cropModeChangeCallback: ((Boolean) -> Unit)? = null

    // 🎯 新增：统一裁剪状态监听器（来自UnifiedCropManager）
    private var unifiedCropStateListener: ((RectF?) -> Unit)? = null

    // 🎯 新增：圆角和边框同步回调（来自UnifiedCropManager）
    private var cornerRadiusUpdateCallback: (() -> Unit)? = null

    /**
     * 🎯 新增：设置窗口尺寸（用于统一裁剪逻辑）
     */
    fun setWindowSize(width: Int, height: Int) {
        this.windowWidth = width
        this.windowHeight = height
        AppLog.d("🎯 [统一裁剪] 设置窗口尺寸: $connectionId, ${width}x${height}")
    }

    /**
     * 设置裁剪状态监听器
     */
    fun setCropStateListener(listener: CropStateListener?) {
        this.cropStateListener = listener
    }

    /**
     * 🎯 新增：设置统一裁剪状态监听器（来自UnifiedCropManager）
     */
    fun setCropStateListener(listener: (RectF?) -> Unit) {
        this.unifiedCropStateListener = listener
    }

    /**
     * 🎯 新增：设置圆角和边框更新回调（来自UnifiedCropManager）
     */
    fun setCornerRadiusUpdateCallback(callback: () -> Unit) {
        this.cornerRadiusUpdateCallback = callback
    }

    /**
     * 设置裁剪模式变化回调
     */
    fun setCropModeChangeCallback(callback: (Boolean) -> Unit) {
        this.cropModeChangeCallback = callback
    }
    
    /**
     * 设置设备信息（用于裁剪模式显示）
     */
    fun setDeviceInfo(deviceName: String?, ipAddress: String, port: Int) {
        this.deviceName = deviceName
        this.ipAddress = ipAddress
        this.port = port
        AppLog.d("🐾 CropManager设置设备信息: $connectionId, 设备名称=$deviceName, 地址=${ipAddress}:${port}")
    }
    
    /**
     * 获取设备显示信息（与调控面板格式一致）
     */
    private fun getDeviceDisplayInfo(): String {
        val displayDeviceName = if (!deviceName.isNullOrBlank()) deviceName else "未知设备"
        val shortId = connectionId.takeLast(8)
        return "$displayDeviceName($shortId)"
    }
    
    /**
     * 开始裁剪模式
     */
    fun startCropMode() {
        if (isCropping) {
            AppLog.d("🎯 已经在裁剪模式中: $connectionId")
            return
        }

        // 🎯 关键修复：保存进入裁剪模式时的原始状态
        originalCropRectRatio = cropRectRatio?.let { RectF(it) } // 深拷贝
        originalIsCroppedWindow = isCroppedWindow
        AppLog.d("🎯 保存原始裁剪状态: cropRectRatio=$originalCropRectRatio, isCroppedWindow=$originalIsCroppedWindow")

        // 如果当前有裁剪，临时清除裁剪以显示完整画面进行重新裁剪
        if (isCroppedWindow) {
            // 🎯 修复：通过applyCrop(null)临时清除裁剪，但立即恢复cropRectRatio状态
            val savedCropRatio = cropRectRatio // 保存原始裁剪比例
            applyCrop(null) // 清除实际裁剪效果
            cropRectRatio = savedCropRatio // 恢复裁剪比例状态，用于后续恢复边框
            AppLog.d("🎯 临时清除裁剪显示，恢复完整画面: $connectionId")
        }
        
        // 创建裁剪覆盖层
        cropOverlay = CropOverlayView(context).apply {
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )

            // 🎯 修复：如果有保存的裁剪区域，显示它作为参考，用户可以基于此进行调整
            // 如果没有保存的裁剪区域，则初始化为全屏
            originalCropRectRatio?.let { ratio ->
                setCropRectRatio(ratio)
                AppLog.d("🎯 恢复之前的裁剪区域作为参考: $connectionId, 比例: $ratio")
            } ?: run {
                AppLog.d("🎯 无之前的裁剪区域，初始化为全屏: $connectionId")
            }

            // 设置裁剪变化监听器
            setCropChangeListener(object : CropOverlayView.CropChangeListener {
                override fun onCropChanged(cropRect: RectF) {
                    // 保存裁剪区域比例
                    cropRectRatio = getCropRectRatio()
                    AppLog.d("🎯 裁剪区域变化: $connectionId, 比例: $cropRectRatio")
                }
            })
        }
        
        // 添加到容器
        container.addView(cropOverlay)
        cropOverlay?.bringToFront()
        
        // 创建控制按钮（在父容器中）
        createCropControlButtonsInParent()
        
        isCropping = true
        
        // 通知裁剪模式变化
        cropStateListener?.onCropModeChanged(true)
        cropModeChangeCallback?.invoke(true)
        
        AppLog.d("🎯 开始裁剪模式: $connectionId")
    }

    /**
     * 结束裁剪模式
     * @param isCancel 是否为取消操作，取消时不保存当前裁剪区域且不应用智能恢复
     */
    fun endCropMode(isCancel: Boolean = false) {
        if (!isCropping) {
            return
        }

        // 只有非取消操作才保存当前裁剪区域
        if (!isCancel) {
            cropOverlay?.let { overlay ->
                cropRectRatio = overlay.getCropRectRatio()
            }
        }

        // 移除覆盖层
        cropOverlay?.let { overlay ->
            container.removeView(overlay)
        }
        cropOverlay = null

        // 移除控制按钮
        removeCropControlButtons()

        // 🎯 修复：使用直接裁剪方法避免循环调用
        if (!isCancel && cropRectRatio != null) {
            isCroppedWindow = true
            applyDirectCrop(cropRectRatio) // 🎯 使用直接裁剪，避免循环调用
            AppLog.d("🎯 恢复裁剪状态: $connectionId, 裁剪比例: $cropRectRatio")
        } else if (!isCancel) {
            AppLog.d("🎯 无裁剪区域，保持未裁剪状态: $connectionId")
        } else {
            // 🎯 关键修复：取消操作时，恢复到进入裁剪模式时的原始状态
            cropRectRatio = originalCropRectRatio?.let { RectF(it) } // 深拷贝恢复
            isCroppedWindow = originalIsCroppedWindow
            // 🎯 使用直接裁剪，避免循环调用
            applyDirectCrop(cropRectRatio)
            AppLog.d("🎯 取消操作，恢复原始状态: $connectionId, 裁剪比例: $cropRectRatio, 裁剪状态: $isCroppedWindow")
        }

        isCropping = false

        // 通知裁剪模式变化
        cropStateListener?.onCropModeChanged(false)
        cropModeChangeCallback?.invoke(false)

        AppLog.d("结束裁剪模式: $connectionId, 是否取消: $isCancel, 保存的裁剪比例: $cropRectRatio")
    }

    /**
     * 🎯 新增：统一裁剪应用方法（来自UnifiedCropManager）
     * 核心简化：使用clipBounds统一处理，消除特殊情况
     *
     * @param cropRatio 裁剪区域比例，null表示移除裁剪
     */
    fun applyCrop(cropRatio: RectF?) {
        // 检查窗口尺寸是否已设置
        if (windowWidth <= 0 || windowHeight <= 0) {
            AppLog.w("🎯 [统一裁剪] 窗口尺寸未设置，无法应用裁剪: $connectionId")
            return
        }

        cropRectRatio = cropRatio
        isCroppedWindow = cropRatio != null

        if (cropRatio != null) {
            // 🎯 统一方案：直接设置容器的clipBounds
            val clipBounds = Rect(
                (cropRatio.left * windowWidth).toInt(),
                (cropRatio.top * windowHeight).toInt(),
                (cropRatio.right * windowWidth).toInt(),
                (cropRatio.bottom * windowHeight).toInt()
            )

            container.clipBounds = clipBounds

            AppLog.d("🎯 [统一裁剪] 应用裁剪: $connectionId")
            AppLog.d("🎯   裁剪比例: left=${cropRatio.left}, top=${cropRatio.top}, right=${cropRatio.right}, bottom=${cropRatio.bottom}")
            AppLog.d("🎯   裁剪区域: $clipBounds")

        } else {
            // 清除裁剪
            container.clipBounds = null
            AppLog.d("🎯 [统一裁剪] 移除裁剪: $connectionId")
        }

        // 🎯 修复：只通知统一裁剪状态监听器，避免循环调用
        // cropStateListener 是UI交互层的监听器，会触发循环调用，所以不在这里调用
        unifiedCropStateListener?.invoke(cropRatio)

        // 🎯 关键修复：裁剪状态变化时，通知更新圆角和边框
        AppLog.d("🎯 [边框恢复] applyCrop调用cornerRadiusUpdateCallback, callback是否为null: ${cornerRadiusUpdateCallback == null}")
        cornerRadiusUpdateCallback?.invoke()
    }

    /**
     * 应用裁剪设置（保持原有UI交互方法）
     */
    fun applyCrop() {
        cropOverlay?.let { overlay ->
            val newCropRatio = overlay.getCropRectRatio()
            applyCrop(newCropRatio) // 使用统一裁剪方法
            AppLog.d("🎯 应用裁剪设置: $connectionId, 裁剪比例: $newCropRatio")
        }
    }

    /**
     * 重置裁剪区域（仅重置UI显示，不影响保存的裁剪状态）
     */
    fun resetCrop() {
        // 🎯 修复：重置操作只影响UI显示，不修改裁剪状态
        // 这样取消操作就能正确恢复到进入裁剪模式时的状态
        // 🎯 关键：不触发回调，避免修改cropRectRatio
        cropOverlay?.resetCrop(notifyChange = false)
        AppLog.d("🎯 重置裁剪区域UI显示: $connectionId, 保持裁剪状态: cropRectRatio=$cropRectRatio, isCroppedWindow=$isCroppedWindow")
    }

    /**
     * 获取当前是否在裁剪模式
     */
    fun isCroppingMode(): Boolean = isCropping

    /**
     * 获取当前是否为裁剪窗口
     */
    fun isCroppedWindow(): Boolean = isCroppedWindow

    /**
     * 获取当前裁剪区域比例
     */
    fun getCropRectRatio(): RectF? = cropRectRatio

    /**
     * 设置裁剪区域比例（用于布局恢复）
     * 🎯 修复：避免循环调用，直接设置状态而不触发监听器
     */
    fun setCropRectRatio(rectRatio: RectF?) {
        // 🎯 关键修复：直接应用裁剪而不触发监听器，避免循环调用
        applyDirectCrop(rectRatio)
        AppLog.d("【布局恢复】设置裁剪区域: $connectionId, 区域: $rectRatio, 裁剪状态: $isCroppedWindow")
    }

    /**
     * 🎯 新增：直接应用裁剪（不触发监听器，避免循环调用）
     * 用于内部状态设置，如布局恢复等场景
     */
    private fun applyDirectCrop(cropRatio: RectF?) {
        // 检查窗口尺寸是否已设置
        if (windowWidth <= 0 || windowHeight <= 0) {
            AppLog.w("🎯 [直接裁剪] 窗口尺寸未设置，无法应用裁剪: $connectionId")
            return
        }

        cropRectRatio = cropRatio
        isCroppedWindow = cropRatio != null

        if (cropRatio != null) {
            // 🎯 统一方案：直接设置容器的clipBounds
            val clipBounds = Rect(
                (cropRatio.left * windowWidth).toInt(),
                (cropRatio.top * windowHeight).toInt(),
                (cropRatio.right * windowWidth).toInt(),
                (cropRatio.bottom * windowHeight).toInt()
            )

            container.clipBounds = clipBounds

            AppLog.d("🎯 [直接裁剪] 应用裁剪: $connectionId")
            AppLog.d("🎯   裁剪比例: left=${cropRatio.left}, top=${cropRatio.top}, right=${cropRatio.right}, bottom=${cropRatio.bottom}")
            AppLog.d("🎯   裁剪区域: $clipBounds")

        } else {
            // 清除裁剪
            container.clipBounds = null
            AppLog.d("🎯 [直接裁剪] 移除裁剪: $connectionId")
        }

        // 🎯 关键修复：裁剪状态变化时，通知更新圆角和边框（但不触发状态监听器）
        AppLog.d("🎯 [边框恢复] applyDirectCrop调用cornerRadiusUpdateCallback, callback是否为null: ${cornerRadiusUpdateCallback == null}")
        cornerRadiusUpdateCallback?.invoke()
    }

    /**
     * 创建裁剪控制按钮（使用XML布局）
     */
    private fun createCropControlButtonsInParent() {
        // 获取父容器
        val parentContainer = container.parent as? FrameLayout ?: return

        // 加载保存的按钮位置
        loadButtonPosition()

        // 计算按钮组位置
        val screenWidth = context.resources.displayMetrics.widthPixels
        val screenHeight = context.resources.displayMetrics.heightPixels
        val estimatedButtonWidth = dpToPx(120) // 估算按钮组宽度
        val estimatedButtonHeight = dpToPx(60) // 估算按钮组高度

        // 如果有保存的位置，使用保存的位置；否则使用屏幕底部中央默认位置
        val buttonX = if (buttonAbsoluteX >= 0) {
            buttonAbsoluteX.coerceIn(0f, (screenWidth - estimatedButtonWidth).toFloat())
        } else {
            (screenWidth - estimatedButtonWidth) / 2f  // 屏幕水平居中
        }

        val buttonY = if (buttonAbsoluteY >= 0) {
            buttonAbsoluteY.coerceIn(0f, (screenHeight - estimatedButtonHeight).toFloat())
        } else {
            (screenHeight - estimatedButtonHeight - dpToPx(50)).toFloat()  // 屏幕底部向上50dp
        }

        // 使用XML布局创建裁剪控制按钮组
        val layoutInflater = LayoutInflater.from(context)
        cropControlButtons = layoutInflater.inflate(
            R.layout.crop_control_buttons,
            container,
            false
        ) as LinearLayout

        // 设置按钮组位置和基本属性
        cropControlButtons?.apply {
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT,
                FrameLayout.LayoutParams.WRAP_CONTENT
            )

            // 使用计算好的绝对位置
            x = buttonX
            y = buttonY
            elevation = 1000f // 确保显示在最上层
        }

        // 设置拖动功能
        setupCropControlButtonsDrag()

        // 设置设备信息显示
        setupDeviceInfoDisplay()

        // 设置按钮点击事件
        setupCropControlButtonsEvents()

        // 添加按钮容器到父容器
        cropControlButtons?.let { buttons ->
            parentContainer.addView(buttons)
            buttons.bringToFront()
            AppLog.d("🐾 XML布局裁剪控制按钮组已添加到父容器")
        }
    }

    /**
     * 设置设备信息显示
     */
    private fun setupDeviceInfoDisplay() {
        val deviceInfoTextView = cropControlButtons?.findViewById<TextView>(R.id.tv_device_info)
        deviceInfoTextView?.text = getDeviceDisplayInfo()
        AppLog.d("🐾 设置设备信息显示: ${getDeviceDisplayInfo()}")
    }

    /**
     * 设置裁剪控制按钮拖动功能
     */
    private fun setupCropControlButtonsDrag() {
        cropControlButtons?.let { buttonsLayout ->
            var isDragging = false
            var lastTouchX = 0f
            var lastTouchY = 0f
            var initialTouchX = 0f
            var initialTouchY = 0f
            val touchSlop = ViewConfiguration.get(context).scaledTouchSlop

            buttonsLayout.setOnTouchListener { _, event ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        initialTouchX = event.rawX
                        initialTouchY = event.rawY
                        lastTouchX = event.rawX
                        lastTouchY = event.rawY
                        isDragging = false
                        true
                    }
                    MotionEvent.ACTION_MOVE -> {
                        val deltaX = abs(event.rawX - initialTouchX)
                        val deltaY = abs(event.rawY - initialTouchY)

                        // 如果移动距离超过触摸阈值，开始拖动
                        if (!isDragging && (deltaX > touchSlop || deltaY > touchSlop)) {
                            isDragging = true
                        }

                        if (isDragging) {
                            val moveX = event.rawX - lastTouchX
                            val moveY = event.rawY - lastTouchY

                            buttonsLayout.x += moveX
                            buttonsLayout.y += moveY

                            lastTouchX = event.rawX
                            lastTouchY = event.rawY
                        }
                        true
                    }
                    MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                        if (isDragging) {
                            // 拖动结束，保存当前绝对位置
                            buttonAbsoluteX = buttonsLayout.x
                            buttonAbsoluteY = buttonsLayout.y
                            saveButtonPosition()
                            AppLog.d("🎯 按钮组拖动结束，保存位置: (${buttonAbsoluteX}, ${buttonAbsoluteY})")
                            isDragging = false
                        }
                        true
                    }
                    else -> false
                }
            }
        }
    }

    /**
     * 设置裁剪控制按钮事件
     */
    private fun setupCropControlButtonsEvents() {
        cropControlButtons?.let { buttonsLayout ->
            // 重置按钮
            val resetButton = buttonsLayout.findViewById<Button>(R.id.btn_reset)
            resetButton?.setOnClickListener {
                resetCrop()
                AppLog.d("🐾 点击重置按钮")
            }

            // 取消按钮
            val cancelButton = buttonsLayout.findViewById<Button>(R.id.btn_cancel)
            cancelButton?.setOnClickListener {
                // 取消裁剪，完全恢复到进入裁剪模式之前的状态
                endCropMode(isCancel = true)
                AppLog.d("🐾 点击取消按钮")
            }

            // 应用按钮
            val applyButton = buttonsLayout.findViewById<Button>(R.id.btn_apply)
            applyButton?.setOnClickListener {
                // 应用裁剪设置
                applyCrop()
                endCropMode()
                AppLog.d("🐾 点击应用按钮")
            }
        }
    }

    /**
     * 移除裁剪控制按钮
     */
    private fun removeCropControlButtons() {
        cropControlButtons?.let { buttons ->
            // 从父容器中移除按钮
            val parentContainer = container.parent as? FrameLayout
            parentContainer?.removeView(buttons)
        }
        cropControlButtons = null
    }

    /**
     * 保存按钮组位置到SharedPreferences（绝对坐标）
     */
    private fun saveButtonPosition() {
        if (connectionId.isEmpty()) return

        val sharedPrefs = context.getSharedPreferences("crop_button_positions", Context.MODE_PRIVATE)
        sharedPrefs.edit().apply {
            putFloat("${connectionId}_absolute_x", buttonAbsoluteX)
            putFloat("${connectionId}_absolute_y", buttonAbsoluteY)
            apply()
        }
        AppLog.d("🎯 保存按钮组位置: $connectionId, 绝对坐标=(${buttonAbsoluteX}, ${buttonAbsoluteY})")
    }

    /**
     * 从SharedPreferences加载按钮组位置（绝对坐标）
     */
    private fun loadButtonPosition() {
        if (connectionId.isEmpty()) return

        val sharedPrefs = context.getSharedPreferences("crop_button_positions", Context.MODE_PRIVATE)
        buttonAbsoluteX = sharedPrefs.getFloat("${connectionId}_absolute_x", -1f)
        buttonAbsoluteY = sharedPrefs.getFloat("${connectionId}_absolute_y", -1f)
        AppLog.d("🎯 加载按钮组位置: $connectionId, 绝对坐标=(${buttonAbsoluteX}, ${buttonAbsoluteY})")
    }

    /**
     * dp转px工具方法
     */
    private fun dpToPx(dp: Int): Int {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp.toFloat(),
            context.resources.displayMetrics
        ).toInt()
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        // 结束裁剪模式（如果正在裁剪）
        if (isCropping) {
            endCropMode(isCancel = true)
        }

        // 清理引用
        cropOverlay = null
        cropControlButtons = null
        cropStateListener = null
        cropModeChangeCallback = null

        AppLog.d("CropManager清理完成: $connectionId")
    }
}
