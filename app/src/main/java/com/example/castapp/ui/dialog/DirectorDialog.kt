package com.example.castapp.ui.dialog

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageButton
import android.widget.TextView
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.database.entity.WindowLayoutEntity
import com.example.castapp.database.entity.WindowLayoutItemEntity
import com.example.castapp.manager.LayoutManager
import com.example.castapp.manager.WindowSettingsManager
import com.example.castapp.model.CastWindowInfo
import com.example.castapp.utils.ToastUtils
import com.example.castapp.ui.adapter.LayoutDetailAdapter
import com.example.castapp.ui.adapter.LayoutListAdapter
import com.example.castapp.ui.helper.LayoutItemTouchHelperCallback
import com.example.castapp.utils.AppLog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

/**
 * 导播台BottomSheet对话框
 * 用于管理和恢复已保存的窗口布局
 */
class DirectorDialog(
    private val windowSettingsManager: WindowSettingsManager,
    private val onRestoreLayout: (List<WindowLayoutItemEntity>) -> Unit
) : BottomSheetDialogFragment() {

    private val layoutManager = LayoutManager.getInstance()

    // UI组件
    private lateinit var layoutListRecyclerView: RecyclerView
    private lateinit var detailListRecyclerView: RecyclerView
    private lateinit var emptyView: TextView
    private lateinit var selectionButton: Button
    private lateinit var batchDeleteButton: Button
    private lateinit var saveButton: Button
    private lateinit var editButton: Button
    private lateinit var applyButton: Button
    private lateinit var closeButton: ImageButton

    // 适配器
    private lateinit var layoutListAdapter: LayoutListAdapter
    private lateinit var layoutDetailAdapter: LayoutDetailAdapter

    // 拖拽排序相关
    private lateinit var itemTouchHelper: ItemTouchHelper

    // 当前选中的布局
    private var selectedLayout: WindowLayoutEntity? = null

    // 🐾 当前应用的布局（从数据库获取）
    private var appliedLayout: WindowLayoutEntity? = null

    // 对话框关闭回调
    var onDialogDismissed: (() -> Unit)? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_director, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        try {
            // 初始化视图
            initViews(view)
            setupRecyclerViews()
            setupClickListeners()
            observeLayoutData()

            // 🐾 设置按钮初始状态
            initializeButtonStates()

            // 🐾 加载当前应用状态
            loadCurrentAppliedStatus()

            AppLog.d("导播台BottomSheet对话框初始化完成")

        } catch (e: Exception) {
            AppLog.e("初始化导播台BottomSheet对话框失败", e)
            context?.let { ToastUtils.showToast(it, "显示导播台失败") }
        }
    }
    
    /**
     * 初始化视图组件
     */
    private fun initViews(view: View) {
        layoutListRecyclerView = view.findViewById(R.id.layout_list)
        detailListRecyclerView = view.findViewById(R.id.detail_list)
        emptyView = view.findViewById(R.id.empty_view)
        selectionButton = view.findViewById(R.id.selection_button)
        batchDeleteButton = view.findViewById(R.id.batch_delete_button)
        saveButton = view.findViewById(R.id.save_button)
        editButton = view.findViewById(R.id.edit_button)
        applyButton = view.findViewById(R.id.apply_button)
        closeButton = view.findViewById(R.id.btn_close)
    }

    /**
     * 🐾 初始化按钮状态
     */
    private fun initializeButtonStates() {
        // 批量选择相关按钮初始状态
        selectionButton.isEnabled = true
        selectionButton.text = "选择"
        batchDeleteButton.isEnabled = false

        // 其他按钮初始状态（无选中时禁用）
        saveButton.isEnabled = false
        editButton.isEnabled = false
        applyButton.isEnabled = false

        AppLog.d("按钮初始状态设置完成")
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerViews() {
        // 设置布局列表
        layoutListAdapter = LayoutListAdapter()
        layoutListRecyclerView.layoutManager = LinearLayoutManager(context)
        layoutListRecyclerView.adapter = layoutListAdapter

        // 设置布局详情列表
        layoutDetailAdapter = LayoutDetailAdapter()
        detailListRecyclerView.layoutManager = LinearLayoutManager(context)
        detailListRecyclerView.adapter = layoutDetailAdapter

        // 设置拖拽排序功能
        setupDragAndDrop()

        // 设置布局选择监听器
        layoutListAdapter.setOnLayoutSelectedListener(object : LayoutListAdapter.OnLayoutSelectedListener {
            override fun onLayoutSelected(layout: WindowLayoutEntity) {
                selectedLayout = layout
                loadLayoutDetails(layout.id)
                updateButtonStates(true)
                AppLog.d("选中布局: ${layout.layoutName}")
            }
        })

        // 设置拖拽完成监听器
        layoutListAdapter.setOnItemMoveListener(object : LayoutListAdapter.OnItemMoveListener {
            override fun onItemMoved(fromPosition: Int, toPosition: Int, layouts: List<WindowLayoutEntity>) {
                handleLayoutOrderChanged(layouts)

                // 拖拽完成后，如果当前有选中项，需要重新设置选中状态
                selectedLayout?.let { layout ->
                    layoutListAdapter.setSelectedLayoutId(layout.id)
                    AppLog.d("拖拽完成后重新设置选中状态: ${layout.layoutName}")
                }
            }
        })

        // 🐾 设置选择状态变化监听器
        layoutListAdapter.setOnSelectionChangedListener(object : LayoutListAdapter.OnSelectionChangedListener {
            override fun onSelectionChanged(selectedCount: Int, totalCount: Int) {
                updateBatchDeleteButtonState(selectedCount)
                AppLog.d("选择状态变化: 已选中${selectedCount}个，共${totalCount}个")
            }
        })
    }
    
    /**
     * 设置拖拽排序功能
     */
    private fun setupDragAndDrop() {
        val callback = LayoutItemTouchHelperCallback(layoutListAdapter)
        itemTouchHelper = ItemTouchHelper(callback)
        itemTouchHelper.attachToRecyclerView(layoutListRecyclerView)

        AppLog.d("拖拽排序功能已启用")
    }

    /**
     * 处理布局顺序改变
     */
    private fun handleLayoutOrderChanged(layouts: List<WindowLayoutEntity>) {
        AppLog.d("开始保存布局排序，共${layouts.size}个布局")

        // 更新布局的排序顺序（静默保存，不显示Toast避免干扰用户体验）
        layoutManager.updateLayoutsOrder(layouts) { success, message ->
            if (success) {
                AppLog.d("布局排序保存成功")
                // 静默保存成功，不显示Toast
            } else {
                AppLog.w("布局排序保存失败: $message")
                context?.let { ToastUtils.showToast(it, "排序保存失败: $message") }
            }
        }
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        closeButton.setOnClickListener {
            AppLog.d("用户关闭导播台BottomSheet对话框")
            dismiss()
        }

        // 🐾 选择按钮点击事件
        selectionButton.setOnClickListener {
            handleSelectionModeToggle()
        }

        // 🐾 批量删除按钮点击事件
        batchDeleteButton.setOnClickListener {
            handleBatchDelete()
        }

        saveButton.setOnClickListener {
            handleSaveLayout()
        }

        editButton.setOnClickListener {
            handleEditLayout()
        }

        applyButton.setOnClickListener {
            handleRestoreLayout()
        }
    }

    /**
     * 🐾 加载当前应用状态（新增）
     */
    private fun loadCurrentAppliedStatus() {
        layoutManager.getCurrentAppliedLayout { layout ->
            appliedLayout = layout
            val appliedLayoutId = layout?.id ?: -1L
            layoutListAdapter.setAppliedLayoutId(appliedLayoutId)

            AppLog.d("加载应用状态: appliedLayoutId=$appliedLayoutId, layoutName=${layout?.layoutName}")

            // 如果当前选中的布局就是应用的布局，更新按钮状态
            if (selectedLayout?.id == appliedLayoutId) {
                updateButtonStates(true)
            }
        }
    }

    /**
     * 观察布局数据变化
     */
    private fun observeLayoutData() {
        layoutManager.getAllLayouts()?.observe(viewLifecycleOwner) { layouts ->
            AppLog.d("布局数据更新，共${layouts.size}个布局")
            layoutListAdapter.submitList(layouts)

            // 如果没有布局，清空详情显示
            if (layouts.isEmpty()) {
                selectedLayout = null
                layoutDetailAdapter.submitList(emptyList())
                updateButtonStates(false)
                showEmptyDetailView(true)
            } else {
                // 🐾 关键修复：如果当前有选中的布局，需要从新数据中找到对应的更新后对象
                selectedLayout?.let { currentSelected ->
                    val updatedLayout = layouts.find { it.id == currentSelected.id }
                    if (updatedLayout != null) {
                        // 更新selectedLayout为最新的数据对象
                        selectedLayout = updatedLayout
                        AppLog.d("同步更新选中布局对象: ${currentSelected.layoutName} -> ${updatedLayout.layoutName}")
                    } else {
                        // 如果选中的布局已被删除，清空选中状态
                        AppLog.w("选中的布局已被删除，清空选中状态: ${currentSelected.layoutName}")
                        selectedLayout = null
                        layoutListAdapter.setSelectedLayoutId(-1)
                        layoutDetailAdapter.submitList(emptyList())
                        updateButtonStates(false)
                        showEmptyDetailView(true)
                    }
                }
            }
        }
    }
    
    /**
     * 加载布局详情
     */
    private fun loadLayoutDetails(layoutId: Long) {
        layoutManager.getLayoutDetails(layoutId) { success, items, message ->
            if (success && items != null) {
                layoutDetailAdapter.submitList(items)
                showEmptyDetailView(false)
                AppLog.d("加载布局详情成功，共${items.size}个窗口")
            } else {
                layoutDetailAdapter.submitList(emptyList())
                showEmptyDetailView(true)
                AppLog.w("加载布局详情失败: $message")
                context?.let { ToastUtils.showToast(it, "加载布局详情失败") }
            }
        }
    }
    
    /**
     * 🐾 处理应用布局或取消应用（重构版）
     */
    private fun handleRestoreLayout() {
        val layout = selectedLayout
        if (layout == null) {
            context?.let { ToastUtils.showToast(it, "请先选择要恢复的布局") }
            return
        }

        // 检查当前是否为应用状态
        if (appliedLayout?.id == layout.id) {
            // 当前是"取消应用"操作
            handleCancelApply()
        } else {
            // 当前是"应用布局"操作
            handleApplyLayout(layout)
        }
    }

    /**
     * 🐾 处理应用布局（新增）
     */
    private fun handleApplyLayout(layout: WindowLayoutEntity) {
        // 禁用按钮，防止重复点击
        applyButton.isEnabled = false

        layoutManager.getLayoutDetails(layout.id) { success, items, message ->
            applyButton.isEnabled = true

            if (success && items != null && items.isNotEmpty()) {
                AppLog.d("开始应用布局: ${layout.layoutName}")
                onRestoreLayout(items)

                // 🐾 保存应用状态到数据库
                layoutManager.setLayoutAppliedStatus(layout.id, true) { success, message ->
                    if (success) {
                        appliedLayout = layout
                        layoutListAdapter.setAppliedLayoutId(layout.id)
                        updateButtonStates(true)
                        AppLog.d("布局应用状态已保存到数据库: layoutId=${layout.id}")
                    } else {
                        AppLog.w("保存应用状态失败: $message")
                    }
                }

                context?.let { ToastUtils.showToast(it, "布局应用成功") }
            } else {
                AppLog.w("应用布局失败: $message")
                context?.let { ToastUtils.showToast(it, "应用布局失败: $message") }
            }
        }
    }

    /**
     * 🐾 处理取消应用（新增）
     */
    private fun handleCancelApply() {
        val currentAppliedLayout = appliedLayout
        if (currentAppliedLayout == null) {
            AppLog.w("没有找到当前应用的布局")
            return
        }

        AppLog.d("取消应用布局: layoutId=${currentAppliedLayout.id}, layoutName=${currentAppliedLayout.layoutName}")

        // 🐾 清除数据库中的应用状态
        layoutManager.setLayoutAppliedStatus(currentAppliedLayout.id, false) { success, message ->
            if (success) {
                appliedLayout = null
                layoutListAdapter.setAppliedLayoutId(-1)
                updateButtonStates(true)
                AppLog.d("布局应用状态已从数据库清除")
            } else {
                AppLog.w("清除应用状态失败: $message")
            }
        }

        context?.let { ToastUtils.showToast(it, "已取消应用布局") }
    }

    /**
     * 处理保存布局参数
     */
    private fun handleSaveLayout() {
        val layout = selectedLayout
        if (layout == null) {
            context?.let { ToastUtils.showToast(it, "请先选择要保存参数的布局") }
            return
        }

        // 获取当前投屏窗口信息
        val currentWindowInfoList = windowSettingsManager.getCurrentWindowInfoList()
        if (currentWindowInfoList.isEmpty()) {
            context?.let { ToastUtils.showToast(it, "当前没有投屏窗口可以保存") }
            return
        }

        AppLog.d("准备保存布局参数: ${layout.layoutName}, 当前投屏窗口数: ${currentWindowInfoList.size}")

        // 显示保存选项对话框
        context?.let { ctx ->
            val saveOptionsDialog = SaveOptionsDialog(ctx) { isUpdateMode ->
                if (isUpdateMode) {
                    // 更新已有布局参数模式
                    updateLayoutParameters(layout, currentWindowInfoList)
                } else {
                    // 清空已有布局参数并重新保存模式
                    replaceLayoutParameters(layout, currentWindowInfoList)
                }
            }
            saveOptionsDialog.show()
        }
    }

    /**
     * 更新已有布局参数
     */
    private fun updateLayoutParameters(layout: WindowLayoutEntity, currentWindowInfoList: List<CastWindowInfo>) {
        saveButton.isEnabled = false

        context?.let { ctx ->
            layoutManager.updateLayoutParameters(layout.id, currentWindowInfoList, ctx) { success, message ->
                saveButton.isEnabled = true

                if (success) {
                    AppLog.d("更新布局参数成功: ${layout.layoutName}")
                    ToastUtils.showToast(ctx, "布局参数更新成功")

                    // 刷新布局详情显示
                    loadLayoutDetails(layout.id)
                } else {
                    AppLog.w("更新布局参数失败: $message")
                    ToastUtils.showToast(ctx, "更新失败: $message")
                }
            }
        }
    }

    /**
     * 清空已有布局参数并重新保存
     */
    private fun replaceLayoutParameters(layout: WindowLayoutEntity, currentWindowInfoList: List<CastWindowInfo>) {
        saveButton.isEnabled = false

        context?.let { ctx ->
            layoutManager.replaceLayoutParameters(layout.id, currentWindowInfoList, ctx) { success, message ->
                saveButton.isEnabled = true

                if (success) {
                    AppLog.d("替换布局参数成功: ${layout.layoutName}")
                    ToastUtils.showToast(ctx, "布局参数替换成功")

                    // 刷新布局详情显示
                    loadLayoutDetails(layout.id)
                } else {
                    AppLog.w("替换布局参数失败: $message")
                    ToastUtils.showToast(ctx, "替换失败: $message")
                }
            }
        }
    }

    /**
     * 处理编辑布局
     */
    private fun handleEditLayout() {
        val layout = selectedLayout
        if (layout == null) {
            context?.let { ToastUtils.showToast(it, "请先选择要编辑的布局") }
            return
        }

        context?.let { ctx ->
            try {
                val editDialog = EditLayoutDialog(
                    context = ctx,
                    layout = layout,
                    onEditSuccess = {
                        // 编辑成功后刷新详情标题
                        AppLog.d("布局编辑成功，刷新界面")
                        // 注意：布局列表会通过LiveData自动更新
                    }
                )
                editDialog.show()

                AppLog.d("显示编辑布局对话框: ${layout.layoutName}")

            } catch (e: Exception) {
                AppLog.e("显示编辑布局对话框失败", e)
                ToastUtils.showToast(ctx, "显示编辑对话框失败")
            }
        }
    }


    
    /**
     * 🐾 更新按钮状态（重构版，支持动态文字和样式）
     */
    private fun updateButtonStates(hasSelection: Boolean) {
        // 🐾 批量选择相关按钮状态管理
        selectionButton.isEnabled = true  // 选择按钮始终可用
        // batchDeleteButton的状态由updateBatchDeleteButtonState方法单独管理

        saveButton.isEnabled = hasSelection
        editButton.isEnabled = hasSelection
        applyButton.isEnabled = hasSelection

        // 🐾 根据应用状态动态更新按钮文字和样式
        if (hasSelection && selectedLayout != null) {
            val isCurrentLayoutApplied = appliedLayout?.id == selectedLayout!!.id

            if (isCurrentLayoutApplied) {
                // 当前布局已应用，显示"取消应用"
                applyButton.text = "取消应用"
                applyButton.setBackgroundResource(R.drawable.button_cancel_apply_background)
                AppLog.d("按钮状态更新: 取消应用模式")
            } else {
                // 当前布局未应用，显示"应用布局"
                applyButton.text = "应用布局"
                applyButton.setBackgroundResource(R.drawable.button_background)
                AppLog.d("按钮状态更新: 应用布局模式")
            }
        } else {
            // 无选中状态，恢复默认
            applyButton.text = "应用布局"
            applyButton.setBackgroundResource(R.drawable.button_background)
        }
    }
    
    /**
     * 显示/隐藏空详情视图
     */
    private fun showEmptyDetailView(show: Boolean) {
        if (show) {
            detailListRecyclerView.visibility = View.GONE
            emptyView.visibility = View.VISIBLE
        } else {
            detailListRecyclerView.visibility = View.VISIBLE
            emptyView.visibility = View.GONE
        }
    }

    // ==================== 批量选择和删除相关方法 ====================

    /**
     * 🐾 处理选择模式切换
     */
    private fun handleSelectionModeToggle() {
        if (layoutListAdapter.isInSelectionMode()) {
            // 退出选择模式
            layoutListAdapter.exitSelectionMode()
            selectionButton.text = "选择"
            updateBatchDeleteButtonState(0)
            AppLog.d("退出批量选择模式")
        } else {
            // 进入选择模式
            layoutListAdapter.enterSelectionMode()
            selectionButton.text = "退出"
            updateBatchDeleteButtonState(0)
            AppLog.d("进入批量选择模式")
        }
    }

    /**
     * 🐾 更新批量删除按钮状态
     */
    private fun updateBatchDeleteButtonState(selectedCount: Int) {
        batchDeleteButton.isEnabled = selectedCount > 0
        AppLog.d("更新批量删除按钮状态: selectedCount=$selectedCount, enabled=${selectedCount > 0}")
    }

    /**
     * 🐾 处理批量删除
     */
    private fun handleBatchDelete() {
        val selectedLayouts = layoutListAdapter.getSelectedLayouts()
        if (selectedLayouts.isEmpty()) {
            context?.let { ToastUtils.showToast(it, "请先选择要删除的布局") }
            return
        }

        // 显示确认对话框
        showBatchDeleteConfirmDialog(selectedLayouts)
    }

    /**
     * 🐾 显示批量删除确认对话框
     */
    private fun showBatchDeleteConfirmDialog(layouts: List<WindowLayoutEntity>) {
        val context = context ?: return

        val layoutNames = layouts.joinToString("、") { it.layoutName }
        val message = "确定要删除以下${layouts.size}个布局吗？\n\n$layoutNames"

        androidx.appcompat.app.AlertDialog.Builder(context)
            .setTitle("批量删除确认")
            .setMessage(message)
            .setPositiveButton("删除") { _, _ ->
                performBatchDelete(layouts)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 🐾 执行批量删除操作
     */
    private fun performBatchDelete(layouts: List<WindowLayoutEntity>) {
        val layoutIds = layouts.map { it.id }

        // 禁用按钮防止重复操作
        batchDeleteButton.isEnabled = false
        selectionButton.isEnabled = false

        layoutManager.deleteLayouts(layoutIds) { success, message ->
            // 恢复按钮状态
            batchDeleteButton.isEnabled = true
            selectionButton.isEnabled = true

            if (success) {
                AppLog.d("批量删除成功: 删除了${layouts.size}个布局")
                context?.let { ToastUtils.showToast(it, "成功删除${layouts.size}个布局") }

                // 退出选择模式
                layoutListAdapter.exitSelectionMode()
                selectionButton.text = "选择"

                // 清空选中状态和应用状态
                selectedLayout = null
                layoutListAdapter.setSelectedLayoutId(-1)

                // 检查是否删除了当前应用的布局
                val deletedAppliedLayout = layouts.any { it.id == appliedLayout?.id }
                if (deletedAppliedLayout) {
                    appliedLayout = null
                    layoutListAdapter.setAppliedLayoutId(-1)
                    AppLog.d("删除的布局中包含当前应用的布局，已清除应用状态")
                }

                layoutDetailAdapter.submitList(emptyList())
                updateButtonStates(false)
                showEmptyDetailView(true)
            } else {
                AppLog.w("批量删除失败: $message")
                context?.let { ToastUtils.showToast(it, "删除失败: $message") }
            }
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onDialogDismissed?.invoke()
        AppLog.d("导播台BottomSheet对话框已关闭")
    }
}
