package com.example.castapp.ui.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.ui.adapter.RemoteSenderDeviceAdapter
import com.example.castapp.model.RemoteSenderConnection

/**
 * 发送端标签页Fragment
 * 显示和管理远程发送端设备列表
 */
class RemoteSenderTabFragment : Fragment() {

    private lateinit var senderConnectionCountText: TextView
    private lateinit var addSenderConnectionButton: ImageButton
    private lateinit var senderConnectionsRecyclerView: RecyclerView
    private lateinit var connectionAdapter: RemoteSenderDeviceAdapter

    private val connections = mutableListOf<RemoteSenderConnection>()

    // 回调接口
    private var onConnectClickListener: ((RemoteSenderConnection) -> Unit)? = null
    private var onControlClickListener: ((RemoteSenderConnection) -> Unit)? = null
    private var onEditClickListener: ((RemoteSenderConnection) -> Unit)? = null
    private var onDeleteClickListener: ((RemoteSenderConnection) -> Unit)? = null
    private var onAddConnectionClickListener: (() -> Unit)? = null

    companion object {
        fun newInstance(): RemoteSenderTabFragment {
            return RemoteSenderTabFragment()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_sender_tab, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
        setupRecyclerView()
        setupClickListeners()
    }

    private fun initViews(view: View) {
        senderConnectionCountText = view.findViewById(R.id.sender_connection_count_text)
        addSenderConnectionButton = view.findViewById(R.id.add_sender_connection_button)
        senderConnectionsRecyclerView = view.findViewById(R.id.sender_connections_recycler_view)
    }

    private fun setupRecyclerView() {
        connectionAdapter = RemoteSenderDeviceAdapter(
            connections = connections,
            onConnectClick = { connection ->
                onConnectClickListener?.invoke(connection)
            },
            onControlClick = { connection ->
                onControlClickListener?.invoke(connection)
            },
            onEditClick = { connection ->
                onEditClickListener?.invoke(connection)
            },
            onDeleteClick = { connection ->
                onDeleteClickListener?.invoke(connection)
            }
        )
        
        senderConnectionsRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = connectionAdapter
        }
    }

    private fun setupClickListeners() {
        addSenderConnectionButton.setOnClickListener {
            onAddConnectionClickListener?.invoke()
        }
    }

    /**
     * 设置连接点击监听器
     */
    fun setOnConnectClickListener(listener: (RemoteSenderConnection) -> Unit) {
        onConnectClickListener = listener
    }

    /**
     * 设置控制点击监听器
     */
    fun setOnControlClickListener(listener: (RemoteSenderConnection) -> Unit) {
        onControlClickListener = listener
    }

    /**
     * 设置编辑点击监听器
     */
    fun setOnEditClickListener(listener: (RemoteSenderConnection) -> Unit) {
        onEditClickListener = listener
    }

    /**
     * 设置删除点击监听器
     */
    fun setOnDeleteClickListener(listener: (RemoteSenderConnection) -> Unit) {
        onDeleteClickListener = listener
    }

    /**
     * 设置添加连接点击监听器
     */
    fun setOnAddConnectionClickListener(listener: () -> Unit) {
        onAddConnectionClickListener = listener
    }

    /**
     * 更新连接列表
     */
    fun updateConnections(newConnections: List<RemoteSenderConnection>) {
        connections.clear()
        connections.addAll(newConnections)
        connectionAdapter.notifyDataSetChanged()
        updateConnectionCount()
    }

    /**
     * 添加连接
     */
    fun addConnection(connection: RemoteSenderConnection) {
        connectionAdapter.addConnection(connection)
        updateConnectionCount()
    }

    /**
     * 更新连接状态
     */
    fun updateConnection(connection: RemoteSenderConnection) {
        connectionAdapter.updateConnection(connection)
    }

    /**
     * 删除连接
     */
    fun removeConnection(connection: RemoteSenderConnection) {
        connectionAdapter.removeConnection(connection)
        updateConnectionCount()
    }

    /**
     * 更新连接数量显示
     */
    private fun updateConnectionCount() {
        senderConnectionCountText.text = "${connections.size}个设备"
    }

    /**
     * 获取所有连接
     */
    fun getAllConnections(): List<RemoteSenderConnection> = connections.toList()
}
