package com.example.castapp.ui

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.database.ContentObserver
import android.media.AudioManager
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import com.example.castapp.R
import com.example.castapp.service.ReceivingService
import com.example.castapp.utils.AppLog
import com.example.castapp.viewmodel.ReceiverViewModel
import com.example.castapp.websocket.ControlMessage

/**
 * 接收端设置对话框
 */
class ReceiverDialogFragment : DialogFragment() {
    private val viewModel: ReceiverViewModel by viewModels()

    private lateinit var ipAddressText: TextView
    private lateinit var portInput: EditText
    private lateinit var audioVideoServerSwitch: androidx.appcompat.widget.SwitchCompat
    private lateinit var serverStatus: TextView
    private lateinit var closeButton: ImageButton
    private lateinit var audioOutputModeGroup: RadioGroup
    private lateinit var receiverVolumeSeekBar: SeekBar
    private lateinit var receiverVolumeText: TextView
    private lateinit var remoteControlSwitch: androidx.appcompat.widget.SwitchCompat

    // 系统音量管理 - 重构版：单向数据流状态管理
    private lateinit var audioManager: AudioManager
    private var volumeContentObserver: ContentObserver? = null
    private var isUserDragging = false // 用户是否正在拖动滑动条

    // 🎛️ 远程设置变更广播接收器
    private var remoteSettingsReceiver: BroadcastReceiver? = null

    // 🔄 双向同步：本地操作标识（避免反向同步时重复发送）
    private var isLocalOperation = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.DialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_receive, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews(view)
        setupClickListeners()
        observeViewModel()
        // 确保音量键控制正确的音频流
        setupVolumeControlStream()
        // 注册远程设置变更广播接收器
        registerRemoteSettingsReceiver()
    }

    /**
     * 初始化视图
     */
    private fun initViews(view: View) {
        ipAddressText = view.findViewById(R.id.ip_address_text)
        portInput = view.findViewById(R.id.port_input)
        audioVideoServerSwitch = view.findViewById(R.id.audio_video_server_switch)
        serverStatus = view.findViewById(R.id.server_status)
        closeButton = view.findViewById(R.id.close_dialog_button)
        audioOutputModeGroup = view.findViewById(R.id.audio_output_mode_group)
        receiverVolumeSeekBar = view.findViewById(R.id.receiver_volume_seekbar)
        receiverVolumeText = view.findViewById(R.id.receiver_volume_text)
        remoteControlSwitch = view.findViewById(R.id.remote_control_switch)

        // 初始化音频管理器
        audioManager = requireContext().getSystemService(Context.AUDIO_SERVICE) as AudioManager

        // 加载保存的播放模式设置
        loadAudioOutputMode()

        // 加载保存的音量设置并同步系统音量
        loadReceiverVolumeAndSync()

        // 设置系统音量监听
        setupSystemVolumeListener()
    }

    /**
     * 观察ViewModel状态变化
     */
    private fun observeViewModel() {
        // 观察本地IP地址
        viewModel.localIpAddress.observe(viewLifecycleOwner) { ipAddress ->
            ipAddressText.text = ipAddress
        }

        // 观察端口
        viewModel.port.observe(viewLifecycleOwner) { port ->
            portInput.setText(port.toString())
        }

        // 观察服务器状态文本
        viewModel.serverStatusText.observe(viewLifecycleOwner) { statusText ->
            serverStatus.text = statusText
        }

        // 观察音视频服务器运行状态
        viewModel.isReceivingServiceRunning.observe(viewLifecycleOwner) { isRunning ->
            AppLog.d("【状态同步】音视频服务状态变化: $isRunning")

            // 🔄 关键：标记为非本地操作，避免触发反向同步
            isLocalOperation = false

            // 暂时移除监听器，避免循环触发
            audioVideoServerSwitch.setOnCheckedChangeListener(null)
            audioVideoServerSwitch.isChecked = isRunning ?: false
            // 重新设置监听器
            setupAudioVideoServerSwitchListener()

            // 🔄 恢复本地操作标识
            isLocalOperation = true

            // 统一更新端口输入框状态
            updatePortInputState()

            AppLog.d("【状态同步】音视频服务开关UI已更新: ${isRunning ?: false}")
        }

        // 观察Toast消息
        viewModel.toastMessage.observe(viewLifecycleOwner) { message ->
            if (!message.isNullOrEmpty()) {
                showToast(message)
            }
        }

        // 观察加载状态
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            // 只控制开关的启用/禁用
            audioVideoServerSwitch.isEnabled = !isLoading

            // 统一更新端口输入框状态
            updatePortInputState()
        }

        // 观察固定端口WebSocket服务器状态
        viewModel.isFixedWebSocketRunning.observe(viewLifecycleOwner) { isRunning ->
            // 临时移除监听器，防止重复触发
            remoteControlSwitch.setOnCheckedChangeListener(null)
            remoteControlSwitch.isChecked = isRunning
            // 重新设置监听器
            setupRemoteControlSwitchListener()
        }

        // 观察固定端口WebSocket服务器加载状态
        viewModel.isFixedWebSocketLoading.observe(viewLifecycleOwner) { isLoading ->
            remoteControlSwitch.isEnabled = !isLoading
        }

        // 确保初始状态正确
        updatePortInputState()
    }

    /**
     * 统一更新端口输入框状态
     * 根据加载状态和服务运行状态决定端口输入框的启用/禁用
     */
    private fun updatePortInputState() {
        val isLoading = viewModel.isLoading.value ?: false
        val isServiceRunning = viewModel.isReceivingServiceRunning.value ?: false

        // 优先级：加载中 > 服务运行中 > 正常状态
        // 加载中或服务运行中时禁用，否则启用
        portInput.isEnabled = !isLoading && !isServiceRunning

        AppLog.d("更新端口输入框状态: 加载中=$isLoading, 服务运行中=$isServiceRunning, 端口框启用=${portInput.isEnabled}")
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        setupAudioVideoServerSwitchListener()
        setupOtherClickListeners()
    }

    /**
     * 设置音视频服务器开关监听器
     */
    private fun setupAudioVideoServerSwitchListener() {
        audioVideoServerSwitch.setOnCheckedChangeListener { _, isChecked ->
            // 更新端口
            val portText = portInput.text.toString()
            AppLog.d("【双向同步】用户切换音视频服务器开关: $isChecked，端口: $portText")
            if (viewModel.validatePort(portText)) {
                viewModel.setPort(portText.toInt())
            }
            viewModel.toggleServer()

            // 🔄 双向同步：如果是本地操作，发送反向同步消息
            if (isLocalOperation) {
                sendLocalAudioVideoChangedToRemote(isChecked)
            }
        }
    }

    private fun setupOtherClickListeners() {
        closeButton.setOnClickListener {
            // 只关闭对话框，不停止服务器
            dismiss()
        }

        // 设置远程被控开关监听器
        setupRemoteControlSwitchListener()

        // 播放模式选择监听器
        audioOutputModeGroup.setOnCheckedChangeListener { _, checkedId ->
            val isSpeakerMode = checkedId == R.id.speaker_mode_radio

            // 🔥 时序修复：先读取旧模式的音量，避免SharedPreferences时序问题
            val oldVolume = getCurrentSystemVolumePercent()

            // 保存新的播放模式
            saveAudioOutputMode(isSpeakerMode)
            viewModel.setAudioOutputMode(isSpeakerMode)

            // 🔥 关键：使用带参数的方法，确保所有操作基于新的播放模式
            setupVolumeControlStreamForMode(isSpeakerMode)

            // 🔥 关键：重新设置音量监听器，确保监听正确的音频流
            resetSystemVolumeListener()

            // 🔥 时序修复：读取新模式对应的音量流当前音量，用于音量转换
            val newModeCurrentVolume = getSystemVolumePercentForMode(isSpeakerMode)

            // 确保音量值在不同音频流之间正确转换和保持
            // 如果新模式的当前音量与期望不同，则设置为旧模式的音量值
            if (newModeCurrentVolume != oldVolume) {
                ensureVolumeConsistency(oldVolume)
            }

            AppLog.d("【双向同步】播放模式切换: ${if (isSpeakerMode) "扬声器" else "听筒"}，旧音量: ${oldVolume}%，新模式当前音量: ${newModeCurrentVolume}%")

            // 🔄 双向同步：如果是本地操作，发送反向同步消息
            if (isLocalOperation) {
                sendLocalPlaybackModeChangedToRemote(isSpeakerMode)
            }
        }

        // 音量滑动条监听器 - 重构版：单向数据流，只处理用户输入
        receiverVolumeSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                // 只处理用户操作，程序更新通过系统音量监听器处理
                if (fromUser) {
                    // 立即更新文本显示，提供即时反馈
                    receiverVolumeText.text = "${progress}%"
                    // 设置系统音量，后续UI更新由系统音量监听器处理
                    setSystemVolume(progress)
                    AppLog.d("用户调整音量: ${progress}%")
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                // 开始拖动时暂时禁用系统音量监听，避免冲突
                isUserDragging = true
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                val volume = seekBar?.progress ?: 80
                // 保存音量设置
                saveReceiverVolume(volume)
                // 重新启用系统音量监听
                isUserDragging = false
                AppLog.d("【双向同步】用户完成音量调整: ${volume}%")

                // 🔄 双向同步：如果是本地操作，发送反向同步消息
                if (isLocalOperation) {
                    sendLocalVolumeChangedToRemote(volume)
                }
            }
        })
    }

    private fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }

    /**
     * 加载音频输出模式设置 - 🔧 修复：确保获取最新的实际状态
     */
    private fun loadAudioOutputMode() {
        val sharedPrefs = requireContext().getSharedPreferences("receiver_settings", Context.MODE_PRIVATE)
        val isSpeakerMode = sharedPrefs.getBoolean("audio_output_speaker_mode", true) // 默认扬声器

        AppLog.d("【状态同步】从SharedPreferences加载播放模式: ${if (isSpeakerMode) "扬声器" else "听筒"}")

        // 🔄 关键：标记为非本地操作，避免触发反向同步
        isLocalOperation = false

        if (isSpeakerMode) {
            audioOutputModeGroup.check(R.id.speaker_mode_radio)
        } else {
            audioOutputModeGroup.check(R.id.earpiece_mode_radio)
        }

        // 🔄 恢复本地操作标识
        isLocalOperation = true

        // 通知ViewModel当前的播放模式
        viewModel.setAudioOutputMode(isSpeakerMode)
        AppLog.d("【状态同步】播放模式设置已加载并同步: ${if (isSpeakerMode) "扬声器" else "听筒"}")
    }

    /**
     * 保存音频输出模式设置
     */
    private fun saveAudioOutputMode(isSpeakerMode: Boolean) {
        val sharedPrefs = requireContext().getSharedPreferences("receiver_settings", Context.MODE_PRIVATE)
        sharedPrefs.edit()
            .putBoolean("audio_output_speaker_mode", isSpeakerMode)
            .apply()
        AppLog.d("保存播放模式设置: ${if (isSpeakerMode) "扬声器" else "听筒"}")
    }

    /**
     * 加载接收端音量设置并同步系统音量 - 🔥 根本性修复：总是基于当前系统音量
     */
    private fun loadReceiverVolumeAndSync() {
        // 🔥 关键修复：总是读取当前系统音量，而不是用保存的旧值覆盖
        val currentSystemVolume = getCurrentSystemVolumePercent()

        // 用当前系统音量更新UI，确保显示真实的音量状态
        updateVolumeUI(currentSystemVolume)

        // 保存当前系统音量，确保SharedPreferences与实际状态同步
        saveReceiverVolume(currentSystemVolume)

        AppLog.d("加载接收端音量设置: ${currentSystemVolume}% (基于当前系统音量，不覆盖用户调整)")
    }

    /**
     * 保存接收端音量设置
     */
    private fun saveReceiverVolume(volume: Int) {
        val sharedPrefs = requireContext().getSharedPreferences("receiver_settings", Context.MODE_PRIVATE)
        sharedPrefs.edit()
            .putInt("receiver_volume", volume)
            .apply()
        AppLog.d("保存接收端音量设置: ${volume}%")
    }

    /**
     * 获取当前播放模式对应的音量流 - 🔥 根本性修复：动态音量流控制
     */
    private fun getCurrentVolumeStream(): Int {
        return if (getCurrentAudioOutputMode()) {
            AudioManager.STREAM_MUSIC // 扬声器模式：媒体音量流
        } else {
            AudioManager.STREAM_VOICE_CALL // 听筒模式：通话音量流
        }
    }

    /**
     * 根据指定播放模式获取对应的音量流 - 🔥 时序修复：避免SharedPreferences读取时序问题
     */
    private fun getVolumeStreamForMode(isSpeakerMode: Boolean): Int {
        return if (isSpeakerMode) {
            AudioManager.STREAM_MUSIC // 扬声器模式：媒体音量流
        } else {
            AudioManager.STREAM_VOICE_CALL // 听筒模式：通话音量流
        }
    }

    /**
     * 获取当前音频输出模式
     */
    private fun getCurrentAudioOutputMode(): Boolean {
        val sharedPrefs = requireContext().getSharedPreferences("receiver_settings", Context.MODE_PRIVATE)
        return sharedPrefs.getBoolean("audio_output_speaker_mode", true) // true=扬声器，false=听筒
    }

    /**
     * 获取当前系统音量百分比 - 🔥 根本性修复：根据播放模式读取对应音量流
     */
    private fun getCurrentSystemVolumePercent(): Int {
        val volumeStream = getCurrentVolumeStream()
        val currentVolume = audioManager.getStreamVolume(volumeStream)
        val maxVolume = audioManager.getStreamMaxVolume(volumeStream)
        val streamName = if (volumeStream == AudioManager.STREAM_MUSIC) "STREAM_MUSIC" else "STREAM_VOICE_CALL"

        return if (maxVolume > 0) {
            val percent = ((currentVolume.toFloat() / maxVolume) * 100).toInt()
            AppLog.d("读取系统音量: ${percent}% ($currentVolume/$maxVolume) [$streamName]")
            percent
        } else {
            AppLog.w("无法获取音量流最大值: $streamName")
            80 // 默认值
        }
    }

    /**
     * 根据指定播放模式获取系统音量百分比 - 🔥 时序修复：避免SharedPreferences读取时序问题
     */
    private fun getSystemVolumePercentForMode(isSpeakerMode: Boolean): Int {
        val volumeStream = getVolumeStreamForMode(isSpeakerMode)
        val currentVolume = audioManager.getStreamVolume(volumeStream)
        val maxVolume = audioManager.getStreamMaxVolume(volumeStream)
        val streamName = if (volumeStream == AudioManager.STREAM_MUSIC) "STREAM_MUSIC" else "STREAM_VOICE_CALL"
        val modeName = if (isSpeakerMode) "扬声器" else "听筒"

        return if (maxVolume > 0) {
            val percent = ((currentVolume.toFloat() / maxVolume) * 100).toInt()
            AppLog.d("读取${modeName}模式音量: ${percent}% ($currentVolume/$maxVolume) [$streamName]")
            percent
        } else {
            AppLog.w("无法获取${modeName}模式音量流最大值: $streamName")
            80 // 默认值
        }
    }

    /**
     * 设置系统音量 - 🔥 根本性修复：根据播放模式设置对应音量流
     */
    private fun setSystemVolume(volumePercent: Int) {
        try {
            val volumeStream = getCurrentVolumeStream()
            val maxVolume = audioManager.getStreamMaxVolume(volumeStream)
            val targetVolume = ((volumePercent / 100.0f) * maxVolume + 0.5f).toInt()
            val streamName = if (volumeStream == AudioManager.STREAM_MUSIC) "STREAM_MUSIC" else "STREAM_VOICE_CALL"

            audioManager.setStreamVolume(volumeStream, targetVolume, 0)
            AppLog.d("设置系统音量: ${volumePercent}% -> $targetVolume/$maxVolume [$streamName]")
        } catch (e: Exception) {
            AppLog.e("设置系统音量失败", e)
        }
    }

    /**
     * 更新音量UI - 重构版：单一职责，只负责UI更新
     */
    private fun updateVolumeUI(volumePercent: Int) {
        receiverVolumeSeekBar.progress = volumePercent
        receiverVolumeText.text = "${volumePercent}%"
        AppLog.d("更新音量UI: ${volumePercent}%")
    }

    /**
     * 设置系统音量监听 - 重构版：单向数据流的核心，系统音量是唯一数据源
     */
    private fun setupSystemVolumeListener() {
        volumeContentObserver = object : ContentObserver(Handler(Looper.getMainLooper())) {
            override fun onChange(selfChange: Boolean, uri: Uri?) {
                super.onChange(selfChange, uri)
                // 只有在用户不拖动滑动条时才更新UI，避免冲突
                if (!isUserDragging) {
                    val systemVolumePercent = getCurrentSystemVolumePercent()
                    AppLog.d("系统音量变化: ${systemVolumePercent}%")
                    updateVolumeUI(systemVolumePercent)
                    // 保存系统音量变化（包括精度调整后的实际值）
                    saveReceiverVolume(systemVolumePercent)
                }
            }
        }

        requireContext().contentResolver.registerContentObserver(
            Settings.System.CONTENT_URI,
            true,
            volumeContentObserver!!
        )

        val volumeStream = getCurrentVolumeStream()
        val maxVolume = audioManager.getStreamMaxVolume(volumeStream)
        val streamName = if (volumeStream == AudioManager.STREAM_MUSIC) "STREAM_MUSIC" else "STREAM_VOICE_CALL"
        AppLog.d("系统音量监听器已设置，监听音量流: $streamName，最大音量档位: $maxVolume")
    }

    /**
     * 设置音量控制流 - 🔥 根本性修复：根据播放模式设置对应的音量键控制流
     */
    private fun setupVolumeControlStream() {
        try {
            val volumeStream = getCurrentVolumeStream()
            val streamName = if (volumeStream == AudioManager.STREAM_MUSIC) "STREAM_MUSIC" else "STREAM_VOICE_CALL"

            // 根据播放模式设置Activity的音量控制流
            requireActivity().volumeControlStream = volumeStream
            AppLog.d("设置音量控制流: $streamName，确保音量键控制正确的音频流")
        } catch (e: Exception) {
            AppLog.e("设置音量控制流失败", e)
        }
    }

    /**
     * 根据指定播放模式设置音量控制流 - 🔥 时序修复：避免SharedPreferences读取时序问题
     */
    private fun setupVolumeControlStreamForMode(isSpeakerMode: Boolean) {
        try {
            val volumeStream = getVolumeStreamForMode(isSpeakerMode)
            val streamName = if (volumeStream == AudioManager.STREAM_MUSIC) "STREAM_MUSIC" else "STREAM_VOICE_CALL"
            val modeName = if (isSpeakerMode) "扬声器" else "听筒"

            // 根据指定播放模式设置Activity的音量控制流
            requireActivity().volumeControlStream = volumeStream
            AppLog.d("设置${modeName}模式音量控制流: $streamName，确保音量键控制正确的音频流")
        } catch (e: Exception) {
            AppLog.e("设置音量控制流失败", e)
        }
    }

    /**
     * 确保音量一致性 - 切换播放模式时保持音量不变
     */
    private fun ensureVolumeConsistency(targetVolume: Int) {
        try {
            // 延迟一小段时间确保播放模式切换完成
            receiverVolumeSeekBar.postDelayed({
                val currentVolume = getCurrentSystemVolumePercent()
                if (currentVolume != targetVolume) {
                    AppLog.d("音量不一致，恢复到: ${targetVolume}% (当前: ${currentVolume}%)")
                    setSystemVolume(targetVolume)
                    updateVolumeUI(targetVolume)
                } else {
                    AppLog.d("音量一致性检查通过: ${targetVolume}%")
                }
            }, 100) // 100ms延迟
        } catch (e: Exception) {
            AppLog.e("确保音量一致性失败", e)
        }
    }

    /**
     * 重新设置系统音量监听器 - 🔥 根本性修复：播放模式切换时重新监听对应音量流
     */
    private fun resetSystemVolumeListener() {
        try {
            // 先清理现有监听器
            volumeContentObserver?.let { observer ->
                requireContext().contentResolver.unregisterContentObserver(observer)
                AppLog.d("已清理旧的音量监听器")
            }

            // 重新设置监听器，监听当前播放模式对应的音量流
            setupSystemVolumeListener()
            AppLog.d("已重新设置音量监听器")
        } catch (e: Exception) {
            AppLog.e("重新设置音量监听器失败", e)
        }
    }

    // 移除updateVolumeFromSystem方法，已被updateVolumeUI替代

    override fun onDestroy() {
        super.onDestroy()
        // 清理系统音量监听器
        volumeContentObserver?.let { observer ->
            requireContext().contentResolver.unregisterContentObserver(observer)
        }
        AppLog.d("系统音量监听器已清理")

        // 注销远程设置变更广播接收器
        unregisterRemoteSettingsReceiver()
    }

    /**
     * 设置远程被控开关监听器
     */
    private fun setupRemoteControlSwitchListener() {
        remoteControlSwitch.setOnCheckedChangeListener { _, isChecked ->
            // 防止观察者触发的状态变化导致重复调用
            if (remoteControlSwitch.isEnabled) {
                AppLog.d("用户切换远程被控开关: $isChecked")
                if (isChecked) {
                    viewModel.startFixedWebSocketServerFromUI()
                } else {
                    viewModel.stopFixedWebSocketServerFromUI()
                }
            }
        }
    }

    // ========== 🎛️ 远程设置变更广播接收器 ==========

    /**
     * 注册远程设置变更广播接收器
     */
    private fun registerRemoteSettingsReceiver() {
        remoteSettingsReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                if (intent?.action == "com.example.castapp.REMOTE_SETTINGS_CHANGED") {
                    handleRemoteSettingsChanged(intent)
                }
            }
        }

        val filter = IntentFilter("com.example.castapp.REMOTE_SETTINGS_CHANGED")
        requireContext().registerReceiver(remoteSettingsReceiver, filter, Context.RECEIVER_NOT_EXPORTED)
        AppLog.d("【远程设置控制】广播接收器已注册")
    }

    /**
     * 注销远程设置变更广播接收器
     */
    private fun unregisterRemoteSettingsReceiver() {
        remoteSettingsReceiver?.let { receiver ->
            try {
                requireContext().unregisterReceiver(receiver)
                AppLog.d("【远程设置控制】广播接收器已注销")
            } catch (e: Exception) {
                AppLog.w("【远程设置控制】注销广播接收器失败", e)
            }
        }
        remoteSettingsReceiver = null
    }

    /**
     * 处理远程设置变更
     */
    private fun handleRemoteSettingsChanged(intent: Intent) {
        val settingType = intent.getStringExtra("setting_type") ?: return
        AppLog.d("【双向同步】收到远程设置变更通知: $settingType")

        // 🔄 关键：标记为非本地操作，避免反向同步时重复发送
        isLocalOperation = false

        when (settingType) {
            "audio_video_enabled" -> {
                val enabled = intent.getBooleanExtra("setting_value_boolean", false)
                AppLog.d("【双向同步】音视频服务状态变更: $enabled")
                // 更新音视频服务开关UI
                activity?.runOnUiThread {
                    audioVideoServerSwitch.isChecked = enabled
                }
            }

            "playback_mode" -> {
                val isSpeakerMode = intent.getBooleanExtra("setting_value_boolean", true)
                AppLog.d("【双向同步】播放模式变更: ${if (isSpeakerMode) "扬声器" else "听筒"}")
                // 更新播放模式UI
                activity?.runOnUiThread {
                    if (isSpeakerMode) {
                        audioOutputModeGroup.check(R.id.speaker_mode_radio)
                    } else {
                        audioOutputModeGroup.check(R.id.earpiece_mode_radio)
                    }
                }
            }

            "volume" -> {
                val volume = intent.getIntExtra("setting_value_int", 80)
                AppLog.d("【双向同步】音量变更: $volume%")
                // 更新音量UI
                activity?.runOnUiThread {
                    updateVolumeUI(volume)
                }
            }
        }

        // 🔄 恢复本地操作标识
        isLocalOperation = true
    }

    // ========== 🔄 双向同步：发送反向同步消息到遥控端 ==========

    /**
     * 发送本地音视频服务状态变更到遥控端
     */
    private fun sendLocalAudioVideoChangedToRemote(enabled: Boolean) {
        try {
            AppLog.d("【双向同步】接收端准备发送音视频服务状态变更: $enabled，isLocalOperation: $isLocalOperation")
            // 🔧 修复：使用广播消息，不依赖特定连接ID
            val message = ControlMessage.createReceiverLocalAudioVideoChanged("receiver_broadcast", enabled)
            sendMessageToAllRemoteControllers(message)
            AppLog.d("【双向同步】接收端已发送音视频服务状态变更到遥控端: $enabled")
        } catch (e: Exception) {
            AppLog.e("【双向同步】接收端发送音视频服务状态变更失败", e)
        }
    }

    /**
     * 发送本地播放模式变更到遥控端
     */
    private fun sendLocalPlaybackModeChangedToRemote(isSpeakerMode: Boolean) {
        try {
            AppLog.d("【双向同步】接收端准备发送播放模式变更: ${if (isSpeakerMode) "扬声器" else "听筒"}，isLocalOperation: $isLocalOperation")
            // 🔧 修复：使用广播消息，不依赖特定连接ID
            val message = ControlMessage.createReceiverLocalPlaybackModeChanged("receiver_broadcast", isSpeakerMode)
            sendMessageToAllRemoteControllers(message)
            AppLog.d("【双向同步】接收端已发送播放模式变更到遥控端: ${if (isSpeakerMode) "扬声器" else "听筒"}")
        } catch (e: Exception) {
            AppLog.e("【双向同步】接收端发送播放模式变更失败", e)
        }
    }

    /**
     * 发送本地音量变更到遥控端
     */
    private fun sendLocalVolumeChangedToRemote(volume: Int) {
        try {
            // 🔧 修复：使用广播消息，不依赖特定连接ID
            val message = ControlMessage.createReceiverLocalVolumeChanged("receiver_broadcast", volume)
            sendMessageToAllRemoteControllers(message)
            AppLog.d("【双向同步】已发送音量变更到遥控端: $volume%")
        } catch (e: Exception) {
            AppLog.e("【双向同步】发送音量变更失败", e)
        }
    }

    /**
     * 发送消息到所有连接的遥控端
     */
    private fun sendMessageToAllRemoteControllers(message: ControlMessage) {
        try {
            // 通过ReceivingService发送消息到所有连接的遥控端
            val intent = Intent(requireContext(), ReceivingService::class.java)
            intent.action = "SEND_MESSAGE_TO_REMOTE_CONTROLLERS"
            intent.putExtra("message_json", message.toJson())
            requireContext().startService(intent)
        } catch (e: Exception) {
            AppLog.e("【双向同步】发送消息到遥控端失败", e)
        }
    }
}
