package com.example.castapp.ui.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.util.TypedValue
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import android.widget.FrameLayout
import androidx.core.graphics.toColorInt
import androidx.core.graphics.withSave
import com.example.castapp.R
import com.example.castapp.model.WindowVisualizationData
import com.example.castapp.utils.AppLog
import com.example.castapp.utils.WindowScaleCalculator
import kotlin.math.abs
import kotlin.math.atan2
import kotlin.math.roundToInt
import kotlin.math.sqrt

/**
 * 🪟 投屏窗口容器可视化View
 * 在远程接收端控制窗口中绘制投屏窗口容器的可视化框架
 * 🎯 重构：使用FrameLayout + 子View架构，每个窗口使用独立的View.clipBounds裁剪
 */
class WindowContainerVisualizationView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    // 🎯 移除Paint对象，现在在WindowVisualizationContainerView中处理绘制
    
    // 数据
    private var visualizationDataList: List<WindowVisualizationData> = emptyList()
    private var isVisualizationEnabled = false

    // 🎯 新增：窗口容器View映射表，用于管理每个窗口的独立View
    private val windowContainerViews = mutableMapOf<String, WindowVisualizationContainerView>()

    // 🎯 新增：裁剪控制按钮组
    private var cropControlButtons: android.widget.LinearLayout? = null

    // 🎯 按钮组位置保存（参考接收端CropManager）
    private var buttonAbsoluteX = -1f
    private var buttonAbsoluteY = -1f

    // 🎯 原始裁剪状态保存（用于取消操作恢复）
    private var originalCropRatioForCancel: RectF? = null

    // 📸 截图数据
    private var screenshotDataMap: Map<String, Bitmap> = emptyMap()
    private var isScreenshotMode = false

    // 🎯 拖动相关属性
    private var isDragging = false
    private var draggedWindowData: WindowVisualizationData? = null
    private var lastTouchX = 0f
    private var lastTouchY = 0f
    private var dragOffsetX = 0f
    private var dragOffsetY = 0f
    private val dragThreshold = 15f

    // 🎯 缩放相关属性
    private var isScaling = false
    private var scaledWindowData: WindowVisualizationData? = null
    private var initialScaleFactor = 1.0f
    private var currentScaleFactor = 1.0f
    private var originalWindowWidth = 0f
    private var originalWindowHeight = 0f
    private var baseAbsoluteScaleFactor = 1.0f // 接收端的基础绝对缩放因子
    private var scaleEndTime = 0L
    private val scaleEndDelay = 100L // 缩放结束后100ms内不处理拖动
    private var scaleGestureDetector: ScaleGestureDetector

    // 🎯 旋转相关属性
    private var isRotating = false
    private var rotatedWindowData: WindowVisualizationData? = null
    private var currentRotationAngle = 0f
    private var baseAbsoluteRotationAngle = 0f // 接收端的基础绝对旋转角度
    private var rotationEndTime = 0L
    private val rotationEndDelay = 100L // 旋转结束后100ms内不处理拖动
    private var rotationGestureDetector: RotationGestureDetector

    // 🎯 裁剪相关属性
    private var isCropping = false
    private var croppedWindowData: WindowVisualizationData? = null
    private var cropOverlay: CropVisualizationOverlay? = null

    // 🎯 移除绘制相关常量，现在在WindowVisualizationContainerView中处理

    // 🎯 拖动和缩放回调接口
    interface OnWindowDragListener {
        /**
         * 拖动开始
         * @param windowData 被拖动的窗口数据
         */
        fun onDragStart(windowData: WindowVisualizationData)

        /**
         * 拖动中
         * @param windowData 被拖动的窗口数据
         * @param newX 新的X坐标（远程控制窗口坐标系）
         * @param newY 新的Y坐标（远程控制窗口坐标系）
         */
        fun onDragMove(windowData: WindowVisualizationData, newX: Float, newY: Float)

        /**
         * 拖动结束
         * @param windowData 被拖动的窗口数据
         * @param finalX 最终X坐标（远程控制窗口坐标系）
         * @param finalY 最终Y坐标（远程控制窗口坐标系）
         */
        fun onDragEnd(windowData: WindowVisualizationData, finalX: Float, finalY: Float)

        /**
         * 缩放开始
         * @param windowData 被缩放的窗口数据
         */
        fun onScaleStart(windowData: WindowVisualizationData)

        /**
         * 缩放中
         * @param windowData 被缩放的窗口数据
         * @param scaleFactor 当前缩放因子
         */
        fun onScaleMove(windowData: WindowVisualizationData, scaleFactor: Float)

        /**
         * 缩放结束
         * @param windowData 被缩放的窗口数据
         * @param finalScaleFactor 最终缩放因子（绝对值）
         * @param finalX 缩放后的最终X坐标（远程控制窗口坐标系）
         * @param finalY 缩放后的最终Y坐标（远程控制窗口坐标系）
         */
        fun onScaleEnd(windowData: WindowVisualizationData, finalScaleFactor: Float, finalX: Float, finalY: Float)

        /**
         * 旋转开始
         * @param windowData 被旋转的窗口数据
         */
        fun onRotationStart(windowData: WindowVisualizationData)

        /**
         * 旋转中
         * @param windowData 被旋转的窗口数据
         * @param rotationAngle 当前旋转角度
         */
        fun onRotationMove(windowData: WindowVisualizationData, rotationAngle: Float)

        /**
         * 旋转结束
         * @param windowData 被旋转的窗口数据
         * @param finalRotationAngle 最终旋转角度（绝对值）
         * @param finalX 旋转后的最终X坐标（远程控制窗口坐标系）
         * @param finalY 旋转后的最终Y坐标（远程控制窗口坐标系）
         */
        fun onRotationEnd(windowData: WindowVisualizationData, finalRotationAngle: Float, finalX: Float, finalY: Float)



        /**
         * 裁剪模式开始
         * @param windowData 进入裁剪模式的窗口数据
         */
        fun onCropModeStart(windowData: WindowVisualizationData)

        /**
         * 裁剪区域变化
         * @param windowData 被裁剪的窗口数据
         * @param cropRatio 当前裁剪区域比例
         */
        fun onCropAreaChange(windowData: WindowVisualizationData, cropRatio: RectF)

        /**
         * 裁剪模式结束
         * @param windowData 被裁剪的窗口数据
         * @param finalCropRatio 最终裁剪区域比例，null表示取消裁剪
         * @param isCancel 是否为取消操作
         */
        fun onCropModeEnd(windowData: WindowVisualizationData, finalCropRatio: RectF?, isCancel: Boolean)
    }

    // 拖动监听器
    private var onWindowDragListener: OnWindowDragListener? = null

    init {
        // 🎯 初始化缩放和旋转手势检测器
        scaleGestureDetector = ScaleGestureDetector(context, ScaleGestureListener())
        rotationGestureDetector = RotationGestureDetector(RotationGestureListener())
        AppLog.d("【窗口可视化】WindowContainerVisualizationView 初始化完成")
    }
    
    // 🎯 移除setupPaints方法，现在在WindowVisualizationContainerView中处理绘制
    
    /**
     * 更新可视化数据
     * @param dataList 窗口可视化数据列表
     * 🎯 重构：管理子View而不是重绘Canvas
     */
    fun updateVisualizationData(dataList: List<WindowVisualizationData>) {
        this.visualizationDataList = dataList.filter { it.shouldDisplay() }
        this.isVisualizationEnabled = visualizationDataList.isNotEmpty()

        AppLog.d("【窗口可视化】更新可视化数据: ${dataList.size} 个窗口，${visualizationDataList.size} 个可显示")

        // 🎯 管理子View：创建、更新或移除窗口容器View
        updateWindowContainerViews()
    }

    /**
     * 🎯 层级修复：管理窗口容器子View（优化层级排序逻辑）
     */
    private fun updateWindowContainerViews() {
        // 获取当前需要显示的窗口ID集合
        val currentWindowIds = visualizationDataList.map { it.connectionId }.toSet()

        // 移除不再需要的窗口View
        val viewsToRemove = windowContainerViews.keys - currentWindowIds
        viewsToRemove.forEach { windowId ->
            windowContainerViews[windowId]?.let { view ->
                removeView(view)
                windowContainerViews.remove(windowId)
                AppLog.d("【层级修复】移除窗口View: $windowId")
            }
        }

        // 🎯 层级修复：按层级排序（接收端实际逻辑：zOrder=1是最上层，zOrder越大越在下层）
        // 所以zOrder大的先添加（在底层），zOrder小的后添加（在上层）
        val sortedData = visualizationDataList.sortedByDescending { it.zOrder }

        // 🎯 层级修复：分别处理新建和更新，确保层级正确
        val newViews = mutableListOf<Pair<WindowVisualizationContainerView, WindowVisualizationData>>()
        val existingViews = mutableListOf<Pair<WindowVisualizationContainerView, WindowVisualizationData>>()

        // 分类处理窗口View
        sortedData.forEach { data ->
            val windowId = data.connectionId
            val existingView = windowContainerViews[windowId]

            if (existingView != null) {
                // 更新现有View
                existingView.setWindowData(data)
                existingViews.add(Pair(existingView, data))
                AppLog.d("【层级修复】更新窗口View: $windowId, zOrder=${data.zOrder}")
            } else {
                // 创建新View
                val bounds = data.getVisualizationBounds()
                val newView = WindowVisualizationContainerView(context)

                // 🎯 修复：先设置layoutParams再设置数据
                val layoutParams = FrameLayout.LayoutParams(
                    bounds.width().roundToInt(),
                    bounds.height().roundToInt()
                )
                newView.layoutParams = layoutParams

                // 然后设置窗口数据
                newView.setWindowData(data)

                newViews.add(Pair(newView, data))
                windowContainerViews[windowId] = newView
                AppLog.d("【层级修复】准备创建窗口View: $windowId, zOrder=${data.zOrder}")
            }
        }

        // 🎯 层级修复：先添加新View，再调整现有View的层级
        newViews.forEach { (view, data) ->
            addView(view)
            AppLog.d("【层级修复】新建窗口View已添加: ${data.connectionId}")
        }

        // 🎯 层级修复：调整现有View的层级顺序
        adjustExistingViewsLayerOrder(existingViews)

        AppLog.d("【层级修复】窗口管理完成，当前子View数量: ${childCount}")
        AppLog.d("【层级修复】添加顺序（先添加的在底层）: ${sortedData.map { "${it.getShortConnectionId()}(z=${it.zOrder})" }}")
        AppLog.d("【层级修复】显示层级（zOrder=1是最上层）: ${sortedData.reversed().map { "${it.getShortConnectionId()}(z=${it.zOrder})" }}")
    }

    /**
     * 🎯 层级修复：调整现有View的层级顺序
     */
    private fun adjustExistingViewsLayerOrder(existingViews: List<Pair<WindowVisualizationContainerView, WindowVisualizationData>>) {
        if (existingViews.isEmpty()) return

        // 接收端实际逻辑：zOrder=1是最上层，zOrder越大越在下层
        // 所以zOrder大的先调用bringToFront（移到底层），zOrder小的后调用bringToFront（移到上层）
        val sortedViews = existingViews.sortedByDescending { it.second.zOrder }

        // 使用bringToFront调整层级，zOrder大的先调用，zOrder小的后调用，确保zOrder小的最终在上层
        sortedViews.forEach { (view, data) ->
            view.bringToFront()
            AppLog.d("【层级修复】调整窗口层级: ${data.getShortConnectionId()}, zOrder=${data.zOrder}")
        }
    }

    /**
     * 🎯 层级修复：将指定窗口移到前台
     */
    fun bringWindowToFront(connectionId: String) {
        val windowView = windowContainerViews[connectionId]
        if (windowView != null) {
            windowView.bringToFront()
            AppLog.d("【层级修复】窗口已移到前台: $connectionId")
        } else {
            AppLog.w("【层级修复】未找到指定窗口: $connectionId")
        }
    }

    /**
     * 🎯 层级修复：批量调整窗口层级顺序
     */
    fun adjustWindowLayerOrder(orderedConnectionIds: List<String>) {
        AppLog.d("【层级修复】开始批量调整窗口层级，共 ${orderedConnectionIds.size} 个窗口")

        // 🎯 修复：按照正确的顺序调用bringToFront
        // 如果orderedConnectionIds是按照zOrder从小到大排序的，那么直接按顺序调用
        // 如果orderedConnectionIds是按照显示层级排序的（第一个是最上层），那么需要反向调用

        // 根据接收端的逻辑，这里应该是反向调用，确保列表第一个窗口最终在最上层
        orderedConnectionIds.reversed().forEachIndexed { reverseIndex, connectionId ->
            val originalIndex = orderedConnectionIds.size - 1 - reverseIndex
            val layerNumber = originalIndex + 1

            val windowView = windowContainerViews[connectionId]
            if (windowView != null) {
                windowView.bringToFront()
                AppLog.d("【层级修复】调整序号${layerNumber}窗口: $connectionId")
            } else {
                AppLog.w("【层级修复】未找到序号${layerNumber}窗口: $connectionId")
            }
        }

        AppLog.d("【层级修复】批量层级调整完成！序号1的窗口应该在最上层")
    }

    /**
     * 清除可视化数据
     * 🎯 重构：清除所有子View
     */
    fun clearVisualization() {
        visualizationDataList = emptyList()
        isVisualizationEnabled = false

        // 🎯 移除所有窗口容器View
        windowContainerViews.values.forEach { view ->
            removeView(view)
        }
        windowContainerViews.clear()

        AppLog.d("【窗口可视化】清除可视化数据，移除所有子View")
    }

    /**
     * 🎯 设置窗口拖动监听器
     */
    fun setOnWindowDragListener(listener: OnWindowDragListener?) {
        this.onWindowDragListener = listener
        AppLog.d("【窗口可视化】设置拖动监听器: ${listener != null}")
    }

    // 🎯 移除onDraw方法，现在使用子View架构，不需要Canvas绘制
    
    // 🎯 移除drawWindowContainer方法，现在使用WindowVisualizationContainerView子View
    
    // 🎯 移除drawDeviceInfoText方法，现在在WindowVisualizationContainerView中处理

    // ========== 📸 截图功能 ==========

    /**
     * 📸 更新截图数据
     * 🎯 重构：更新子View中的截图数据
     */
    fun updateScreenshots(screenshotsData: List<Map<String, Any>>) {
        try {
            AppLog.d("📸 [WindowVisualization] 开始更新截图数据，输入数据数量: ${screenshotsData.size}")

            val newScreenshotMap = mutableMapOf<String, Bitmap>()

            screenshotsData.forEach { screenshotData ->
                val connectionId = screenshotData["connectionId"] as? String
                val screenshotBase64 = screenshotData["imageData"] as? String

                if (connectionId != null && screenshotBase64 != null) {
                    val bitmap = base64ToBitmap(screenshotBase64)
                    if (bitmap != null) {
                        newScreenshotMap[connectionId] = bitmap
                        AppLog.d("📸 [WindowVisualization] 解析截图成功: $connectionId, 尺寸: ${bitmap.width}x${bitmap.height}")
                    }
                }
            }

            screenshotDataMap = newScreenshotMap
            isScreenshotMode = screenshotDataMap.isNotEmpty()

            // 🎯 更新可视化数据，将截图信息添加到WindowVisualizationData中
            updateVisualizationDataWithScreenshots()

            AppLog.d("📸 [WindowVisualization] 截图数据更新完成: ${screenshotDataMap.size} 个截图")

        } catch (e: Exception) {
            AppLog.e("📸 [WindowVisualization] 更新截图数据失败", e)
        }
    }

    /**
     * 🎯 新增：更新可视化数据中的截图信息
     */
    private fun updateVisualizationDataWithScreenshots() {
        // 更新可视化数据，添加截图信息
        visualizationDataList = visualizationDataList.map { data ->
            val screenshot = screenshotDataMap[data.connectionId]
            data.copy(screenshotBitmap = screenshot)
        }

        // 重新更新子View
        updateWindowContainerViews()
    }

    // 🎯 移除calculateScreenshotDestRect方法，现在在WindowVisualizationContainerView中处理

    /**
     * 📸 将Base64字符串转换为Bitmap
     * 🎯 优化：使用高质量解码选项
     */
    private fun base64ToBitmap(base64String: String): Bitmap? {
        return try {
            val decodedBytes = android.util.Base64.decode(base64String, android.util.Base64.NO_WRAP)

            // 🎯 使用BitmapFactory.Options优化解码质量
            val options = BitmapFactory.Options().apply {
                inPreferredConfig = Bitmap.Config.ARGB_8888  // 使用最高质量配置
                // inDither 已弃用，现代Android系统会自动处理抖动
                inScaled = false  // 禁用自动缩放
                inPremultiplied = true  // 启用预乘alpha，提升绘制性能
            }

            val bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size, options)

            if (bitmap != null) {
                AppLog.v("📸 [Base64解码] 成功，尺寸: ${bitmap.width}×${bitmap.height}, 配置: ${bitmap.config}")
            } else {
                AppLog.w("📸 [Base64解码] 失败，返回null")
            }

            bitmap
        } catch (e: Exception) {
            AppLog.e("📸 Base64转Bitmap失败", e)
            null
        }
    }

    /**
     * 🎯 dp转px工具方法（与接收端TransformHandler保持一致）
     */
    private fun dpToPx(dp: Float): Float {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp,
            context.resources.displayMetrics
        )
    }

    // ========== 🎯 触摸事件处理 ==========

    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (!isVisualizationEnabled || visualizationDataList.isEmpty()) {
            return super.onTouchEvent(event)
        }

        // 🎯 如果正在裁剪模式，不处理其他手势
        if (isCropping) {
            return cropOverlay?.onTouchEvent(event) ?: false
        }



        // 🎯 处理缩放和旋转手势
        val scaleHandled = scaleGestureDetector.onTouchEvent(event)
        val rotationHandled = rotationGestureDetector.onTouchEvent(event)

        // 如果正在缩放或旋转，或者刚结束不久，不处理拖动手势
        val currentTime = System.currentTimeMillis()
        if (isScaling || isRotating ||
            (currentTime - scaleEndTime < scaleEndDelay) ||
            (currentTime - rotationEndTime < rotationEndDelay)) {
            return scaleHandled || rotationHandled
        }

        // 🎯 重构：处理拖动手势，现在基于子View架构
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                return handleTouchDownWithSubViews(event.x, event.y)
            }

            MotionEvent.ACTION_MOVE -> {
                return handleTouchMove(event.x, event.y)
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                return handleTouchUp(event.x, event.y)
            }
        }

        return scaleHandled || rotationHandled || super.onTouchEvent(event)
    }

    /**
     * 🎯 新增：基于子View架构的触摸按下事件处理
     */
    private fun handleTouchDownWithSubViews(x: Float, y: Float): Boolean {
        lastTouchX = x
        lastTouchY = y

        // 🎯 通过子View查找被点击的窗口
        val clickedView = findClickedWindowView(x, y)
        if (clickedView != null) {
            // 从子View获取对应的窗口数据
            val clickedWindow = visualizationDataList.find { data ->
                windowContainerViews[data.connectionId] == clickedView
            }

            if (clickedWindow != null) {
                draggedWindowData = clickedWindow

                // 计算拖动偏移（点击位置相对于窗口左上角的偏移）
                dragOffsetX = x - clickedWindow.visualizedX
                dragOffsetY = y - clickedWindow.visualizedY

                AppLog.d("【窗口拖动】检测到点击: ${clickedWindow.getShortConnectionId()}, 偏移: ($dragOffsetX, $dragOffsetY)")
                return true
            }
        }

        return false
    }

    /**
     * 🎯 新增：查找被点击的窗口子View
     */
    private fun findClickedWindowView(x: Float, y: Float): WindowVisualizationContainerView? {
        // 🎯 层级修复：按层级从上到下检测（接收端实际逻辑：zOrder=1是最上层，所以从小到大检测）
        val sortedViews = windowContainerViews.values.sortedBy { view ->
            visualizationDataList.find { data ->
                windowContainerViews[data.connectionId] == view
            }?.zOrder ?: Int.MAX_VALUE
        }

        for (view in sortedViews) {
            // 检查点击是否在View的边界内（考虑View的clipBounds）
            val viewBounds = RectF(view.x, view.y, view.x + view.width, view.y + view.height)
            if (x >= viewBounds.left && x <= viewBounds.right &&
                y >= viewBounds.top && y <= viewBounds.bottom) {

                // 🎯 关键修复：如果View有clipBounds，检查是否在裁剪区域内
                val clipBounds = view.clipBounds
                if (clipBounds != null) {
                    val localX = x - view.x
                    val localY = y - view.y
                    if (localX < clipBounds.left || localX > clipBounds.right ||
                        localY < clipBounds.top || localY > clipBounds.bottom) {
                        // 🎯 修复：触摸点在被裁剪区域内，跳过该窗口继续检测下层窗口
                        AppLog.d("🎯 触摸点在被裁剪区域内，跳过窗口: 触摸点($localX, $localY), 裁剪区域: $clipBounds")
                        continue // 跳过该窗口，继续检测下一个窗口
                    }
                }

                // 🎯 触摸点在可见区域内（未裁剪或在裁剪区域内），返回该窗口
                AppLog.d("🎯 触摸点在可见区域内，选中窗口")
                return view
            }
        }

        return null
    }

    /**
     * 处理触摸移动事件
     */
    private fun handleTouchMove(x: Float, y: Float): Boolean {
        val draggedWindow = draggedWindowData ?: return false

        if (!isDragging) {
            // 检查是否达到拖动阈值
            val deltaX = x - lastTouchX
            val deltaY = y - lastTouchY
            val distance = sqrt(deltaX * deltaX + deltaY * deltaY)

            if (distance > dragThreshold) {
                isDragging = true
                onWindowDragListener?.onDragStart(draggedWindow)
                AppLog.d("【窗口拖动】开始拖动: ${draggedWindow.getShortConnectionId()}")
            }
        }

        if (isDragging) {
            // 计算新位置（减去拖动偏移，确保窗口左上角位置正确）
            val newX = x - dragOffsetX
            val newY = y - dragOffsetY

            // 更新可视化数据中的位置（创建新的数据对象）
            updateDraggedWindowPosition(draggedWindow, newX, newY)

            // 通知拖动监听器
            onWindowDragListener?.onDragMove(draggedWindow, newX, newY)

            // 重绘视图
            invalidate()

            AppLog.v("【窗口拖动】移动到: ($newX, $newY)")
        }

        return isDragging
    }

    /**
     * 处理触摸抬起事件
     */
    private fun handleTouchUp(x: Float, y: Float): Boolean {
        val draggedWindow = draggedWindowData
        val wasDragging = isDragging

        if (wasDragging && draggedWindow != null) {
            // 计算最终位置
            val finalX = x - dragOffsetX
            val finalY = y - dragOffsetY

            // 通知拖动结束
            onWindowDragListener?.onDragEnd(draggedWindow, finalX, finalY)

            AppLog.d("【窗口拖动】结束拖动: ${draggedWindow.getShortConnectionId()}, 最终位置: ($finalX, $finalY)")
        }

        // 重置拖动状态
        isDragging = false
        draggedWindowData = null
        dragOffsetX = 0f
        dragOffsetY = 0f

        return wasDragging
    }

    // 🎯 移除findClickedWindow方法，现在使用findClickedWindowView

    // 🎯 移除isPointInWindow方法，现在使用子View的点击检测

    /**
     * 更新被拖动窗口的位置
     * 🎯 重构：同时更新可视化数据和子View位置
     */
    private fun updateDraggedWindowPosition(originalData: WindowVisualizationData, newX: Float, newY: Float) {
        // 创建新的可视化数据列表，更新被拖动窗口的位置
        visualizationDataList = visualizationDataList.map { data ->
            if (data.connectionId == originalData.connectionId) {
                // 创建新的数据对象，更新位置
                data.copy(
                    visualizedX = newX,
                    visualizedY = newY
                )
            } else {
                data
            }
        }

        // 更新拖动窗口引用
        draggedWindowData = visualizationDataList.find { it.connectionId == originalData.connectionId }

        // 🎯 关键修复：使用与缩放相同的更新机制，确保边框正确同步
        updateWindowContainerViews()

        AppLog.d("【窗口拖动】子View已更新，边框已同步: ${originalData.getShortConnectionId()}, 位置: ($newX, $newY)")
    }

    // ========== 🎯 缩放手势监听器 ==========

    /**
     * 缩放手势监听器
     */
    private inner class ScaleGestureListener : ScaleGestureDetector.OnScaleGestureListener {

        override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
            if (!isVisualizationEnabled || visualizationDataList.isEmpty()) {
                return false
            }

            // 查找被缩放的窗口（基于缩放中心点）
            val focusX = detector.focusX
            val focusY = detector.focusY
            val scaledView = findClickedWindowView(focusX, focusY)
            val scaledWindow = scaledView?.let { view ->
                visualizationDataList.find { data ->
                    windowContainerViews[data.connectionId] == view
                }
            }

            if (scaledWindow != null) {
                isScaling = true
                scaledWindowData = scaledWindow
                initialScaleFactor = 1.0f
                currentScaleFactor = 1.0f

                // 保存原始尺寸
                originalWindowWidth = scaledWindow.visualizedWidth
                originalWindowHeight = scaledWindow.visualizedHeight

                // 🎯 关键修复：使用接收端当前的实际缩放因子作为基础
                // 这样遥控端的相对缩放手势可以正确地基于接收端当前状态进行计算
                baseAbsoluteScaleFactor = scaledWindow.scaleFactor

                AppLog.d("【窗口缩放】接收端当前缩放因子: $baseAbsoluteScaleFactor")
                AppLog.d("【窗口缩放】这将作为遥控端手势缩放的基础值")

                // 通知缩放开始
                onWindowDragListener?.onScaleStart(scaledWindow)

                AppLog.d("【窗口缩放】开始缩放: ${scaledWindow.getShortConnectionId()}, 焦点: ($focusX, $focusY)")
                AppLog.d("【窗口缩放】基础绝对缩放因子: $baseAbsoluteScaleFactor")
                return true
            }

            return false
        }

        override fun onScale(detector: ScaleGestureDetector): Boolean {
            val scaledWindow = scaledWindowData ?: return false

            if (isScaling) {
                // 计算累积缩放因子
                currentScaleFactor *= detector.scaleFactor

                // 限制缩放范围（0.5x 到 3.0x）
                currentScaleFactor = currentScaleFactor.coerceIn(0.5f, 3.0f)

                // 更新可视化数据中的缩放（这里只是视觉效果，不发送消息）
                updateScaledWindowVisualization(scaledWindow, currentScaleFactor)

                // 通知缩放中
                onWindowDragListener?.onScaleMove(scaledWindow, currentScaleFactor)

                // 重绘视图
                invalidate()

                AppLog.v("【窗口缩放】缩放中: ${scaledWindow.getShortConnectionId()}, 因子: $currentScaleFactor")
            }

            return true
        }

        override fun onScaleEnd(detector: ScaleGestureDetector) {
            val scaledWindow = scaledWindowData
            val wasScaling = isScaling

            if (wasScaling && scaledWindow != null) {
                // 🎯 计算最终的绝对缩放因子
                val finalAbsoluteScaleFactor = baseAbsoluteScaleFactor * currentScaleFactor

                // 🎯 获取缩放后的最终位置（从更新后的可视化数据中获取）
                val updatedWindowData = visualizationDataList.find { it.connectionId == scaledWindow.connectionId }
                val finalX = updatedWindowData?.visualizedX ?: scaledWindow.visualizedX
                val finalY = updatedWindowData?.visualizedY ?: scaledWindow.visualizedY

                // 通知缩放结束，传递绝对缩放因子和最终位置
                onWindowDragListener?.onScaleEnd(scaledWindow, finalAbsoluteScaleFactor, finalX, finalY)

                AppLog.d("【窗口缩放】结束缩放: ${scaledWindow.getShortConnectionId()}")
                AppLog.d("【窗口缩放】相对缩放因子: $currentScaleFactor")
                AppLog.d("【窗口缩放】基础绝对缩放因子: $baseAbsoluteScaleFactor")
                AppLog.d("【窗口缩放】最终绝对缩放因子: $finalAbsoluteScaleFactor")
                AppLog.d("【窗口缩放】最终位置: ($finalX, $finalY)")
            }

            // 重置缩放状态
            isScaling = false
            scaledWindowData = null
            initialScaleFactor = 1.0f
            currentScaleFactor = 1.0f
            originalWindowWidth = 0f
            originalWindowHeight = 0f
            baseAbsoluteScaleFactor = 1.0f
            scaleEndTime = System.currentTimeMillis() // 记录缩放结束时间
        }
    }

    /**
     * 更新被缩放窗口的可视化效果
     */
    private fun updateScaledWindowVisualization(originalData: WindowVisualizationData, scaleFactor: Float) {
        // 创建新的可视化数据列表，更新被缩放窗口的尺寸
        visualizationDataList = visualizationDataList.map { data ->
            if (data.connectionId == originalData.connectionId) {
                // 计算新的尺寸（基于保存的原始尺寸）
                val newWidth = originalWindowWidth * scaleFactor
                val newHeight = originalWindowHeight * scaleFactor

                // 计算缩放中心点，保持窗口中心位置不变
                val centerX = data.visualizedX + data.visualizedWidth / 2
                val centerY = data.visualizedY + data.visualizedHeight / 2
                val newX = centerX - newWidth / 2
                val newY = centerY - newHeight / 2

                // 创建新的数据对象，更新尺寸和位置
                data.copy(
                    visualizedX = newX,
                    visualizedY = newY,
                    visualizedWidth = newWidth,
                    visualizedHeight = newHeight
                )
            } else {
                data
            }
        }

        // 更新缩放窗口引用
        scaledWindowData = visualizationDataList.find { it.connectionId == originalData.connectionId }

        // 🎯 关键修复：更新子View以应用缩放效果
        updateWindowContainerViews()

        AppLog.d("【窗口缩放】可视化数据已更新: ${originalData.getShortConnectionId()}, 缩放因子: $scaleFactor")
        AppLog.d("【窗口缩放】子View已更新，缩放效果已应用")
    }

    // ========== 🎯 旋转手势监听器 ==========

    /**
     * 旋转手势监听器
     */
    private inner class RotationGestureListener : RotationGestureDetector.OnRotationGestureListener {

        override fun onRotationBegin(detector: RotationGestureDetector): Boolean {
            if (!isVisualizationEnabled || visualizationDataList.isEmpty()) {
                return false
            }

            // 查找被旋转的窗口（基于旋转中心点）
            val focusX = detector.focusX
            val focusY = detector.focusY
            val rotatedView = findClickedWindowView(focusX, focusY)
            val rotatedWindow = rotatedView?.let { view ->
                visualizationDataList.find { data ->
                    windowContainerViews[data.connectionId] == view
                }
            }

            if (rotatedWindow != null) {
                isRotating = true
                rotatedWindowData = rotatedWindow
                currentRotationAngle = 0f

                // 🎯 记录接收端的基础绝对旋转角度
                baseAbsoluteRotationAngle = rotatedWindow.rotationAngle

                // 通知旋转开始
                onWindowDragListener?.onRotationStart(rotatedWindow)

                AppLog.d("【窗口旋转】开始旋转: ${rotatedWindow.getShortConnectionId()}, 焦点: ($focusX, $focusY)")
                AppLog.d("【窗口旋转】基础绝对旋转角度: $baseAbsoluteRotationAngle")
                return true
            }

            return false
        }

        override fun onRotate(detector: RotationGestureDetector, rotationDelta: Float): Boolean {
            val rotatedWindow = rotatedWindowData ?: return false

            if (isRotating) {
                // 累积旋转角度
                currentRotationAngle += rotationDelta

                // 更新可视化数据中的旋转（这里只是视觉效果，不发送消息）
                updateRotatedWindowVisualization(rotatedWindow, currentRotationAngle)

                // 通知旋转中
                onWindowDragListener?.onRotationMove(rotatedWindow, currentRotationAngle)

                // 重绘视图
                invalidate()

                AppLog.v("【窗口旋转】旋转中: ${rotatedWindow.getShortConnectionId()}, 角度: $currentRotationAngle")
            }

            return true
        }

        override fun onRotationEnd(detector: RotationGestureDetector) {
            val rotatedWindow = rotatedWindowData
            val wasRotating = isRotating

            if (wasRotating && rotatedWindow != null) {
                // 🎯 计算最终的绝对旋转角度
                val finalAbsoluteRotationAngle = baseAbsoluteRotationAngle + currentRotationAngle

                // 🎯 获取旋转后的最终位置（从更新后的可视化数据中获取）
                val updatedWindowData = visualizationDataList.find { it.connectionId == rotatedWindow.connectionId }
                val finalX = updatedWindowData?.visualizedX ?: rotatedWindow.visualizedX
                val finalY = updatedWindowData?.visualizedY ?: rotatedWindow.visualizedY

                // 通知旋转结束，传递绝对旋转角度和最终位置
                onWindowDragListener?.onRotationEnd(rotatedWindow, finalAbsoluteRotationAngle, finalX, finalY)

                AppLog.d("【窗口旋转】结束旋转: ${rotatedWindow.getShortConnectionId()}")
                AppLog.d("【窗口旋转】相对旋转角度: $currentRotationAngle")
                AppLog.d("【窗口旋转】基础绝对旋转角度: $baseAbsoluteRotationAngle")
                AppLog.d("【窗口旋转】最终绝对旋转角度: $finalAbsoluteRotationAngle")
                AppLog.d("【窗口旋转】最终位置: ($finalX, $finalY)")
            }

            // 重置旋转状态
            isRotating = false
            rotatedWindowData = null
            currentRotationAngle = 0f
            baseAbsoluteRotationAngle = 0f
            rotationEndTime = System.currentTimeMillis() // 记录旋转结束时间
        }
    }

    /**
     * 更新被旋转窗口的可视化效果
     */
    private fun updateRotatedWindowVisualization(originalData: WindowVisualizationData, rotationDelta: Float) {
        // 创建新的可视化数据列表，更新被旋转窗口的角度
        visualizationDataList = visualizationDataList.map { data ->
            if (data.connectionId == originalData.connectionId) {
                // 计算新的旋转角度（基于基础角度）
                val newRotationAngle = baseAbsoluteRotationAngle + rotationDelta

                // 创建新的数据对象，更新旋转角度
                data.copy(rotationAngle = newRotationAngle)
            } else {
                data
            }
        }

        // 更新旋转窗口引用
        rotatedWindowData = visualizationDataList.find { it.connectionId == originalData.connectionId }

        // 🎯 关键修复：更新子View以应用旋转效果
        updateWindowContainerViews()

        AppLog.d("【窗口旋转】可视化数据已更新: ${originalData.getShortConnectionId()}, 旋转角度: $rotationDelta")
        AppLog.d("【窗口旋转】子View已更新，旋转效果已应用")
    }

    // ========== 🎯 自定义旋转手势检测器 ==========

    /**
     * 自定义旋转手势检测器（复用TransformHandler的实现）
     */
    private class RotationGestureDetector(private val listener: OnRotationGestureListener) {
        private var prevAngle = 0f
        private var isInProgress = false
        var focusX = 0f
            private set
        var focusY = 0f
            private set

        interface OnRotationGestureListener {
            fun onRotationBegin(detector: RotationGestureDetector): Boolean
            fun onRotate(detector: RotationGestureDetector, rotationDelta: Float): Boolean
            fun onRotationEnd(detector: RotationGestureDetector)
        }

        fun onTouchEvent(event: MotionEvent): Boolean {
            var handled = false
            when (event.action and MotionEvent.ACTION_MASK) {
                MotionEvent.ACTION_POINTER_DOWN -> {
                    if (event.pointerCount == 2) {
                        prevAngle = getAngle(event)
                        focusX = (event.getX(0) + event.getX(1)) / 2f
                        focusY = (event.getY(0) + event.getY(1)) / 2f
                        isInProgress = listener.onRotationBegin(this)
                        handled = true
                    }
                }
                MotionEvent.ACTION_MOVE -> {
                    if (isInProgress && event.pointerCount == 2) {
                        val newAngle = getAngle(event)
                        var angleDelta = newAngle - prevAngle

                        if (angleDelta > 180) angleDelta -= 360
                        if (angleDelta < -180) angleDelta += 360

                        if (abs(angleDelta) > 1f) {
                            listener.onRotate(this, angleDelta)
                            prevAngle = newAngle
                        }

                        focusX = (event.getX(0) + event.getX(1)) / 2f
                        focusY = (event.getY(0) + event.getY(1)) / 2f
                        handled = true
                    }
                }
                MotionEvent.ACTION_POINTER_UP -> {
                    if (isInProgress) {
                        listener.onRotationEnd(this)
                        isInProgress = false
                    }
                    handled = true
                }
            }
            return handled
        }

        private fun getAngle(event: MotionEvent): Float {
            val deltaX = event.getX(0) - event.getX(1)
            val deltaY = event.getY(0) - event.getY(1)
            return Math.toDegrees(atan2(deltaY.toDouble(), deltaX.toDouble())).toFloat()
        }
    }



    // ========== 🎯 裁剪模式管理 ==========

    /**
     * 🎯 新增：公共方法 - 根据连接ID进入裁剪模式
     * @param connectionId 窗口连接ID
     * @return 是否成功进入裁剪模式
     */
    fun enterCropModeByConnectionId(connectionId: String): Boolean {
        val windowData = visualizationDataList.find { it.connectionId == connectionId }
        return if (windowData != null) {
            enterCropMode(windowData)
            true
        } else {
            AppLog.w("【窗口裁剪】未找到连接ID对应的窗口: $connectionId")
            false
        }
    }

    /**
     * 🎯 新增：公共方法 - 退出当前裁剪模式
     * @param isCancel 是否为取消操作
     * @return 是否成功退出裁剪模式
     */
    fun exitCurrentCropMode(isCancel: Boolean = false): Boolean {
        return if (isCropping && croppedWindowData != null) {
            val currentCropRatio = if (!isCancel) {
                // 获取当前裁剪区域
                val windowView = windowContainerViews[croppedWindowData!!.connectionId]
                windowView?.cropOverlay?.getCropRectRatio()
            } else {
                // 取消时恢复原始裁剪状态
                croppedWindowData!!.cropRectRatio
            }
            exitCropMode(isCancel, currentCropRatio)
            true
        } else {
            AppLog.w("【窗口裁剪】当前没有活跃的裁剪模式")
            false
        }
    }

    /**
     * 进入裁剪模式
     * 🎯 重构：使用窗口容器内部的裁剪覆盖层，与接收端保持一致
     */
    private fun enterCropMode(windowData: WindowVisualizationData) {
        if (isCropping) {
            AppLog.d("【窗口裁剪】已经在裁剪模式中")
            return
        }

        isCropping = true
        croppedWindowData = windowData

        // 🎯 关键修复：像接收端一样，临时清除裁剪效果显示完整画面
        // 🎯 修复：保存进入裁剪模式时的原始状态（参考接收端CropManager）
        originalCropRatioForCancel = windowData.cropRectRatio?.let { RectF(it) } // 深拷贝
        val originalCropRatio = originalCropRatioForCancel

        // 临时更新可视化数据，清除裁剪状态以显示完整画面
        visualizationDataList = visualizationDataList.map { data ->
            if (data.connectionId == windowData.connectionId) {
                data.copy(
                    isCropping = false,
                    cropRectRatio = null
                )
            } else {
                data
            }
        }

        // 更新裁剪窗口引用和对应的子View
        croppedWindowData = visualizationDataList.find { it.connectionId == windowData.connectionId }
        val windowView = windowContainerViews[windowData.connectionId]



        if (windowView != null && croppedWindowData != null) {
            // 🎯 修复：在窗口容器内部创建裁剪覆盖层，与接收端保持一致
            windowView.startCropMode(originalCropRatio) { cropRatio, isCancel ->
                if (!isCancel && cropRatio != null) {
                    // 应用裁剪
                    exitCropMode(false, cropRatio)
                } else {
                    // 取消裁剪，恢复原始状态
                    exitCropMode(true, originalCropRatio)
                }
            }

            // 🎯 修复：在父容器中创建控制按钮组，与接收端保持一致
            createCropControlButtonsInParent(croppedWindowData!!)

            // 通知裁剪模式开始
            onWindowDragListener?.onCropModeStart(windowData)

            AppLog.d("【窗口裁剪】进入裁剪模式: ${windowData.getShortConnectionId()}")
        } else {
            AppLog.w("【窗口裁剪】未找到对应的窗口容器View: ${windowData.getShortConnectionId()}")
            isCropping = false
            croppedWindowData = null
        }
    }

    /**
     * 退出裁剪模式
     */
    private fun exitCropMode(isCancel: Boolean, finalCropRatio: RectF?) {
        if (!isCropping) {
            return
        }

        val windowData = croppedWindowData
        if (windowData != null) {
            // 🎯 结束窗口容器的裁剪模式
            val windowView = windowContainerViews[windowData.connectionId]
            windowView?.endCropMode(isCancel)

            // 🎯 修复：正确处理裁剪状态恢复
            if (!isCancel && finalCropRatio != null) {
                // 应用新的裁剪
                updateCroppedWindowVisualization(windowData, finalCropRatio)
                AppLog.d("【窗口裁剪】应用新裁剪: ${windowData.getShortConnectionId()}, 裁剪比例: $finalCropRatio")
            } else {
                // 🎯 关键修复：取消时恢复到进入裁剪模式前的原始状态
                // finalCropRatio 在取消时包含原始裁剪状态
                updateCroppedWindowVisualization(windowData, finalCropRatio)
                AppLog.d("【窗口裁剪】取消裁剪，恢复原始状态: ${windowData.getShortConnectionId()}, 原始裁剪: $finalCropRatio")
            }

            // 通知裁剪模式结束
            onWindowDragListener?.onCropModeEnd(windowData, finalCropRatio, isCancel)
        }

        // 🎯 移除裁剪控制按钮
        removeCropControlButtons()

        // 重置状态
        isCropping = false
        croppedWindowData = null

        AppLog.d("【窗口裁剪】退出裁剪模式: ${windowData?.getShortConnectionId()}, 取消: $isCancel")
    }

    /**
     * 🎯 新增：在父容器中创建裁剪控制按钮组（与接收端CropManager保持一致）
     */
    private fun createCropControlButtonsInParent(windowData: WindowVisualizationData) {
        // 获取父容器（远程控制窗口的根容器）
        val parentContainer = parent as? FrameLayout ?: return

        // 🎯 加载保存的按钮位置（参考接收端CropManager）
        loadButtonPosition(windowData.connectionId)

        // 使用XML布局创建裁剪控制按钮组
        val layoutInflater = android.view.LayoutInflater.from(context)
        cropControlButtons = layoutInflater.inflate(
            R.layout.crop_control_buttons,
            parentContainer,
            false
        ) as android.widget.LinearLayout

        // 🎯 计算按钮组位置（参考接收端CropManager的逻辑）
        val screenWidth = context.resources.displayMetrics.widthPixels
        val screenHeight = context.resources.displayMetrics.heightPixels
        val estimatedButtonWidth = dpToPx(120f).toInt()
        val estimatedButtonHeight = dpToPx(60f).toInt()

        // 如果有保存的位置，使用保存的位置；否则使用屏幕底部中央默认位置
        val buttonX = if (buttonAbsoluteX >= 0) {
            buttonAbsoluteX.coerceIn(0f, (screenWidth.toFloat() - estimatedButtonWidth.toFloat()))
        } else {
            (screenWidth.toFloat() - estimatedButtonWidth.toFloat()) / 2f  // 屏幕水平居中
        }

        val buttonY = if (buttonAbsoluteY >= 0) {
            buttonAbsoluteY.coerceIn(0f, (screenHeight.toFloat() - estimatedButtonHeight.toFloat()))
        } else {
            (screenHeight.toFloat() - estimatedButtonHeight.toFloat() - dpToPx(50f))  // 屏幕底部向上50dp
        }

        // 设置按钮组位置和基本属性
        cropControlButtons?.apply {
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT,
                FrameLayout.LayoutParams.WRAP_CONTENT
            )

            // 使用计算好的绝对位置
            x = buttonX
            y = buttonY
            elevation = 1000f // 确保显示在最上层
        }

        // 🎯 设置拖动功能（参考接收端CropManager）
        setupCropControlButtonsDrag(windowData.connectionId)

        // 设置设备信息显示
        val deviceInfoTextView = cropControlButtons?.findViewById<android.widget.TextView>(R.id.tv_device_info)
        deviceInfoTextView?.text = windowData.getShortConnectionId()

        // 设置按钮点击事件
        setupCropControlButtonsEvents()

        // 添加按钮容器到父容器
        cropControlButtons?.let { buttons ->
            parentContainer.addView(buttons)
            buttons.bringToFront()
            AppLog.d("【窗口裁剪】裁剪控制按钮组已添加到父容器")
        }
    }

    /**
     * 🎯 新增：设置裁剪控制按钮事件
     */
    private fun setupCropControlButtonsEvents() {
        cropControlButtons?.let { buttonsLayout ->
            // 重置按钮
            val resetButton = buttonsLayout.findViewById<android.widget.Button>(R.id.btn_reset)
            resetButton?.setOnClickListener {
                // 重置裁剪区域为全屏
                val windowView = croppedWindowData?.let { windowContainerViews[it.connectionId] }
                windowView?.let { view ->
                    view.cropOverlay?.setCropRectRatio(RectF(0f, 0f, 1f, 1f))
                }
                AppLog.d("【窗口裁剪】点击重置按钮")
            }

            // 取消按钮
            val cancelButton = buttonsLayout.findViewById<android.widget.Button>(R.id.btn_cancel)
            cancelButton?.setOnClickListener {
                // 🎯 修复：取消裁剪，恢复到进入裁剪模式前的原始状态（参考接收端行为）
                exitCropMode(true, originalCropRatioForCancel)
                AppLog.d("【窗口裁剪】点击取消按钮，恢复原始状态: $originalCropRatioForCancel")
            }

            // 应用按钮
            val applyButton = buttonsLayout.findViewById<android.widget.Button>(R.id.btn_apply)
            applyButton?.setOnClickListener {
                // 应用裁剪设置
                val windowView = croppedWindowData?.let { windowContainerViews[it.connectionId] }
                val finalCropRatio = windowView?.cropOverlay?.getCropRectRatio()

                AppLog.d("【窗口裁剪】点击应用按钮")
                AppLog.d("【窗口裁剪】窗口数据: ${croppedWindowData?.getShortConnectionId()}")
                AppLog.d("【窗口裁剪】窗口View: $windowView")
                AppLog.d("【窗口裁剪】裁剪覆盖层: ${windowView?.cropOverlay}")
                AppLog.d("【窗口裁剪】获取的裁剪比例: $finalCropRatio")

                exitCropMode(false, finalCropRatio)
            }
        }
    }

    /**
     * 🎯 新增：设置裁剪控制按钮拖动功能（完全参考接收端CropManager）
     */
    private fun setupCropControlButtonsDrag(connectionId: String) {
        cropControlButtons?.let { buttonsLayout ->
            var isDragging = false
            var lastTouchX = 0f
            var lastTouchY = 0f
            var initialTouchX = 0f
            var initialTouchY = 0f
            val touchSlop = android.view.ViewConfiguration.get(context).scaledTouchSlop

            buttonsLayout.setOnTouchListener { _, event ->
                when (event.action) {
                    android.view.MotionEvent.ACTION_DOWN -> {
                        initialTouchX = event.rawX
                        initialTouchY = event.rawY
                        lastTouchX = event.rawX
                        lastTouchY = event.rawY
                        isDragging = false
                        true
                    }
                    android.view.MotionEvent.ACTION_MOVE -> {
                        val deltaX = kotlin.math.abs(event.rawX - initialTouchX)
                        val deltaY = kotlin.math.abs(event.rawY - initialTouchY)

                        // 如果移动距离超过触摸阈值，开始拖动
                        if (!isDragging && (deltaX > touchSlop || deltaY > touchSlop)) {
                            isDragging = true
                        }

                        if (isDragging) {
                            val moveX = event.rawX - lastTouchX
                            val moveY = event.rawY - lastTouchY

                            buttonsLayout.x += moveX
                            buttonsLayout.y += moveY

                            lastTouchX = event.rawX
                            lastTouchY = event.rawY
                        }
                        true
                    }
                    android.view.MotionEvent.ACTION_UP, android.view.MotionEvent.ACTION_CANCEL -> {
                        if (isDragging) {
                            // 拖动结束，保存当前绝对位置
                            buttonAbsoluteX = buttonsLayout.x
                            buttonAbsoluteY = buttonsLayout.y
                            saveButtonPosition(connectionId)
                            AppLog.d("【窗口裁剪】按钮组拖动结束，保存位置: (${buttonAbsoluteX}, ${buttonAbsoluteY})")
                            isDragging = false
                        }
                        true
                    }
                    else -> false
                }
            }
        }
    }

    /**
     * 🎯 新增：保存按钮组位置到SharedPreferences（参考接收端CropManager）
     */
    private fun saveButtonPosition(connectionId: String) {
        if (connectionId.isEmpty()) return

        val sharedPrefs = context.getSharedPreferences("crop_button_positions", android.content.Context.MODE_PRIVATE)
        sharedPrefs.edit().apply {
            putFloat("${connectionId}_absolute_x", buttonAbsoluteX)
            putFloat("${connectionId}_absolute_y", buttonAbsoluteY)
            apply()
        }
        AppLog.d("【窗口裁剪】保存按钮组位置: $connectionId, 绝对坐标=(${buttonAbsoluteX}, ${buttonAbsoluteY})")
    }

    /**
     * 🎯 新增：从SharedPreferences加载按钮组位置（参考接收端CropManager）
     */
    private fun loadButtonPosition(connectionId: String) {
        if (connectionId.isEmpty()) return

        val sharedPrefs = context.getSharedPreferences("crop_button_positions", android.content.Context.MODE_PRIVATE)
        buttonAbsoluteX = sharedPrefs.getFloat("${connectionId}_absolute_x", -1f)
        buttonAbsoluteY = sharedPrefs.getFloat("${connectionId}_absolute_y", -1f)
        AppLog.d("【窗口裁剪】加载按钮组位置: $connectionId, 绝对坐标=(${buttonAbsoluteX}, ${buttonAbsoluteY})")
    }

    /**
     * 🎯 新增：移除裁剪控制按钮
     */
    private fun removeCropControlButtons() {
        cropControlButtons?.let { buttons ->
            // 从父容器中移除按钮
            val parentContainer = parent as? FrameLayout
            parentContainer?.removeView(buttons)
        }
        cropControlButtons = null
    }

    /**
     * 🎯 更新被裁剪窗口的可视化效果
     */
    private fun updateCroppedWindowVisualization(originalData: WindowVisualizationData, cropRatio: RectF?) {
        // 创建新的可视化数据列表，更新被裁剪窗口的裁剪状态
        visualizationDataList = visualizationDataList.map { data ->
            if (data.connectionId == originalData.connectionId) {
                // 创建新的数据对象，更新裁剪状态
                data.copy(
                    isCropping = cropRatio != null,
                    cropRectRatio = cropRatio
                )
            } else {
                data
            }
        }

        // 更新裁剪窗口引用
        croppedWindowData = visualizationDataList.find { it.connectionId == originalData.connectionId }

        // 🎯 关键修复：更新子View以应用裁剪效果
        updateWindowContainerViews()

        AppLog.d("【窗口裁剪】可视化数据已更新: ${originalData.getShortConnectionId()}, 裁剪状态: ${cropRatio != null}")
        AppLog.d("【窗口裁剪】子View已更新，裁剪效果已应用")
    }

    /**
     * 获取当前是否在裁剪模式
     */
    fun isCroppingMode(): Boolean = isCropping

    /**
     * 🎯 新增：获取当前可视化数据列表
     * 用于外部更新可视化数据
     */
    fun getVisualizationDataList(): List<WindowVisualizationData> = visualizationDataList.toList()
}
