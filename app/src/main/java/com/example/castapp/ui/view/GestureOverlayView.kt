package com.example.castapp.ui.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.PointF
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import com.example.castapp.utils.AppLog
import kotlin.math.*

/**
 * 手势覆盖层视图 - 简化版
 * 专门用于检测"C"字母手势
 */
class GestureOverlayView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 手势轨迹点
    private val gesturePoints = mutableListOf<PointF>()
    private var isDrawing = false

    // 回调接口
    interface OnGestureListener {
        fun onCLetterDetected()
    }

    private var gestureListener: OnGestureListener? = null

    fun setOnGestureListener(listener: OnGestureListener) {
        this.gestureListener = listener
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // 不绘制轨迹，保持隐形检测
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        val x = event.x
        val y = event.y

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                startGesture(x, y)
                return true
            }
            MotionEvent.ACTION_MOVE -> {
                continueGesture(x, y)
                return true
            }
            MotionEvent.ACTION_UP -> {
                endGesture(x, y)
                performClick()
                return true
            }
        }
        return false
    }

    override fun performClick(): Boolean {
        super.performClick()
        return true
    }

    private fun startGesture(x: Float, y: Float) {
        isDrawing = true
        gesturePoints.clear()
        gesturePoints.add(PointF(x, y))

        // 不再需要绘制路径，只记录手势点用于检测
        AppLog.d("【手势覆盖层】开始手势: ($x, $y)")
    }

    private fun continueGesture(x: Float, y: Float) {
        if (!isDrawing) return

        // 只有当点与上一个点有一定距离时才添加
        val lastPoint = gesturePoints.lastOrNull()
        if (lastPoint != null) {
            val distance = sqrt((x - lastPoint.x).pow(2) + (y - lastPoint.y).pow(2))
            if (distance > 8) { // 最小距离阈值
                gesturePoints.add(PointF(x, y))
                // 不再需要绘制路径和刷新显示
            }
        } else {
            // 如果没有上一个点，说明状态异常，重新开始
            startGesture(x, y)
        }
    }

    private fun endGesture(x: Float, y: Float) {
        if (!isDrawing) return

        gesturePoints.add(PointF(x, y))
        isDrawing = false

        AppLog.d("【手势覆盖层】完成手势，点数: ${gesturePoints.size}")

        // 检测C字母
        if (detectCLetter()) {
            AppLog.d("【手势覆盖层】检测到C字母！")
            gestureListener?.onCLetterDetected()
        }

        // 立即清除手势数据，不需要延迟
        clearGesture()
    }

    /**
     * 平衡的C字母检测算法 - 既准确又不过分严格
     */
    private fun detectCLetter(): Boolean {
        if (gesturePoints.size < 6) {
            AppLog.d("【C字母检测】点数太少: ${gesturePoints.size}")
            return false
        }

        // 计算边界框
        val minX = gesturePoints.minOf { it.x }
        val maxX = gesturePoints.maxOf { it.x }
        val minY = gesturePoints.minOf { it.y }
        val maxY = gesturePoints.maxOf { it.y }

        val width = maxX - minX
        val height = maxY - minY

        // 1. 基本尺寸要求（放宽）
        if (width < 40 || height < 60) {
            AppLog.d("【C字母检测】尺寸太小: width=$width, height=$height")
            return false
        }

        // 2. 宽高比检查（放宽范围）
        val aspectRatio = height / width
        if (aspectRatio < 1.0 || aspectRatio > 4.0) {
            AppLog.d("【C字母检测】宽高比不符合: $aspectRatio (应在1.0-4.0之间)")
            return false
        }

        val startPoint = gesturePoints.first()
        val endPoint = gesturePoints.last()
        val centerX = (minX + maxX) / 2

        // 3. 基本的开口方向检查（放宽）
        val rightSide = centerX - width * 0.1f  // 放宽到只需要在中线右侧一点点
        if (startPoint.x < rightSide || endPoint.x < rightSide) {
            AppLog.d("【C字母检测】起终点不在右侧")
            return false
        }

        // 4. 简化的弧形检查
        if (!checkSimpleCurve(gesturePoints, centerX)) {
            AppLog.d("【C字母检测】不是弧形")
            return false
        }

        // 5. 排除明显的封闭图形（放宽条件）
        val startEndDistance = sqrt((endPoint.x - startPoint.x).pow(2) + (endPoint.y - startPoint.y).pow(2))
        if (startEndDistance < width * 0.2f) {  // 从30%放宽到20%
            AppLog.d("【C字母检测】起终点太近，可能是封闭图形")
            return false
        }

        // 6. 基本的方向检查（简化）
        val hasDownwardTrend = endPoint.y > startPoint.y + height * 0.2f  // 从30%放宽到20%
        if (!hasDownwardTrend) {
            AppLog.d("【C字母检测】没有向下的趋势")
            return false
        }

        AppLog.d("【C字母检测】通过检测！宽高比=$aspectRatio, 尺寸=${width}x${height}")
        return true
    }

    /**
     * 简化的弧形检查
     */
    private fun checkSimpleCurve(points: List<PointF>, centerX: Float): Boolean {
        // 找到最左边的点
        val leftmostPoint = points.minByOrNull { it.x } ?: return false

        // 检查最左边的点是否在中间部分（不是起点或终点）
        val startPoint = points.first()
        val endPoint = points.last()

        // 最左边的点不应该是起点或终点
        val isLeftmostNotEndpoint = leftmostPoint != startPoint && leftmostPoint != endPoint

        // 最左边的点应该明显在中心线左侧
        val isLeftOfCenter = leftmostPoint.x < centerX - (centerX - points.minOf { it.x }) * 0.3f

        return isLeftmostNotEndpoint && isLeftOfCenter
    }



    /**
     * 清除手势数据
     */
    fun clearGesture() {
        // 只需要清除手势点数据，不需要处理绘制路径
        gesturePoints.clear()
        isDrawing = false  // 确保绘制状态重置
        AppLog.d("【手势覆盖层】清除手势数据")
    }
}
