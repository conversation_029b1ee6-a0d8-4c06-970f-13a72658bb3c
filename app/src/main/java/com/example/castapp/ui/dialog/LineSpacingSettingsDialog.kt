package com.example.castapp.ui.dialog

import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.utils.AppLog
import com.example.castapp.utils.LineSpacingPresetManager
import com.example.castapp.utils.ToastUtils

/**
 * 行间距设置对话框
 * 提供行间距管理功能，包括添加、删除、选择行间距
 */
class LineSpacingSettingsDialog(
    private val context: Context,
    private val currentLineSpacing: Float,
    private val existingLineSpacings: List<Float>,
    private val onLineSpacingSelected: (Float) -> Unit,
    private val onLineSpacingAdded: ((Float) -> Unit)? = null,
    private val onLineSpacingDeleted: ((Float) -> Unit)? = null,
    private val onResetToDefault: (() -> Unit)? = null
) {

    private var dialog: Dialog? = null
    private lateinit var recyclerView: RecyclerView
    private lateinit var lineSpacingAdapter: LineSpacingAdapter
    private lateinit var btnClose: ImageView
    private lateinit var btnAddLineSpacing: Button
    private lateinit var btnResetDefault: Button
    private lateinit var tvCurrentLineSpacing: TextView
    private lateinit var etNewLineSpacing: EditText

    // 预设行间距列表
    private val presetLineSpacings = LineSpacingPresetManager.PRESET_LINE_SPACINGS

    // 行间距列表数据
    private val lineSpacingList = mutableListOf<LineSpacingItem>()
    private var selectedLineSpacing = currentLineSpacing

    /**
     * 行间距项数据类
     */
    data class LineSpacingItem(
        val lineSpacing: Float,
        val isPreset: Boolean,
        var isSelected: Boolean
    )

    /**
     * 显示对话框
     */
    fun show() {
        try {
            // 创建对话框
            dialog = Dialog(context)
            val view = LayoutInflater.from(context).inflate(R.layout.dialog_line_spacing_settings, null)
            dialog?.setContentView(view)

            // 初始化视图
            initViews(view)
            setupRecyclerView()
            setupClickListeners()
            loadLineSpacingList()
            updateCurrentLineSpacingDisplay()

            // 显示对话框
            dialog?.show()

            AppLog.d("【行间距设置对话框】对话框已显示，当前行间距: ${currentLineSpacing}dp")

        } catch (e: Exception) {
            AppLog.e("【行间距设置对话框】显示对话框失败", e)
            ToastUtils.showToast(context, "显示行间距设置对话框失败")
        }
    }

    /**
     * 初始化视图
     */
    private fun initViews(view: View) {
        recyclerView = view.findViewById(R.id.rv_line_spacing_list)
        btnClose = view.findViewById(R.id.btn_close)
        btnAddLineSpacing = view.findViewById(R.id.btn_add_line_spacing)
        btnResetDefault = view.findViewById(R.id.btn_reset_default)
        tvCurrentLineSpacing = view.findViewById(R.id.tv_current_line_spacing)
        etNewLineSpacing = view.findViewById(R.id.et_new_line_spacing)
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        lineSpacingAdapter = LineSpacingAdapter()
        recyclerView.layoutManager = LinearLayoutManager(context)
        recyclerView.adapter = lineSpacingAdapter
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        // 关闭按钮
        btnClose.setOnClickListener {
            dialog?.dismiss()
        }

        // 添加行间距按钮
        btnAddLineSpacing.setOnClickListener {
            addNewLineSpacing()
        }

        // 重置默认按钮
        btnResetDefault.setOnClickListener {
            resetToDefault()
        }
    }

    /**
     * 加载行间距列表
     */
    private fun loadLineSpacingList() {
        lineSpacingList.clear()

        existingLineSpacings.forEach { lineSpacing ->
            val isPreset = presetLineSpacings.contains(lineSpacing)
            val isSelected = lineSpacing == selectedLineSpacing
            lineSpacingList.add(LineSpacingItem(lineSpacing, isPreset, isSelected))
        }

        lineSpacingAdapter.notifyDataSetChanged()
        AppLog.d("【行间距设置对话框】行间距列表已加载，共${lineSpacingList.size}项")
    }

    /**
     * 更新当前行间距显示
     */
    private fun updateCurrentLineSpacingDisplay() {
        tvCurrentLineSpacing.text = "当前行间距: ${selectedLineSpacing}dp"
    }

    /**
     * 添加新行间距
     */
    private fun addNewLineSpacing() {
        try {
            val lineSpacingText = etNewLineSpacing.text.toString().trim()
            if (lineSpacingText.isEmpty()) {
                ToastUtils.showToast(context, "请输入行间距值")
                return
            }

            val lineSpacing = lineSpacingText.toFloat()

            // 检查范围
            if (lineSpacing < 0.0f || lineSpacing > 10.0f) {
                ToastUtils.showToast(context, "行间距范围应在0.0-10.0之间")
                return
            }

            // 检查是否已存在（检查内部列表）
            if (lineSpacingList.any { it.lineSpacing == lineSpacing }) {
                ToastUtils.showToast(context, "行间距${lineSpacing}dp已存在")
                return
            }

            // 添加到内部列表
            val isPreset = presetLineSpacings.contains(lineSpacing)
            lineSpacingList.add(LineSpacingItem(lineSpacing, isPreset, false))
            lineSpacingList.sortBy { it.lineSpacing }

            // 通知适配器更新
            lineSpacingAdapter.notifyDataSetChanged()

            // 清空输入框
            etNewLineSpacing.setText("")

            // 回调通知外部
            onLineSpacingAdded?.invoke(lineSpacing)

            ToastUtils.showToast(context, "行间距${lineSpacing}dp已添加")
            AppLog.d("【行间距设置对话框】已添加新行间距: ${lineSpacing}dp")

        } catch (e: NumberFormatException) {
            ToastUtils.showToast(context, "请输入有效的数字")
        } catch (e: Exception) {
            AppLog.e("【行间距设置对话框】添加行间距失败", e)
            ToastUtils.showToast(context, "添加行间距失败")
        }
    }

    /**
     * 选择行间距
     */
    private fun selectLineSpacing(lineSpacing: Float) {
        try {
            // 更新选中状态
            lineSpacingList.forEach { it.isSelected = (it.lineSpacing == lineSpacing) }
            selectedLineSpacing = lineSpacing

            // 更新显示
            updateCurrentLineSpacingDisplay()
            lineSpacingAdapter.notifyDataSetChanged()

            // 回调通知
            onLineSpacingSelected(lineSpacing)

            AppLog.d("【行间距设置对话框】已选择行间距: ${lineSpacing}dp")

        } catch (e: Exception) {
            AppLog.e("【行间距设置对话框】选择行间距失败", e)
        }
    }

    /**
     * 删除行间距
     */
    private fun deleteLineSpacing(lineSpacing: Float) {
        try {
            // 检查是否为预设行间距
            if (presetLineSpacings.contains(lineSpacing)) {
                ToastUtils.showToast(context, "无法删除预设行间距")
                return
            }

            // 从内部列表中删除
            val itemToRemove = lineSpacingList.find { it.lineSpacing == lineSpacing }
            if (itemToRemove != null) {
                lineSpacingList.remove(itemToRemove)

                // 如果删除的是当前选中的行间距，选择默认行间距
                if (lineSpacing == selectedLineSpacing) {
                    selectLineSpacing(0.0f) // 选择默认行间距
                }

                // 通知适配器更新
                lineSpacingAdapter.notifyDataSetChanged()
            }

            // 回调通知外部
            onLineSpacingDeleted?.invoke(lineSpacing)

            ToastUtils.showToast(context, "行间距${lineSpacing}dp已删除")
            AppLog.d("【行间距设置对话框】已删除行间距: ${lineSpacing}dp")

        } catch (e: Exception) {
            AppLog.e("【行间距设置对话框】删除行间距失败", e)
            ToastUtils.showToast(context, "删除行间距失败")
        }
    }

    /**
     * 重置为默认设置
     */
    private fun resetToDefault() {
        try {
            // 回调通知外部
            onResetToDefault?.invoke()

            // 重新加载列表（只保留预设行间距）
            lineSpacingList.clear()
            presetLineSpacings.forEach { lineSpacing ->
                val isSelected = lineSpacing == selectedLineSpacing
                lineSpacingList.add(LineSpacingItem(lineSpacing, true, isSelected))
            }

            // 通知适配器更新
            lineSpacingAdapter.notifyDataSetChanged()

            ToastUtils.showToast(context, "已重置为默认行间距设置")
            AppLog.d("【行间距设置对话框】已重置为默认设置")

        } catch (e: Exception) {
            AppLog.e("【行间距设置对话框】重置默认设置失败", e)
            ToastUtils.showToast(context, "重置失败")
        }
    }

    /**
     * 行间距适配器
     */
    private inner class LineSpacingAdapter : RecyclerView.Adapter<LineSpacingAdapter.ViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(context).inflate(R.layout.item_line_spacing, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            holder.bind(lineSpacingList[position])
        }

        override fun getItemCount(): Int = lineSpacingList.size

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            private val tvLineSpacing: TextView = itemView.findViewById(R.id.tv_line_spacing)
            private val tvPresetTag: TextView = itemView.findViewById(R.id.tv_preset_tag)
            private val ivCurrentIndicator: ImageView = itemView.findViewById(R.id.iv_current_indicator)
            private val btnDelete: ImageView = itemView.findViewById(R.id.btn_delete)

            fun bind(item: LineSpacingItem) {
                tvLineSpacing.text = "${item.lineSpacing}dp"

                // 显示预设标识
                tvPresetTag.visibility = if (item.isPreset) View.VISIBLE else View.GONE

                // 显示当前选中标识
                ivCurrentIndicator.visibility = if (item.isSelected) View.VISIBLE else View.GONE

                // 显示删除按钮（只有自定义行间距才显示）
                btnDelete.visibility = if (!item.isPreset) View.VISIBLE else View.GONE

                // 设置背景色
                itemView.setBackgroundColor(
                    if (item.isSelected) 0x20_4CAF50 else 0x00_000000
                )

                // 点击选择行间距
                itemView.setOnClickListener {
                    selectLineSpacing(item.lineSpacing)
                }

                // 删除按钮点击
                btnDelete.setOnClickListener {
                    deleteLineSpacing(item.lineSpacing)
                }
            }
        }
    }
}
