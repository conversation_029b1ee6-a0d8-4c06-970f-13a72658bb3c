package com.example.castapp.ui.view

import android.content.Context
import android.graphics.*
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import android.view.ViewOutlineProvider
import android.widget.FrameLayout
import androidx.core.graphics.toColorInt
import androidx.core.graphics.withSave
import com.example.castapp.model.WindowVisualizationData
import com.example.castapp.utils.AppLog
import kotlin.math.roundToInt

/**
 * 🪟 单个窗口容器可视化View
 * 使用View.clipBounds实现裁剪，与接收端CropManager保持一致
 * 🎯 修复：改为FrameLayout以支持裁剪覆盖层
 */
class WindowVisualizationContainerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    // 窗口数据
    private var windowData: WindowVisualizationData? = null

    // 🎯 新增：裁剪模式相关
    var cropOverlay: CropOverlayView? = null // 公开访问，供父容器使用
        private set
    private var isCropping = false
    private var cropModeCallback: ((RectF?, Boolean) -> Unit)? = null

    // 🎯 修复：使用WeakReference避免内存泄漏和并发问题
    private var borderViewRef: java.lang.ref.WeakReference<View>? = null
    private var isDetached = false

    // 🎯 边框状态保存（用于裁剪模式恢复，参考接收端行为）
    private var borderStateBeforeCrop = false

    // 📝 文本窗口View相关
    private var textWindowView: com.example.castapp.ui.view.TextWindowView? = null
    
    // 绘制相关
    private val windowFillPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
    }
    
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        textAlign = Paint.Align.CENTER
        textSize = dpToPx(10f)
        color = Color.WHITE
    }

    /**
     * 设置窗口数据并更新显示
     */
    fun setWindowData(data: WindowVisualizationData) {
        setWindowDataInternal(data, forceRefresh = false)
    }

    /**
     * 内部窗口数据设置方法，支持强制刷新
     */
    private fun setWindowDataInternal(data: WindowVisualizationData, forceRefresh: Boolean = false) {
        this.windowData = data

        // 🎯 关键：使用View.clipBounds实现裁剪，与接收端保持一致
        applyClipBounds(data)

        // 设置View的位置和尺寸
        val bounds = data.getVisualizationBounds()

        // 🎯 修复：确保layoutParams不为null
        if (layoutParams == null) {
            layoutParams = FrameLayout.LayoutParams(
                bounds.width().roundToInt(),
                bounds.height().roundToInt()
            )
        } else {
            layoutParams = layoutParams.apply {
                width = bounds.width().roundToInt()
                height = bounds.height().roundToInt()
            }
        }

        // 设置位置
        x = bounds.left
        y = bounds.top

        // 设置旋转
        rotation = data.rotationAngle

        // 设置透明度
        alpha = data.alpha

        invalidate()

        AppLog.d("【窗口容器View】设置窗口数据: ${data.getShortConnectionId()}")
        AppLog.d("  位置: (${bounds.left}, ${bounds.top})")
        AppLog.d("  尺寸: ${bounds.width().roundToInt()}×${bounds.height().roundToInt()}")
        AppLog.d("  裁剪状态: ${data.cropRectRatio != null}")
        AppLog.d("  镜像状态: ${data.isMirrored}")
        AppLog.d("  截图数据: ${if (data.screenshotBitmap != null) "有截图 ${data.screenshotBitmap!!.width}×${data.screenshotBitmap!!.height}" else "无截图"}")

        // 🎯 添加边框状态调试日志
        AppLog.d("【窗口容器View】边框状态:")
        AppLog.d("  边框启用: ${data.isBorderEnabled}")
        AppLog.d("  边框颜色: ${String.format("#%08X", data.borderColor)}")
        AppLog.d("  边框宽度: ${data.borderWidth}dp")

        // 🎯 关键修复：使用独立边框View，与接收端保持一致
        updateBorderView(data)

        // 📝 处理文本窗口View的创建或更新
        handleTextWindowView(data)

        // 强制刷新（用于裁剪模式的完整重建）
        if (forceRefresh) {
            invalidate()
            requestLayout()

            // 刷新所有子View
            for (i in 0 until childCount) {
                getChildAt(i)?.let { child ->
                    child.invalidate()
                    child.requestLayout()
                }
            }

            AppLog.d("【窗口容器View】强制刷新完成: ${data.getShortConnectionId()}")
        }
    }

    /**
     * 🎯 新增：更新窗口位置（拖动时调用，同步更新边框位置）
     */
    fun updatePosition(newX: Float, newY: Float) {
        // 更新容器位置
        x = newX
        y = newY

        // 🎯 关键：同步更新边框View位置
        updateBorderPosition()

        AppLog.v("【窗口容器View】位置已更新: ($newX, $newY), 边框已同步")
    }


    
    /**
     * 🎯 应用View.clipBounds裁剪，与接收端CropManager保持一致
     */
    private fun applyClipBounds(data: WindowVisualizationData) {
        val cropRatio = data.cropRectRatio
        // 🎯 关键修复：考虑缩放因子对圆角半径的影响，与接收端保持一致
        val cornerRadiusPx = dpToPx(data.cornerRadius * data.scaleFactor)

        if (cropRatio != null) {
            // 🎯 统一方案：直接设置View的clipBounds（与接收端CropManager相同）
            val bounds = data.getVisualizationBounds()
            val clipBounds = Rect(
                (cropRatio.left * bounds.width()).toInt(),
                (cropRatio.top * bounds.height()).toInt(),
                (cropRatio.right * bounds.width()).toInt(),
                (cropRatio.bottom * bounds.height()).toInt()
            )

            this.clipBounds = clipBounds

            // 🎯 关键修复：裁剪状态下同时使用clipBounds和圆角裁剪（与接收端保持一致）
            // clipBounds负责矩形区域裁剪，outline负责圆角裁剪
            outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    // 🎯 关键：基于clipBounds区域设置圆角outline
                    outline.setRoundRect(
                        clipBounds.left,
                        clipBounds.top,
                        clipBounds.right,
                        clipBounds.bottom,
                        cornerRadiusPx
                    )
                }
            }
            clipToOutline = true // 🎯 启用圆角裁剪

            AppLog.d("【窗口容器View】应用View.clipBounds裁剪: ${data.getShortConnectionId()}")
            AppLog.d("  裁剪比例: left=${cropRatio.left}, top=${cropRatio.top}, right=${cropRatio.right}, bottom=${cropRatio.bottom}")
            AppLog.d("  裁剪区域: $clipBounds")
            AppLog.d("  圆角半径: ${data.cornerRadius}dp × ${data.scaleFactor} = ${data.cornerRadius * data.scaleFactor}dp (${cornerRadiusPx}px)")
        } else {
            // 清除裁剪，但保持圆角
            this.clipBounds = null

            // 🎯 未裁剪状态：设置基于整个容器的圆角outline
            outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    outline.setRoundRect(0, 0, view.width, view.height, cornerRadiusPx)
                }
            }
            clipToOutline = true

            AppLog.d("【窗口容器View】移除裁剪: ${data.getShortConnectionId()}")
            AppLog.d("  圆角半径: ${data.cornerRadius}dp × ${data.scaleFactor} = ${data.cornerRadius * data.scaleFactor}dp (${cornerRadiusPx}px)")
        }
    }

    /**
     * 📝 处理文本窗口View的创建或更新
     */
    private fun handleTextWindowView(data: WindowVisualizationData) {
        if (data.isTextWindow() && data.hasTextContent()) {
            // 文本窗口：创建或更新TextView
            if (textWindowView == null) {
                createTextWindowView(data)
            } else {
                // 更新现有的TextView内容
                updateTextWindowView(data)
            }
        } else {
            // 非文本窗口：移除TextView（如果存在）
            removeTextWindowView()
        }
    }

    /**
     * 📝 创建文本窗口View
     */
    private fun createTextWindowView(data: WindowVisualizationData) {
        try {
            // 防止重复创建
            if (textWindowView != null) {
                AppLog.w("【窗口容器View】文本窗口View已存在，跳过创建: ${data.getShortConnectionId()}")
                return
            }

            // 创建新的TextWindowView
            val newTextView = com.example.castapp.ui.view.TextWindowView(context).apply {
                // 设置布局参数，填充整个容器
                layoutParams = FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.MATCH_PARENT
                )

                // 设置为只读模式（遥控端不支持编辑）
                isEnabled = false
                isFocusable = false
                isFocusableInTouchMode = false

                // 📝 先设置文本内容，再应用格式（富文本反序列化需要文本内容）
                data.textContent?.let { content ->
                    setText(content) // 设置文本内容
                }

                // 应用格式信息（包括富文本格式）
                data.textFormatData?.let { formatData ->
                    applyTextFormat(formatData)
                }

                AppLog.d("【窗口容器View】文本窗口View已创建: ${data.getShortConnectionId()}")
            }

            // 添加到容器
            addView(newTextView)
            textWindowView = newTextView

            AppLog.d("【窗口容器View】文本窗口View已添加到容器: ${data.getShortConnectionId()}")

        } catch (e: Exception) {
            AppLog.e("【窗口容器View】创建文本窗口View失败: ${data.getShortConnectionId()}", e)
        }
    }

    /**
     * 📝 应用文本格式到TextWindowView
     */
    private fun com.example.castapp.ui.view.TextWindowView.applyTextFormat(formatData: Map<String, Any>) {
        try {
            // 🎯 新方案：检查是否有每行文本数据（最精准的换行同步方案）
            AppLog.d("【窗口容器View】🔍 调试完整formatData: ${formatData.keys}")

            val actualLineTexts = formatData["actualLineTexts"] as? List<*>
            if (actualLineTexts != null && actualLineTexts.isNotEmpty()) {
                // 转换为字符串列表
                val lineTexts = actualLineTexts.mapNotNull { it as? String }
                if (lineTexts.isNotEmpty()) {
                    AppLog.d("【窗口容器View】🎯 使用接收端实际每行文本进行精准换行同步: ${lineTexts.size}行")
                    lineTexts.forEachIndexed { index, lineText ->
                        AppLog.d("【窗口容器View】  第${index + 1}行: \"$lineText\"")
                    }

                    // 🎯 关键修复：检查是否为富文本，如果是富文本则需要保持格式
                    val hasRichText = formatData["hasRichText"] as? Boolean ?: false
                    if (hasRichText) {
                        // 富文本情况：先应用富文本格式，再调整换行
                        val richTextData = formatData["richTextData"] as? String
                        if (!richTextData.isNullOrBlank()) {
                            try {
                                val textFormatManager = com.example.castapp.utils.TextFormatManager(context)
                                val spannableString = textFormatManager.deserializeSpannableStringFromJson(text.toString(), richTextData)

                                if (spannableString != null) {
                                    // 🎯 创建新的SpannableString，按行重新组织但精确保持格式
                                    val combinedText = lineTexts.joinToString("\n")
                                    val newSpannable = android.text.SpannableString(combinedText)

                                    // 🎯 精确映射格式：根据原始文本和新文本的字符对应关系来映射格式
                                    val originalText = spannableString.toString()
                                    val spans = spannableString.getSpans(0, spannableString.length, Any::class.java)

                                    for (span in spans) {
                                        try {
                                            val spanStart = spannableString.getSpanStart(span)
                                            val spanEnd = spannableString.getSpanEnd(span)
                                            val spanFlags = spannableString.getSpanFlags(span)

                                            // 🎯 关键修复：精确映射span位置到新文本
                                            val mappedPositions = mapSpanPositionToNewText(
                                                originalText, combinedText, spanStart, spanEnd
                                            )

                                            if (mappedPositions != null) {
                                                val (newStart, newEnd) = mappedPositions
                                                if (newStart < newEnd && newEnd <= newSpannable.length) {
                                                    newSpannable.setSpan(span, newStart, newEnd, spanFlags)
                                                    AppLog.d("【窗口容器View】🎯 格式映射: ${span.javaClass.simpleName} 原始($spanStart-$spanEnd) -> 新($newStart-$newEnd)")
                                                }
                                            }
                                        } catch (e: Exception) {
                                            AppLog.w("【窗口容器View】应用格式span失败: ${span.javaClass.simpleName}", e)
                                        }
                                    }

                                    setRichTextContent(newSpannable)
                                    AppLog.d("【窗口容器View】🎯 富文本精准换行同步完成，格式已保持")
                                } else {
                                    // 富文本反序列化失败，使用纯文本
                                    setLineTexts(lineTexts)
                                    AppLog.d("【窗口容器View】🎯 富文本反序列化失败，使用纯文本精准换行")
                                }
                            } catch (e: Exception) {
                                AppLog.e("【窗口容器View】富文本精准换行处理失败，使用纯文本", e)
                                setLineTexts(lineTexts)
                            }
                        } else {
                            setLineTexts(lineTexts)
                        }
                    } else {
                        // 基本文本情况：直接设置每行文本
                        setLineTexts(lineTexts)
                        AppLog.d("【窗口容器View】🎯 基本文本精准换行同步完成")
                    }

                    // 应用其他格式（背景颜色、行间距、对齐等）
                    applyOtherFormats(formatData)

                    AppLog.d("【窗口容器View】🎯 精准换行同步完成")
                    return
                }
            }

            AppLog.d("【窗口容器View】🔍 未找到每行文本数据，使用传统方式处理")
            val hasRichText = formatData["hasRichText"] as? Boolean ?: false

            if (hasRichText) {
                // 📝 处理富文本格式
                val richTextData = formatData["richTextData"] as? String
                if (!richTextData.isNullOrBlank()) {
                    try {
                        // 使用TextFormatManager反序列化富文本
                        val textFormatManager = com.example.castapp.utils.TextFormatManager(context)
                        val spannableString = textFormatManager.deserializeSpannableStringFromJson(text.toString(), richTextData)

                        if (spannableString != null) {
                            setRichTextContent(spannableString)

                            // 🎨 富文本格式应用后，也要处理基本格式信息
                            val lineSpacing = when (val spacingValue = formatData["lineSpacing"]) {
                                is Float -> spacingValue
                                is Double -> spacingValue.toFloat()
                                is Number -> spacingValue.toFloat()
                                else -> 0.0f
                            }
                            val textAlignment = when (val alignmentValue = formatData["textAlignment"]) {
                                is Int -> alignmentValue
                                is Double -> alignmentValue.toInt()
                                is Float -> alignmentValue.toInt()
                                is Number -> alignmentValue.toInt()
                                else -> 0
                            }

                            // 应用行间距（与接收端保持一致的应用方式）
                            if (lineSpacing > 0.0f) {
                                val lineSpacingExtra = lineSpacing * resources.displayMetrics.density
                                setLineSpacing(lineSpacingExtra, 1.0f)
                                AppLog.d("【窗口容器View】富文本行间距已应用: ${lineSpacing}dp")
                            }

                            // 应用文本对齐（直接使用接收端传输的Gravity值）
                            gravity = textAlignment
                            AppLog.d("【窗口容器View】富文本对齐已应用: $textAlignment")

                            // 🎨 富文本格式应用后，也要处理窗口背景颜色
                            val windowColorEnabled = formatData["windowColorEnabled"] as? Boolean ?: false
                            val windowBackgroundColor = when (val colorValue = formatData["windowBackgroundColor"]) {
                                is Int -> colorValue
                                is Double -> colorValue.toInt()
                                is Float -> colorValue.toInt()
                                is Number -> colorValue.toInt()
                                else -> 0xFFFFFFFF.toInt()
                            }



                            setWindowBackgroundColor(windowColorEnabled, windowBackgroundColor)
                            AppLog.d("【窗口容器View】富文本格式已应用: 长度=${spannableString.length}, 行间距=${lineSpacing}dp, 对齐=$textAlignment, 窗口背景颜色: 启用=$windowColorEnabled")
                            return
                        } else {
                            AppLog.w("【窗口容器View】富文本反序列化失败，使用基本格式")
                        }
                    } catch (e: Exception) {
                        AppLog.e("【窗口容器View】富文本格式应用失败，使用基本格式", e)
                    }
                }
            }

            // 📝 后备方案：应用基本格式
            val isBold = formatData["isBold"] as? Boolean ?: false
            val isItalic = formatData["isItalic"] as? Boolean ?: false
            val fontSize = formatData["fontSize"] as? Int ?: 13
            val lineSpacing = when (val spacingValue = formatData["lineSpacing"]) {
                is Float -> spacingValue
                is Double -> spacingValue.toFloat()
                is Number -> spacingValue.toFloat()
                else -> 0.0f
            }
            val textAlignment = when (val alignmentValue = formatData["textAlignment"]) {
                is Int -> alignmentValue
                is Double -> alignmentValue.toInt()
                is Float -> alignmentValue.toInt()
                is Number -> alignmentValue.toInt()
                else -> android.view.Gravity.CENTER
            }
            val fontName = formatData["fontName"] as? String

            // 使用批量格式应用方法
            applyFormatToAllText(isBold, isItalic, fontSize)

            // 应用行间距（TextView级别属性）
            if (lineSpacing > 0.0f) {
                val lineSpacingExtra = lineSpacing * resources.displayMetrics.density
                setLineSpacing(lineSpacingExtra, 1.0f)
            }

            // 应用文本对齐（TextView级别属性）
            gravity = textAlignment

            // 应用字体
            if (!fontName.isNullOrBlank() && fontName != "Roboto") {
                AppLog.d("【窗口容器View】应用字体: $fontName")
            }

            // 🎨 应用窗口背景颜色
            val windowColorEnabled = formatData["windowColorEnabled"] as? Boolean ?: false
            val windowBackgroundColor = when (val colorValue = formatData["windowBackgroundColor"]) {
                is Int -> colorValue
                is Double -> colorValue.toInt()
                is Float -> colorValue.toInt()
                is Number -> colorValue.toInt()
                else -> 0xFFFFFFFF.toInt()
            }



            setWindowBackgroundColor(windowColorEnabled, windowBackgroundColor)
            AppLog.d("【窗口容器View】窗口背景颜色已应用: 启用=$windowColorEnabled, 颜色=${String.format("#%08X", windowBackgroundColor)}")

            AppLog.d("【窗口容器View】基本格式已应用: 加粗=$isBold, 倾斜=$isItalic, 字号=${fontSize}sp, 行间距=${lineSpacing}dp")

        } catch (e: Exception) {
            AppLog.e("【窗口容器View】应用文本格式失败", e)
        }
    }

    /**
     * 🎯 应用其他格式（背景颜色、行间距、对齐等）
     */
    private fun com.example.castapp.ui.view.TextWindowView.applyOtherFormats(formatData: Map<String, Any>) {
        try {
            // 应用行间距
            val lineSpacing = when (val spacingValue = formatData["lineSpacing"]) {
                is Float -> spacingValue
                is Double -> spacingValue.toFloat()
                is Number -> spacingValue.toFloat()
                else -> 0.0f
            }
            if (lineSpacing > 0.0f) {
                val lineSpacingExtra = lineSpacing * resources.displayMetrics.density
                setLineSpacing(lineSpacingExtra, 1.0f)
                AppLog.d("【窗口容器View】行间距已应用: ${lineSpacing}dp")
            }

            // 应用文本对齐
            val textAlignment = when (val alignmentValue = formatData["textAlignment"]) {
                is Int -> alignmentValue
                is Double -> alignmentValue.toInt()
                is Float -> alignmentValue.toInt()
                is Number -> alignmentValue.toInt()
                else -> android.view.Gravity.CENTER
            }
            gravity = textAlignment
            AppLog.d("【窗口容器View】文本对齐已应用: $textAlignment")

            // 应用窗口背景颜色
            val windowColorEnabled = formatData["windowColorEnabled"] as? Boolean ?: false
            val windowBackgroundColor = when (val colorValue = formatData["windowBackgroundColor"]) {
                is Int -> colorValue
                is Double -> colorValue.toInt()
                is Float -> colorValue.toInt()
                is Number -> colorValue.toInt()
                else -> 0xFFFFFFFF.toInt()
            }
            setWindowBackgroundColor(windowColorEnabled, windowBackgroundColor)
            AppLog.d("【窗口容器View】窗口背景颜色已应用: 启用=$windowColorEnabled")

        } catch (e: Exception) {
            AppLog.e("【窗口容器View】应用其他格式失败", e)
        }
    }

    /**
     * 📝 更新现有文本窗口View的内容
     */
    private fun updateTextWindowView(data: WindowVisualizationData) {
        try {
            textWindowView?.let { textView ->
                // 📝 先设置文本内容，再应用格式（富文本反序列化需要文本内容）
                data.textContent?.let { content ->
                    textView.setText(content) // 设置文本内容
                }

                // 更新格式信息（包括富文本格式）
                data.textFormatData?.let { formatData ->
                    textView.applyTextFormat(formatData)
                }

                AppLog.d("【窗口容器View】文本窗口View内容已更新: ${data.getShortConnectionId()}")
            }
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】更新文本窗口View失败: ${data.getShortConnectionId()}", e)
        }
    }

    /**
     * 📝 移除文本窗口View
     */
    private fun removeTextWindowView() {
        try {
            textWindowView?.let { textView ->
                removeView(textView)
                textWindowView = null
                AppLog.d("【窗口容器View】文本窗口View已移除")
            }
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】移除文本窗口View失败", e)
        }
    }

    init {
        // 🎯 设置为可绘制，因为FrameLayout默认不绘制
        setWillNotDraw(false)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // 🎯 修复：标记为已分离，避免后续操作
        isDetached = true
        // 🎯 安全清理：使用post延迟清理，避免在dispatchDetachedFromWindow过程中修改View层级
        post {
            safeRemoveBorderView()
            // 📝 清理文本窗口View
            removeTextWindowView()
        }
        AppLog.d("【窗口容器View】已从窗口分离，边框View和文本窗口View将延迟清理")
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val data = windowData ?: return

        // 绘制窗口内容（不需要Canvas.clipRect，因为已经使用View.clipBounds）
        drawWindowContent(canvas, data)
    }
    
    /**
     * 绘制窗口内容
     */
    private fun drawWindowContent(canvas: Canvas, data: WindowVisualizationData) {
        val bounds = RectF(0f, 0f, width.toFloat(), height.toFloat())

        // 🎯 修复：移除Canvas边框绘制，改用独立边框View（与接收端保持一致）
        // 🎨 修复：文本窗口任何时候都不绘制默认填充背景
        if (!data.isTextWindow() || !data.hasTextContent()) {
            // 只有非文本窗口才绘制填充背景
            if (data.isBorderEnabled) {
                val fillColor = data.borderColor and 0x00FFFFFF or 0x1A000000 // 10%透明度
                windowFillPaint.apply {
                    color = fillColor
                }
                AppLog.d("【窗口容器View】设置自定义填充色: ${data.getShortConnectionId()}")
                AppLog.d("  填充颜色: ${String.format("#%08X", fillColor)}")
            } else {
                // 默认填充样式
                windowFillPaint.apply {
                    color = "#1A4CAF50".toColorInt() // 10%透明度的绿色
                }
                AppLog.d("【窗口容器View】设置默认填充色: ${data.getShortConnectionId()}")
            }

            // 绘制矩形填充（不带圆角，圆角由Outline处理）
            canvas.drawRect(bounds, windowFillPaint)
        } else {
            AppLog.d("【窗口容器View】🎨 文本窗口跳过默认填充绘制: ${data.getShortConnectionId()}")
        }

        // 📝 只有非文本窗口才绘制截图，文本窗口使用真实TextView
        if (!data.isTextWindow() || !data.hasTextContent()) {
            // 非文本窗口：绘制截图（如果有）
            data.screenshotBitmap?.let { bitmap ->
                drawScreenshot(canvas, bitmap, bounds, data)
            }
        }
        // 📝 文本窗口的TextView已在setWindowData时创建，不需要在这里绘制

        // 🎯 移除设备名称显示：根据用户要求，不在可视化窗口中心显示设备名称
        // drawDeviceName(canvas, data, bounds)
    }
    
    /**
     * 绘制截图
     */
    private fun drawScreenshot(canvas: Canvas, bitmap: Bitmap, bounds: RectF, data: WindowVisualizationData) {
        val paint = Paint(Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG)

        // 计算截图的绘制区域（保持宽高比）
        val bitmapAspectRatio = bitmap.width.toFloat() / bitmap.height.toFloat()
        val boundsAspectRatio = bounds.width() / bounds.height()

        val drawRect = if (bitmapAspectRatio > boundsAspectRatio) {
            // 截图更宽，以高度为准
            val drawWidth = bounds.height() * bitmapAspectRatio
            val offsetX = (bounds.width() - drawWidth) / 2f
            RectF(bounds.left + offsetX, bounds.top, bounds.left + offsetX + drawWidth, bounds.bottom)
        } else {
            // 截图更高，以宽度为准
            val drawHeight = bounds.width() / bitmapAspectRatio
            val offsetY = (bounds.height() - drawHeight) / 2f
            RectF(bounds.left, bounds.top + offsetY, bounds.right, bounds.top + offsetY + drawHeight)
        }

        // 🎯 修复：如果窗口开启了镜像，需要对截图应用镜像变换
        if (data.isMirrored) {
            canvas.save()

            // 计算镜像变换：以绘制区域中心为轴进行水平镜像
            val centerX = drawRect.centerX()
            canvas.translate(centerX, 0f)  // 移动到中心
            canvas.scale(-1f, 1f)          // 水平镜像
            canvas.translate(-centerX, 0f) // 移回原位置

            canvas.drawBitmap(bitmap, null, drawRect, paint)
            canvas.restore()

            AppLog.v("【窗口容器View】截图已应用镜像变换: ${data.getShortConnectionId()}")
        } else {
            canvas.drawBitmap(bitmap, null, drawRect, paint)
        }
    }
    
    /**
     * 绘制设备名称
     */
    private fun drawDeviceName(canvas: Canvas, data: WindowVisualizationData, bounds: RectF) {
        val deviceName = data.deviceName ?: data.getShortConnectionId()
        val centerX = bounds.centerX()
        val centerY = bounds.centerY()
        
        // 绘制文字背景
        val textBounds = Rect()
        textPaint.getTextBounds(deviceName, 0, deviceName.length, textBounds)
        val textBackgroundRect = RectF(
            centerX - textBounds.width() / 2f - dpToPx(4f),
            centerY - textBounds.height() / 2f - dpToPx(2f),
            centerX + textBounds.width() / 2f + dpToPx(4f),
            centerY + textBounds.height() / 2f + dpToPx(2f)
        )
        
        val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.BLACK
            alpha = 128
        }
        canvas.drawRoundRect(textBackgroundRect, dpToPx(4f), dpToPx(4f), backgroundPaint)
        
        // 绘制文字
        canvas.drawText(deviceName, centerX, centerY + textBounds.height() / 2f, textPaint)
    }
    
    /**
     * 🎯 新增：开始裁剪模式
     */
    fun startCropMode(originalCropRatio: RectF?, callback: (RectF?, Boolean) -> Unit) {
        if (isCropping) {
            AppLog.d("【窗口容器View】已经在裁剪模式中")
            return
        }

        isCropping = true
        cropModeCallback = callback

        // 参考接收端CropManager.startCropMode()逻辑，完整清理裁剪状态
        val currentData = windowData
        if (currentData != null && currentData.cropRectRatio != null) {
            // 如果当前有裁剪，临时清除裁剪以显示完整画面进行重新裁剪
            AppLog.d("【窗口容器View】临时清除裁剪显示，恢复完整画面")

            // 完全清除裁剪状态
            clipBounds = null
            clipToOutline = false
            outlineProvider = null

            // 重新设置窗口数据，触发完整的重建过程
            val tempData = currentData.copy(
                cropRectRatio = null,
                isCropping = false
            )
            setWindowDataInternal(tempData, forceRefresh = true)

            AppLog.d("【窗口容器View】完整清理裁剪状态完成")
        } else {
            // 没有裁剪状态，只需要基本清理
            clipBounds = null
            clipToOutline = false
            outlineProvider = null

            AppLog.d("【窗口容器View】基本清理完成")
        }

        // 🎯 修复：参考接收端行为，进入裁剪模式时完全移除边框View
        if (currentData != null && currentData.isBorderEnabled) {
            // 保存边框状态，用于退出裁剪模式时恢复
            borderStateBeforeCrop = true
            removeBorderView()
            AppLog.d("【窗口容器View】进入裁剪模式，移除边框View（参考接收端行为）")
        } else {
            borderStateBeforeCrop = false
            AppLog.d("【窗口容器View】进入裁剪模式，当前无边框")
        }

        // 🎯 同时清除圆角裁剪效果，恢复到完整窗口显示
        if (currentData != null) {
            val cornerRadiusPx = dpToPx(currentData.cornerRadius * currentData.scaleFactor)
            // 设置基于整个容器的圆角outline（无裁剪状态）
            outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    outline.setRoundRect(0, 0, view.width, view.height, cornerRadiusPx)
                }
            }
            clipToOutline = true
            AppLog.d("【窗口容器View】临时清除裁剪效果，恢复完整画面显示")
        }

        // 🎯 修复：创建简化的裁剪覆盖层，只负责裁剪框，不包含按钮
        cropOverlay = CropOverlayView(context).apply {
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )

            // 如果有原始裁剪区域，设置为初始裁剪框位置
            originalCropRatio?.let { ratio ->
                setCropRectRatio(ratio)
                AppLog.d("【窗口容器View】恢复之前的裁剪框位置: 比例: $ratio")
            }

            // 设置裁剪变化监听器
            setCropChangeListener(object : CropOverlayView.CropChangeListener {
                override fun onCropChanged(cropRect: RectF) {
                    // 实时更新裁剪区域
                    AppLog.v("【窗口容器View】裁剪区域变化: $cropRect")
                }
            })
        }

        // 添加裁剪覆盖层到窗口容器中
        addView(cropOverlay)
        cropOverlay?.bringToFront()

        AppLog.d("【窗口容器View】进入裁剪模式")
    }

    /**
     * 🎯 新增：结束裁剪模式
     */
    fun endCropMode(isCancel: Boolean) {
        if (!isCropping) return

        val finalCropRatio = if (!isCancel) {
            cropOverlay?.getCropRectRatio()
        } else {
            null
        }

        AppLog.d("【窗口容器View】结束裁剪模式，取消: $isCancel")
        AppLog.d("【窗口容器View】最终裁剪比例: $finalCropRatio")

        // 移除裁剪覆盖层
        cropOverlay?.let { overlay ->
            removeView(overlay)
        }
        cropOverlay = null

        // 🎯 裁剪修复：退出裁剪模式时不立即重建边框View
        // 边框View将在后续的setWindowData()调用中统一重建，避免重复创建
        if (borderStateBeforeCrop) {
            AppLog.d("【裁剪修复】退出裁剪模式，边框将在数据更新时重建")
        } else {
            AppLog.d("【裁剪修复】退出裁剪模式，进入前无边框，保持无边框状态")
        }

        // 🎯 重置边框状态标记，避免状态混乱
        borderStateBeforeCrop = false

        isCropping = false

        // 🎯 关键修复：如果不是取消操作，需要立即应用最终的裁剪效果
        if (!isCancel && finalCropRatio != null) {
            // 立即应用裁剪效果到View.clipBounds
            applyFinalCropBounds(finalCropRatio)
            AppLog.d("【窗口容器View】立即应用最终裁剪效果: $finalCropRatio")
        }

        // 回调结果
        cropModeCallback?.invoke(finalCropRatio, isCancel)
        cropModeCallback = null
    }

    /**
     * 🎯 新增：应用最终裁剪边界
     */
    private fun applyFinalCropBounds(cropRatio: RectF) {
        val data = windowData ?: return
        val bounds = data.getVisualizationBounds()
        val cornerRadiusPx = dpToPx(data.cornerRadius * data.scaleFactor)

        val clipBounds = Rect(
            (cropRatio.left * bounds.width()).toInt(),
            (cropRatio.top * bounds.height()).toInt(),
            (cropRatio.right * bounds.width()).toInt(),
            (cropRatio.bottom * bounds.height()).toInt()
        )

        this.clipBounds = clipBounds

        // 🎯 关键修复：同时应用圆角效果
        outlineProvider = object : ViewOutlineProvider() {
            override fun getOutline(view: View, outline: Outline) {
                outline.setRoundRect(
                    clipBounds.left,
                    clipBounds.top,
                    clipBounds.right,
                    clipBounds.bottom,
                    cornerRadiusPx
                )
            }
        }
        clipToOutline = true

        AppLog.d("【窗口容器View】应用最终裁剪边界: $clipBounds")
        AppLog.d("【窗口容器View】应用圆角效果: ${data.cornerRadius}dp × ${data.scaleFactor} = ${data.cornerRadius * data.scaleFactor}dp (${cornerRadiusPx}px)")
    }

    /**
     * 🎯 裁剪修复：更新独立边框View（强化边框清理，避免重复创建）
     */
    private fun updateBorderView(data: WindowVisualizationData) {
        val parentView = parent as? ViewGroup
        if (parentView == null) {
            AppLog.w("【窗口容器View】无法获取父容器，跳过边框创建")
            return
        }

        // 🎯 裁剪修复：强化边框清理，确保完全移除所有现有边框
        forceRemoveAllBorderViews(parentView)

        if (data.isBorderEnabled) {
            createBorderView(data, parentView)
        }
    }

    /**
     * 🎯 新增：创建独立边框View
     */
    private fun createBorderView(data: WindowVisualizationData, parentView: ViewGroup) {
        // 🎯 修复：边框宽度需要考虑接收端窗口缩放因子，与接收端保持一致
        val scaledBorderWidth = data.borderWidth * data.scaleFactor
        val borderWidthPx = dpToPx(scaledBorderWidth).toInt()
        val cornerRadiusPx = dpToPx(data.cornerRadius * data.scaleFactor)

        // 🎯 关键：根据是否裁剪计算边框位置和尺寸（与接收端逻辑一致）
        val (borderLeft, borderTop, borderWidth, borderHeight) = if (data.cropRectRatio != null && clipBounds != null) {
            // 裁剪状态：基于clipBounds计算边框位置
            val left = clipBounds.left - borderWidthPx
            val top = clipBounds.top - borderWidthPx
            val width = clipBounds.width() + borderWidthPx * 2
            val height = clipBounds.height() + borderWidthPx * 2
            arrayOf(left, top, width, height)
        } else {
            // 未裁剪状态：基于整个容器计算边框位置
            val left = -borderWidthPx
            val top = -borderWidthPx
            val width = this.width + borderWidthPx * 2
            val height = this.height + borderWidthPx * 2
            arrayOf(left, top, width, height)
        }

        // 创建边框Paint
        val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            style = Paint.Style.STROKE
            strokeWidth = borderWidthPx.toFloat()
            color = data.borderColor
        }

        // 创建边框RectF
        val borderRect = RectF()

        // 🎯 修复：检查是否已分离，避免在分离过程中创建新View
        if (isDetached) {
            AppLog.w("【窗口容器View】容器已分离，跳过边框创建")
            return
        }

        // 创建独立边框View
        val newBorderView = object : View(context) {
            override fun onDraw(canvas: Canvas) {
                super.onDraw(canvas)

                // 设置绘制区域
                borderRect.set(
                    borderWidthPx / 2f,
                    borderWidthPx / 2f,
                    width - borderWidthPx / 2f,
                    height - borderWidthPx / 2f
                )

                // 🎯 关键：边框圆角半径需要加上边框宽度的一半，确保边框内边缘与内容区域外边缘完全贴合
                val borderRadius = cornerRadiusPx + borderWidthPx / 2f
                canvas.drawRoundRect(borderRect, borderRadius, borderRadius, borderPaint)

                AppLog.v("【窗口容器View】独立边框已绘制: ${data.getShortConnectionId()}")
            }
        }.apply {
            // 🎯 裁剪修复：为边框View添加标记，便于识别和清理
            tag = "BorderView_${data.connectionId}"
            // 设置布局参数
            layoutParams = FrameLayout.LayoutParams(borderWidth, borderHeight).apply {
                leftMargin = borderLeft
                topMargin = borderTop
            }

            // 🎯 关键：边框View与容器使用相同的坐标系统
            x = this@WindowVisualizationContainerView.x
            y = this@WindowVisualizationContainerView.y
            translationX = <EMAIL>
            translationY = <EMAIL>
            scaleX = <EMAIL>
            scaleY = <EMAIL>
            rotation = <EMAIL>

            // 🎯 关键修复：设置pivot点，确保旋转时边框与容器保持贴合
            // 边框视图pivot点 = 容器pivot点 - 边框视图的layout偏移（与接收端逻辑一致）
            pivotX = <EMAIL> - borderLeft
            pivotY = <EMAIL> - borderTop

            // 设置背景透明
            setBackgroundColor(Color.TRANSPARENT)
        }

        // 🎯 修复：使用WeakReference存储边框View引用
        borderViewRef = java.lang.ref.WeakReference(newBorderView)

        // 🎯 层级修复：延迟添加边框View，避免在窗口创建过程中打乱层级
        // 将边框View的添加操作延迟到下一个UI循环，确保所有窗口容器都已正确排序
        post {
            try {
                val containerIndex = parentView.indexOfChild(this@WindowVisualizationContainerView)
                if (containerIndex >= 0) {
                    // 🎯 关键修复：将边框View插入到窗口容器的紧邻位置，但不影响其他窗口的层级
                    parentView.addView(newBorderView, containerIndex + 1)
                    AppLog.d("【层级修复】边框View已延迟创建: ${data.getShortConnectionId()}, 容器索引=$containerIndex")
                } else {
                    // 如果找不到容器索引，使用默认添加方式
                    parentView.addView(newBorderView)
                    AppLog.d("【层级修复】边框View已创建（默认位置）: ${data.getShortConnectionId()}")
                }
                AppLog.d("  边框位置: ($borderLeft, $borderTop)")
                AppLog.d("  边框尺寸: ${borderWidth}×${borderHeight}")
                AppLog.d("  边框颜色: ${String.format("#%08X", data.borderColor)}")
            } catch (e: Exception) {
                AppLog.w("【层级修复】创建边框View时发生异常", e)
                borderViewRef = null
            }
        }
    }

    /**
     * 🎯 修复：安全移除边框View
     */
    private fun safeRemoveBorderView() {
        val borderView = borderViewRef?.get()
        if (borderView != null) {
            try {
                val parentView = parent as? ViewGroup
                if (parentView != null && borderView.parent == parentView) {
                    parentView.removeView(borderView)
                    AppLog.d("【窗口容器View】边框View已安全移除")
                } else {
                    AppLog.d("【窗口容器View】边框View已不在父容器中")
                }
            } catch (e: Exception) {
                AppLog.w("【窗口容器View】移除边框View时发生异常", e)
            }
        }
        borderViewRef = null
    }

    /**
     * 🎯 兼容：保留原有接口
     */
    private fun removeBorderView() {
        safeRemoveBorderView()
    }

    /**
     * 🎯 裁剪修复：强制移除所有边框View（解决裁剪模式下边框重复问题）
     */
    private fun forceRemoveAllBorderViews(parentView: ViewGroup) {
        try {
            // 首先移除WeakReference中的边框View
            safeRemoveBorderView()

            // 🎯 关键修复：遍历父容器，移除所有与当前窗口相关的边框View
            val windowData = this.windowData
            if (windowData != null) {
                val targetTag = "BorderView_${windowData.connectionId}"
                val viewsToRemove = mutableListOf<View>()

                for (i in 0 until parentView.childCount) {
                    val child = parentView.getChildAt(i)
                    // 通过tag识别边框View
                    if (child != this && child.tag == targetTag) {
                        viewsToRemove.add(child)
                    }
                }

                // 移除找到的边框View
                viewsToRemove.forEach { view ->
                    parentView.removeView(view)
                    AppLog.d("【裁剪修复】移除重复边框View: tag=${view.tag}")
                }

                if (viewsToRemove.isNotEmpty()) {
                    AppLog.d("【裁剪修复】强制清理完成，移除了 ${viewsToRemove.size} 个重复边框View")
                }
            }

        } catch (e: Exception) {
            AppLog.w("【裁剪修复】强制清理边框View时发生异常", e)
        }
    }

    /**
     * 🎯 修复：更新边框View位置（拖动时调用）
     */
    private fun updateBorderPosition() {
        val border = borderViewRef?.get() ?: return
        val data = windowData ?: return

        // 🎯 关键：边框View与容器完全同步变换状态
        border.apply {
            x = this@WindowVisualizationContainerView.x
            y = this@WindowVisualizationContainerView.y
            translationX = <EMAIL>
            translationY = <EMAIL>
            scaleX = <EMAIL>
            scaleY = <EMAIL>
            rotation = <EMAIL>

            // 🎯 关键修复：同步更新pivot点（与接收端逻辑一致）
            // 🎯 修复：边框宽度需要考虑接收端窗口缩放因子，与接收端保持一致
            val scaledBorderWidth = data.borderWidth * data.scaleFactor
            val borderWidthPx = dpToPx(scaledBorderWidth).toInt()
            val (borderLeft, borderTop) = if (data.cropRectRatio != null && clipBounds != null) {
                // 裁剪状态：基于clipBounds计算偏移
                Pair(clipBounds.left - borderWidthPx, clipBounds.top - borderWidthPx)
            } else {
                // 未裁剪状态：基于容器边缘计算偏移
                Pair(-borderWidthPx, -borderWidthPx)
            }

            // 边框视图pivot点 = 容器pivot点 - 边框视图的layout偏移
            pivotX = <EMAIL> - borderLeft
            pivotY = <EMAIL> - borderTop
        }

        AppLog.v("【窗口容器View】边框位置已同步: (${this.x}, ${this.y})")
    }

    /**
     * 🎯 层级修复：重写bringToFront方法，确保边框View同步调整层级
     */
    override fun bringToFront() {
        // 首先调用父类方法，将窗口容器移到前台
        super.bringToFront()

        // 然后将边框View也移到前台，确保边框始终在窗口上方
        bringBorderToFront()

        val data = windowData
        if (data != null) {
            AppLog.d("【层级修复】可视化窗口和边框已同步调整到前台: ${data.getShortConnectionId()}")
        }
    }

    /**
     * 🎯 层级修复：将边框View移到前台（层级管理时调用）
     */
    private fun bringBorderToFront() {
        val borderView = borderViewRef?.get() ?: return
        val parentView = parent as? ViewGroup ?: return

        try {
            // 🎯 关键修复：将边框View重新插入到窗口容器的下一个位置
            // 先移除边框View
            parentView.removeView(borderView)

            // 获取窗口容器的当前索引
            val containerIndex = parentView.indexOfChild(this)
            if (containerIndex >= 0) {
                // 将边框View重新插入到窗口容器的下一个位置
                parentView.addView(borderView, containerIndex + 1)
                AppLog.d("【层级修复】边框View已重新插入到窗口容器的下一个位置: 容器索引=$containerIndex, 边框索引=${containerIndex + 1}")
            } else {
                // 如果找不到容器索引，回退到原有方式
                parentView.addView(borderView)
                AppLog.w("【层级修复】未找到窗口容器索引，使用默认添加方式")
            }
        } catch (e: Exception) {
            AppLog.w("【层级修复】调整边框View层级时发生异常", e)
        }
    }

    /**
     * dp转px工具方法
     */
    private fun dpToPx(dp: Float): Float {
        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, resources.displayMetrics)
    }

    /**
     * 🎯 精确映射格式span位置到新文本（用于富文本精准换行同步）
     * 将原始文本中的格式位置映射到按行重组后的新文本中
     * 🔧 修复：处理重复文本的精确位置映射
     */
    private fun mapSpanPositionToNewText(
        originalText: String,
        newText: String,
        originalStart: Int,
        originalEnd: Int
    ): Pair<Int, Int>? {
        return try {
            // 验证原始位置的有效性
            if (originalStart >= originalText.length || originalEnd > originalText.length || originalStart >= originalEnd) {
                AppLog.w("【窗口容器View】原始span位置无效: $originalStart-$originalEnd, 文本长度: ${originalText.length}")
                return null
            }

            val spanText = originalText.substring(originalStart, originalEnd)
            AppLog.d("【窗口容器View】🎯 映射span文本: \"$spanText\" (原始位置: $originalStart-$originalEnd)")

            // 🎯 关键修复：使用字符级别的精确映射，而不是简单的indexOf
            // 构建字符位置映射表：原始文本位置 -> 新文本位置
            val positionMap = buildCharacterPositionMap(originalText, newText)

            if (positionMap.isEmpty()) {
                AppLog.w("【窗口容器View】无法构建字符位置映射表")
                return null
            }

            // 映射起始和结束位置
            val newStart = positionMap[originalStart]
            val newEnd = positionMap[originalEnd - 1]?.let { it + 1 } // originalEnd是exclusive的，所以要映射originalEnd-1的位置

            if (newStart == null || newEnd == null) {
                AppLog.w("【窗口容器View】无法映射span位置: 原始($originalStart-$originalEnd) -> 新($newStart-$newEnd)")
                return null
            }

            // 验证映射结果
            if (newStart < newEnd && newEnd <= newText.length) {
                val mappedText = newText.substring(newStart, newEnd)
                if (mappedText == spanText) {
                    AppLog.d("【窗口容器View】🎯 span位置映射成功: \"$spanText\" $originalStart-$originalEnd -> $newStart-$newEnd")
                    return Pair(newStart, newEnd)
                } else {
                    AppLog.w("【窗口容器View】映射后文本不匹配: 期望\"$spanText\", 实际\"$mappedText\"")
                }
            } else {
                AppLog.w("【窗口容器View】映射后位置无效: $newStart-$newEnd")
            }

            return null

        } catch (e: Exception) {
            AppLog.e("【窗口容器View】span位置映射失败", e)
            null
        }
    }

    /**
     * 🎯 构建字符位置映射表（原始文本位置 -> 新文本位置）
     * 处理换行符重新排列后的精确字符位置对应关系
     */
    private fun buildCharacterPositionMap(originalText: String, newText: String): Map<Int, Int> {
        return try {
            val positionMap = mutableMapOf<Int, Int>()

            // 移除所有换行符，获取纯字符内容
            val originalChars = originalText.replace("\n", "")
            val newChars = newText.replace("\n", "")

            // 验证字符内容是否一致（除了换行符）
            if (originalChars != newChars) {
                AppLog.w("【窗口容器View】原始文本和新文本的字符内容不一致")
                AppLog.w("  原始字符: \"$originalChars\"")
                AppLog.w("  新文本字符: \"$newChars\"")
                return emptyMap()
            }

            // 构建映射表：跳过换行符，映射实际字符位置
            var originalIndex = 0
            var newIndex = 0
            var charIndex = 0 // 在纯字符序列中的位置

            while (originalIndex < originalText.length && newIndex < newText.length) {
                val originalChar = originalText[originalIndex]
                val newChar = newText[newIndex]

                if (originalChar == '\n') {
                    // 原始文本中的换行符，跳过
                    originalIndex++
                    continue
                }

                if (newChar == '\n') {
                    // 新文本中的换行符，跳过
                    newIndex++
                    continue
                }

                // 两个都是非换行符，应该相同
                if (originalChar == newChar) {
                    positionMap[originalIndex] = newIndex
                    AppLog.v("【窗口容器View】字符映射: '$originalChar' 原始[$originalIndex] -> 新[$newIndex]")
                    originalIndex++
                    newIndex++
                    charIndex++
                } else {
                    AppLog.w("【窗口容器View】字符不匹配: 原始[$originalIndex]='$originalChar', 新[$newIndex]='$newChar'")
                    break
                }
            }

            AppLog.d("【窗口容器View】🎯 字符位置映射表构建完成: ${positionMap.size} 个字符")
            positionMap

        } catch (e: Exception) {
            AppLog.e("【窗口容器View】构建字符位置映射表失败", e)
            emptyMap()
        }
    }
}
