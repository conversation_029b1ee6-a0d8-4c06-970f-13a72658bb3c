package com.example.castapp.ui.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.widget.Button
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.graphics.toColorInt
import com.example.castapp.R
import com.example.castapp.model.WindowVisualizationData
import com.example.castapp.utils.AppLog
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

/**
 * 🎯 可视化窗口裁剪覆盖层
 * 为可视化窗口提供裁剪功能的UI覆盖层
 */
class CropVisualizationOverlay @JvmOverloads constructor(
    context: Context,
    private val windowData: WindowVisualizationData,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    // 裁剪区域（相对于可视化窗口的比例坐标）
    private var cropRect = RectF()
    
    // 可视化窗口的边界
    private var windowBounds = RectF()
    
    // 画笔
    private val borderPaint = Paint().apply {
        color = "#4CAF50".toColorInt()  // 绿色边框
        style = Paint.Style.STROKE
        strokeWidth = 8f
        isAntiAlias = true
    }
    
    private val maskPaint = Paint().apply {
        color = "#80000000".toColorInt()  // 半透明黑色遮罩
        style = Paint.Style.FILL
    }
    
    private val handlePaint = Paint().apply {
        color = Color.WHITE
        style = Paint.Style.STROKE
        strokeWidth = 10f
        isAntiAlias = true
    }

    // 拖动相关
    private var isDragging = false
    private var dragMode = DragMode.NONE
    private var lastTouchX = 0f
    private var lastTouchY = 0f
    private val touchThreshold = 30f
    private val minCropSize = 50f

    // 拖动模式
    private enum class DragMode {
        NONE, MOVE, LEFT, RIGHT, TOP, BOTTOM,
        TOP_LEFT, TOP_RIGHT, BOTTOM_LEFT, BOTTOM_RIGHT
    }

    // 控制按钮
    private var controlButtons: LinearLayout? = null

    // 🎯 标志位：是否已手动设置初始裁剪区域
    private var hasManuallySetInitialCrop = false

    // 回调接口
    interface CropChangeListener {
        fun onCropChanged(cropRatio: RectF)
        fun onCropApplied(cropRatio: RectF)
        fun onCropCancelled()
        fun onCropReset()
    }

    private var cropChangeListener: CropChangeListener? = null

    init {
        setWillNotDraw(false)
        setupInitialCropRect()
        createControlButtons()
        AppLog.d("【裁剪覆盖层】CropVisualizationOverlay 初始化完成")
    }

    fun setCropChangeListener(listener: CropChangeListener?) {
        cropChangeListener = listener
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        
        // 计算可视化窗口在覆盖层中的位置
        windowBounds.set(
            windowData.visualizedX,
            windowData.visualizedY,
            windowData.visualizedX + windowData.visualizedWidth,
            windowData.visualizedY + windowData.visualizedHeight
        )
        
        // 初始化裁剪区域为整个可视化窗口
        setupInitialCropRect()
        
        AppLog.d("【裁剪覆盖层】尺寸变化: ${w}x${h}, 窗口边界: $windowBounds")
    }

    private fun setupInitialCropRect() {
        if (windowBounds.isEmpty) return

        // 🎯 修复：如果已经手动设置了初始裁剪区域，不要覆盖
        if (hasManuallySetInitialCrop) {
            AppLog.d("【裁剪覆盖层】跳过自动初始化，保持手动设置的裁剪区域: $cropRect")
            return
        }

        // 如果窗口已有裁剪，使用现有裁剪区域，否则使用整个窗口
        if (windowData.cropRectRatio != null) {
            val ratio = windowData.cropRectRatio
            cropRect.set(
                windowBounds.left + ratio.left * windowBounds.width(),
                windowBounds.top + ratio.top * windowBounds.height(),
                windowBounds.left + ratio.right * windowBounds.width(),
                windowBounds.top + ratio.bottom * windowBounds.height()
            )
        } else {
            cropRect.set(windowBounds)
        }

        AppLog.d("【裁剪覆盖层】自动初始化裁剪区域: $cropRect")
    }

    /**
     * 🎯 设置初始裁剪区域（用于恢复之前的裁剪框位置）
     */
    fun setInitialCropRatio(cropRatio: RectF) {
        // 🎯 设置标志位，防止被 setupInitialCropRect() 覆盖
        hasManuallySetInitialCrop = true

        if (windowBounds.isEmpty) {
            // 如果窗口边界还没确定，延迟到 onSizeChanged 后再设置
            post {
                setInitialCropRatio(cropRatio)
            }
            return
        }

        cropRect.set(
            windowBounds.left + cropRatio.left * windowBounds.width(),
            windowBounds.top + cropRatio.top * windowBounds.height(),
            windowBounds.left + cropRatio.right * windowBounds.width(),
            windowBounds.top + cropRatio.bottom * windowBounds.height()
        )

        invalidate()
        AppLog.d("【裁剪覆盖层】设置初始裁剪区域: $cropRect, 比例: $cropRatio")
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        if (windowBounds.isEmpty) return

        // 绘制遮罩（裁剪区域外的半透明覆盖）
        drawMask(canvas)
        
        // 绘制裁剪边框
        canvas.drawRect(cropRect, borderPaint)
        
        // 绘制拖动手柄
        drawHandles(canvas)
    }

    private fun drawMask(canvas: Canvas) {
        // 上方遮罩
        canvas.drawRect(0f, 0f, width.toFloat(), cropRect.top, maskPaint)
        // 下方遮罩
        canvas.drawRect(0f, cropRect.bottom, width.toFloat(), height.toFloat(), maskPaint)
        // 左侧遮罩
        canvas.drawRect(0f, cropRect.top, cropRect.left, cropRect.bottom, maskPaint)
        // 右侧遮罩
        canvas.drawRect(cropRect.right, cropRect.top, width.toFloat(), cropRect.bottom, maskPaint)
    }

    private fun drawHandles(canvas: Canvas) {
        val handleSize = 20f
        
        // 四个角的L型手柄
        drawLHandle(canvas, cropRect.left, cropRect.top, handleSize, true, true) // 左上
        drawLHandle(canvas, cropRect.right, cropRect.top, handleSize, false, true) // 右上
        drawLHandle(canvas, cropRect.left, cropRect.bottom, handleSize, true, false) // 左下
        drawLHandle(canvas, cropRect.right, cropRect.bottom, handleSize, false, false) // 右下
        
        // 四边中点的手柄
        val centerX = cropRect.centerX()
        val centerY = cropRect.centerY()
        
        // 上边中点
        canvas.drawLine(centerX - handleSize/2, cropRect.top, centerX + handleSize/2, cropRect.top, handlePaint)
        // 下边中点
        canvas.drawLine(centerX - handleSize/2, cropRect.bottom, centerX + handleSize/2, cropRect.bottom, handlePaint)
        // 左边中点
        canvas.drawLine(cropRect.left, centerY - handleSize/2, cropRect.left, centerY + handleSize/2, handlePaint)
        // 右边中点
        canvas.drawLine(cropRect.right, centerY - handleSize/2, cropRect.right, centerY + handleSize/2, handlePaint)
    }

    private fun drawLHandle(canvas: Canvas, x: Float, y: Float, size: Float, isLeft: Boolean, isTop: Boolean) {
        val offset = if (isLeft) size else -size
        val vOffset = if (isTop) size else -size
        
        // 水平线
        canvas.drawLine(x, y, x + offset, y, handlePaint)
        // 垂直线
        canvas.drawLine(x, y, x, y + vOffset, handlePaint)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                lastTouchX = event.x
                lastTouchY = event.y
                dragMode = detectDragMode(event.x, event.y)
                
                if (dragMode != DragMode.NONE) {
                    isDragging = true
                    AppLog.d("【裁剪覆盖层】开始拖动: $dragMode")
                    return true
                }
                return false
            }
            
            MotionEvent.ACTION_MOVE -> {
                if (isDragging && dragMode != DragMode.NONE) {
                    val deltaX = event.x - lastTouchX
                    val deltaY = event.y - lastTouchY
                    
                    performCropResize(deltaX, deltaY)
                    
                    lastTouchX = event.x
                    lastTouchY = event.y
                    
                    // 通知裁剪变化
                    notifyCropChange()
                    
                    invalidate()
                    return true
                }
                return false
            }
            
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                if (isDragging) {
                    isDragging = false
                    dragMode = DragMode.NONE
                    AppLog.d("【裁剪覆盖层】拖动结束")
                    return true
                }
                return false
            }
        }
        return super.onTouchEvent(event)
    }

    private fun detectDragMode(x: Float, y: Float): DragMode {
        val isLeft = abs(x - cropRect.left) <= touchThreshold
        val isRight = abs(x - cropRect.right) <= touchThreshold
        val isTop = abs(y - cropRect.top) <= touchThreshold
        val isBottom = abs(y - cropRect.bottom) <= touchThreshold
        val isInside = cropRect.contains(x, y)
        
        return when {
            isLeft && isTop -> DragMode.TOP_LEFT
            isRight && isTop -> DragMode.TOP_RIGHT
            isLeft && isBottom -> DragMode.BOTTOM_LEFT
            isRight && isBottom -> DragMode.BOTTOM_RIGHT
            isLeft -> DragMode.LEFT
            isRight -> DragMode.RIGHT
            isTop -> DragMode.TOP
            isBottom -> DragMode.BOTTOM
            isInside -> DragMode.MOVE
            else -> DragMode.NONE
        }
    }

    private fun performCropResize(deltaX: Float, deltaY: Float) {
        val newRect = RectF(cropRect)
        
        when (dragMode) {
            DragMode.MOVE -> {
                newRect.offset(deltaX, deltaY)
                // 确保不超出窗口边界
                if (newRect.left < windowBounds.left) {
                    newRect.offset(windowBounds.left - newRect.left, 0f)
                }
                if (newRect.right > windowBounds.right) {
                    newRect.offset(windowBounds.right - newRect.right, 0f)
                }
                if (newRect.top < windowBounds.top) {
                    newRect.offset(0f, windowBounds.top - newRect.top)
                }
                if (newRect.bottom > windowBounds.bottom) {
                    newRect.offset(0f, windowBounds.bottom - newRect.bottom)
                }
            }
            DragMode.LEFT -> newRect.left = max(windowBounds.left, min(newRect.left + deltaX, newRect.right - minCropSize))
            DragMode.RIGHT -> newRect.right = min(windowBounds.right, max(newRect.right + deltaX, newRect.left + minCropSize))
            DragMode.TOP -> newRect.top = max(windowBounds.top, min(newRect.top + deltaY, newRect.bottom - minCropSize))
            DragMode.BOTTOM -> newRect.bottom = min(windowBounds.bottom, max(newRect.bottom + deltaY, newRect.top + minCropSize))
            DragMode.TOP_LEFT -> {
                newRect.left = max(windowBounds.left, min(newRect.left + deltaX, newRect.right - minCropSize))
                newRect.top = max(windowBounds.top, min(newRect.top + deltaY, newRect.bottom - minCropSize))
            }
            DragMode.TOP_RIGHT -> {
                newRect.right = min(windowBounds.right, max(newRect.right + deltaX, newRect.left + minCropSize))
                newRect.top = max(windowBounds.top, min(newRect.top + deltaY, newRect.bottom - minCropSize))
            }
            DragMode.BOTTOM_LEFT -> {
                newRect.left = max(windowBounds.left, min(newRect.left + deltaX, newRect.right - minCropSize))
                newRect.bottom = min(windowBounds.bottom, max(newRect.bottom + deltaY, newRect.top + minCropSize))
            }
            DragMode.BOTTOM_RIGHT -> {
                newRect.right = min(windowBounds.right, max(newRect.right + deltaX, newRect.left + minCropSize))
                newRect.bottom = min(windowBounds.bottom, max(newRect.bottom + deltaY, newRect.top + minCropSize))
            }
            else -> return
        }
        
        cropRect.set(newRect)
    }

    private fun notifyCropChange() {
        val cropRatio = getCropRatio()
        cropChangeListener?.onCropChanged(cropRatio)
    }

    private fun getCropRatio(): RectF {
        val windowWidth = windowBounds.width()
        val windowHeight = windowBounds.height()
        
        return RectF(
            (cropRect.left - windowBounds.left) / windowWidth,
            (cropRect.top - windowBounds.top) / windowHeight,
            (cropRect.right - windowBounds.left) / windowWidth,
            (cropRect.bottom - windowBounds.top) / windowHeight
        )
    }

    private fun createControlButtons() {
        // 使用XML布局创建控制按钮
        val layoutInflater = LayoutInflater.from(context)
        controlButtons = layoutInflater.inflate(R.layout.crop_control_buttons, this, false) as LinearLayout
        
        // 设置按钮位置
        controlButtons?.apply {
            layoutParams = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
                gravity = android.view.Gravity.BOTTOM or android.view.Gravity.END
                marginEnd = 20
                bottomMargin = 20
            }
            elevation = 1000f
        }
        
        // 设置设备信息
        controlButtons?.findViewById<TextView>(R.id.tv_device_info)?.text = 
            "${windowData.getShortConnectionId()}"
        
        // 设置按钮点击事件
        controlButtons?.findViewById<Button>(R.id.btn_reset)?.setOnClickListener {
            resetCrop()
        }
        
        controlButtons?.findViewById<Button>(R.id.btn_cancel)?.setOnClickListener {
            cropChangeListener?.onCropCancelled()
        }
        
        controlButtons?.findViewById<Button>(R.id.btn_apply)?.setOnClickListener {
            val cropRatio = getCropRatio()
            cropChangeListener?.onCropApplied(cropRatio)
        }
        
        addView(controlButtons)
    }

    private fun resetCrop() {
        cropRect.set(windowBounds)
        invalidate()
        cropChangeListener?.onCropReset()
    }
}
