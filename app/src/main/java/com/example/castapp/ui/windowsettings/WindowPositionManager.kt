package com.example.castapp.ui.windowsettings

import android.graphics.RectF
import android.widget.FrameLayout
import com.example.castapp.utils.AppLog

/**
 * 窗口位置管理器（集中化版本）
 *
 * 职责：
 * 1. 管理窗口的基准位置（用户设置的位置）
 * 2. 计算裁剪偏移
 * 3. 提供清晰的位置设置和获取接口
 * 4. 🎯 新增：集中处理所有位置坐标更新操作
 *
 * 设计原则：
 * - 基准位置：用户拖拽设置的位置，不受裁剪影响
 * - 显示位置：基准位置 + 裁剪偏移
 * - 单一职责：只管理位置，不管理尺寸或其他变换
 * - 🎯 集中化：所有位置更新都通过此管理器进行
 */
class WindowPositionManager(
    private val container: FrameLayout,
    private val windowWidth: Int,
    private val windowHeight: Int
) {

    // 基准位置：用户设置的位置，不受裁剪影响
    private var baseTranslationX: Float = 0f
    private var baseTranslationY: Float = 0f

    // 当前裁剪状态
    private var currentCropRatio: RectF? = null

    // 🎯 新增：位置状态保存（用于裁剪模式切换）
    private var savedBaseTranslationX: Float = 0f
    private var savedBaseTranslationY: Float = 0f

    /**
     * 同步当前显示位置为基准位置（用于初始化或重置）
     */
    fun syncCurrentAsBase() {
        baseTranslationX = container.translationX
        baseTranslationY = container.translationY
        AppLog.d("🎯 同步当前位置为基准位置: (${baseTranslationX}, ${baseTranslationY})")
    }
    
    /**
     * 从裁剪位置反推基准位置（用于状态恢复）
     * 🎯 修复：区分图片窗口和其他窗口的位置反推方式
     */
    fun syncFromCroppedPosition(cropRatio: RectF?) {
        if (cropRatio != null) {
            val hasImageView = container.childCount > 0 &&
                container.getChildAt(0) is android.widget.ImageView
            val offsetX = cropRatio.left * windowWidth
            val offsetY = cropRatio.top * windowHeight

            if (hasImageView) {
                // 🎯 图片窗口：需要减去补偿偏移来获取基准位置
                // 因为容器位置包含了补偿偏移，需要减去它来获取真正的基准位置
                baseTranslationX = container.translationX - offsetX
                baseTranslationY = container.translationY - offsetY
                AppLog.d("🎯 图片窗口从裁剪位置反推基准位置:")
                AppLog.d("🎯   当前位置: (${container.translationX}, ${container.translationY})")
                AppLog.d("🎯   补偿偏移: (${offsetX}, ${offsetY})")
                AppLog.d("🎯   基准位置: (${baseTranslationX}, ${baseTranslationY})")
            } else {
                // 🎯 投屏/视频窗口：需要减去裁剪偏移
                baseTranslationX = container.translationX - offsetX
                baseTranslationY = container.translationY - offsetY
                AppLog.d("🎯 投屏/视频窗口从裁剪位置反推基准位置:")
                AppLog.d("🎯   当前位置: (${container.translationX}, ${container.translationY})")
                AppLog.d("🎯   裁剪偏移: (${offsetX}, ${offsetY})")
                AppLog.d("🎯   基准位置: (${baseTranslationX}, ${baseTranslationY})")
            }
        } else {
            syncCurrentAsBase()
        }
    }
    
    // ========== 🎯 新增：集中化位置更新方法 ==========

    /**
     * 增量位置更新（用于拖动手势）
     */
    fun updatePosition(deltaX: Float, deltaY: Float) {
        baseTranslationX += deltaX
        baseTranslationY += deltaY
        updateDisplayPosition()
        AppLog.v("🎯 增量位置更新: 基准位置=(${baseTranslationX}, ${baseTranslationY})")
    }

    /**
     * 精确位置设置（用于布局恢复、精准变换等）
     */
    fun setPrecisionPosition(x: Float, y: Float) {
        baseTranslationX = x
        baseTranslationY = y
        updateDisplayPosition()
        AppLog.d("🎯 精确位置设置: 基准位置=(${baseTranslationX}, ${baseTranslationY})")
    }

    /**
     * 重置位置到原点
     */
    fun resetPosition() {
        baseTranslationX = 0f
        baseTranslationY = 0f
        updateDisplayPosition()
        AppLog.d("🎯 位置已重置到原点: (0, 0)")
    }

    /**
     * 保存当前基准位置状态
     */
    fun savePositionState() {
        savedBaseTranslationX = baseTranslationX
        savedBaseTranslationY = baseTranslationY
        AppLog.d("🎯 保存位置状态: (${savedBaseTranslationX}, ${savedBaseTranslationY})")
    }

    /**
     * 直接设置容器位置（仅用于特殊情况，如TextureView布局恢复）
     */
    fun setContainerPosition(x: Float, y: Float) {
        container.translationX = x
        container.translationY = y
        AppLog.d("🎯 直接设置容器位置: (${x}, ${y})")
    }

    /**
     * 更新显示位置（clipBounds裁剪适配版）
     * 🎯 关键修复：clipBounds裁剪模式下，不需要计算裁剪偏移
     */
    private fun updateDisplayPosition() {
        // 🎯 关键修复：clipBounds裁剪模式下，不需要计算裁剪偏移
        // 因为clipBounds是在容器级别进行裁剪，不影响容器的位置
        // 直接使用基准位置即可
        val displayX = baseTranslationX
        val displayY = baseTranslationY

        // 应用基准位置
        container.translationX = displayX
        container.translationY = displayY

        AppLog.v("🎯 [clipBounds适配] 位置更新: 直接使用基准位置=(${displayX}, ${displayY})")

        // 🎯 调试信息：显示当前裁剪状态
        if (currentCropRatio != null) {
            AppLog.d("🎯 [clipBounds适配] 裁剪状态: $currentCropRatio，但位置不受影响")
        }
    }
}
