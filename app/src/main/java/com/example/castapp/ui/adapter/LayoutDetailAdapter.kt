package com.example.castapp.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.database.entity.WindowLayoutItemEntity

/**
 * 布局详情适配器
 * 用于显示选中布局中包含的窗口详细信息
 */
class LayoutDetailAdapter : ListAdapter<WindowLayoutItemEntity, LayoutDetailAdapter.DetailViewHolder>(DetailDiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DetailViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_director_info, parent, false)
        return DetailViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: DetailViewHolder, position: Int) {
        val item = getItem(position)
        val totalWindows = itemCount // 获取总窗口数
        holder.bind(item, totalWindows) // 🐾 传递总窗口数用于层级计算
    }
    
    /**
     * 窗口详情ViewHolder
     */
    class DetailViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val orderNumber: TextView = itemView.findViewById(R.id.order_number)
        private val deviceInfoText: TextView = itemView.findViewById(R.id.device_info_text)
        private val connectionIdText: TextView = itemView.findViewById(R.id.connection_id_text)
        private val positionText: TextView = itemView.findViewById(R.id.tv_position)
        private val scaleText: TextView = itemView.findViewById(R.id.tv_scale)
        private val rotationText: TextView = itemView.findViewById(R.id.tv_rotation)
        private val layerText: TextView = itemView.findViewById(R.id.tv_layer)
        private val cropRectText: TextView = itemView.findViewById(R.id.crop_rect_text)
        private val visibilityText: TextView = itemView.findViewById(R.id.visibility_text)
        private val landscapeText: TextView = itemView.findViewById(R.id.landscape_text) // 🎯 横屏状态
        private val cornerRadiusText: TextView = itemView.findViewById(R.id.corner_radius_text)
        private val opacityText: TextView = itemView.findViewById(R.id.opacity_text)
        private val mirrorText: TextView = itemView.findViewById(R.id.mirror_text)

        private val borderStatusText: TextView = itemView.findViewById(R.id.border_status_text)
        private val borderWidthText: TextView = itemView.findViewById(R.id.border_width_text)
        private val borderColorText: TextView = itemView.findViewById(R.id.border_color_text)
        private val noteText: TextView = itemView.findViewById(R.id.note_text)
        private val videoPlayStatusText: TextView = itemView.findViewById(R.id.video_play_status_text) // 🏷️ 备注信息
        
        fun bind(item: WindowLayoutItemEntity, totalWindows: Int) {
            // 🐾 计算显示层级：orderIndex 0(最底层) -> 显示为 totalWindows
            // orderIndex (totalWindows-1)(最顶层) -> 显示为 1
            val displayLayer = totalWindows - item.orderIndex
            orderNumber.text = itemView.context.getString(R.string.order_number_format, displayLayer)

            // 🐾 调试日志
            com.example.castapp.utils.AppLog.d("【导播台显示】位置${bindingAdapterPosition} -> 显示层级${displayLayer}, 原始orderIndex=${item.orderIndex}: ${item.getDisplayDeviceInfo()}")

            // 设置设备信息
            deviceInfoText.text = item.getDisplayDeviceInfo()

            // 设置连接ID
            connectionIdText.text = itemView.context.getString(R.string.connection_id_parentheses_format, item.getShortConnectionId())

            // 设置变换信息（分别设置四个TextView）
            positionText.text = itemView.context.getString(R.string.position_format, item.positionX.toInt(), item.positionY.toInt())
            scaleText.text = itemView.context.getString(R.string.scale_format, item.scaleFactor)
            rotationText.text = itemView.context.getString(R.string.rotation_format, item.rotationAngle.toInt())
            layerText.text = itemView.context.getString(R.string.layer_format, displayLayer)
            
            // 设置裁剪信息
            cropRectText.text = item.getCropDisplayText()
            
            // 设置可见性信息
            visibilityText.text = item.getVisibilityDisplayText()

            // 🎯 设置横屏模式信息
            landscapeText.text = item.getLandscapeModeDisplayText()

            // 设置圆角信息
            cornerRadiusText.text = item.getCornerRadiusDisplayText()

            // 设置透明度信息
            opacityText.text = item.getAlphaDisplayText()

            // 设置镜像信息
            mirrorText.text = item.getMirrorDisplayText()



            // 设置边框信息（三个独立TextView）
            borderStatusText.text = item.getBorderStatusDisplayText()
            borderWidthText.text = item.getBorderWidthDisplayText()
            borderColorText.text = item.getBorderColorDisplayText()

            // 🏷️ 设置备注信息
            noteText.text = item.getNoteDisplayText()

            // 🎬 设置视频播放状态信息
            val videoPlayStatus = item.getVideoPlayStatusDisplayText()
            if (videoPlayStatus.isNotEmpty()) {
                videoPlayStatusText.text = videoPlayStatus
                videoPlayStatusText.visibility = android.view.View.VISIBLE
            } else {
                videoPlayStatusText.visibility = android.view.View.GONE
            }
        }
    }
    
    /**
     * DiffUtil回调，用于高效更新列表
     */
    private class DetailDiffCallback : DiffUtil.ItemCallback<WindowLayoutItemEntity>() {
        override fun areItemsTheSame(oldItem: WindowLayoutItemEntity, newItem: WindowLayoutItemEntity): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: WindowLayoutItemEntity, newItem: WindowLayoutItemEntity): Boolean {
            return oldItem == newItem
        }
    }
}
