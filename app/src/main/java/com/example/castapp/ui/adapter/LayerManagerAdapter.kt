package com.example.castapp.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.model.CastWindowInfo
import com.example.castapp.utils.AppLog
import java.util.*

/**
 * 层级管理器RecyclerView适配器
 * 专门用于层级调整功能，简化显示内容
 */
class LayerManagerAdapter : ListAdapter<CastWindowInfo, LayerManagerAdapter.LayerViewHolder>(LayerManagerAdapter.WindowDiffCallback()) {

    // 拖拽排序回调接口
    interface OnItemMoveListener {
        fun onItemMove(fromPosition: Int, toPosition: Int)
    }

    // 🏷️ 备注变更回调接口
    interface OnNoteChangeListener {
        fun onNoteChanged(connectionId: String, note: String)
    }

    private var onItemMoveListener: OnItemMoveListener? = null
    private var onNoteChangeListener: OnNoteChangeListener? = null // 🏷️ 备注变更监听器
    private val internalList = mutableListOf<CastWindowInfo>()

    fun setOnItemMoveListener(listener: OnItemMoveListener?) {
        onItemMoveListener = listener
    }

    // 🏷️ 设置备注变更监听器
    fun setOnNoteChangeListener(listener: OnNoteChangeListener?) {
        onNoteChangeListener = listener
    }

    override fun submitList(list: List<CastWindowInfo>?) {
        AppLog.d("【层级适配器】submitList调用，新列表大小: ${list?.size ?: 0}")
        list?.forEachIndexed { index, windowInfo ->
            AppLog.d("【层级适配器】新列表 位置${index} -> 序号${index + 1}: ${windowInfo.getDisplayTextWithDevice()}")
        }

        super.submitList(list)
        internalList.clear()
        list?.let { internalList.addAll(it) }

        AppLog.d("【层级适配器】internalList更新完成，大小: ${internalList.size}")
    }

    fun getCurrentWindowList(): List<CastWindowInfo> {
        return internalList.toList()
    }

    /**
     * 更新所有序号显示，用于拖拽结束后的丝滑更新
     */
    fun updateAllOrderNumbers() {
        AppLog.d("【层级序号】开始更新所有序号")
        // 只更新序号TextView，不重新绑定整个ViewHolder
        for (i in 0 until itemCount) {
            notifyItemChanged(i, "UPDATE_ORDER_NUMBER")
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LayerViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_layer, parent, false)
        return LayerViewHolder(view)
    }

    override fun onBindViewHolder(holder: LayerViewHolder, position: Int) {
        holder.bind(getItem(position), position, onNoteChangeListener)
    }

    override fun onBindViewHolder(holder: LayerViewHolder, position: Int, payloads: MutableList<Any>) {
        if (payloads.isNotEmpty() && payloads.contains("UPDATE_ORDER_NUMBER")) {
            // 只更新序号，避免整个ViewHolder重新绑定
            holder.updateOrderNumber(position)
            AppLog.d("【层级序号】只更新位置${position}的序号")
        } else {
            super.onBindViewHolder(holder, position, payloads)
        }
    }

    /**
     * 处理拖拽移动
     */
    fun onItemMove(fromPosition: Int, toPosition: Int): Boolean {
        if (fromPosition < internalList.size && toPosition < internalList.size) {
            AppLog.d("【层级拖拽】开始移动: 从位置${fromPosition} 到位置${toPosition}")

            Collections.swap(internalList, fromPosition, toPosition)
            notifyItemMoved(fromPosition, toPosition)

            AppLog.d("【层级拖拽】移动后列表:")
            internalList.forEachIndexed { index, windowInfo ->
                AppLog.d("【层级拖拽】  位置${index} -> 层级${index + 1}: ${windowInfo.getDisplayTextWithDevice()}")
            }

            onItemMoveListener?.onItemMove(fromPosition, toPosition)
            return true
        }
        return false
    }

    class LayerViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val tvOrderNumber: TextView = itemView.findViewById(R.id.tv_order_number)
        private val tvDeviceName: TextView = itemView.findViewById(R.id.tv_device_name)
        private val tvConnectionId: TextView = itemView.findViewById(R.id.tv_connection_id)
        private val tvDeviceNote: TextView = itemView.findViewById(R.id.tv_device_note) // 🏷️ 设备备注
        val dragHandle: ImageView = itemView.findViewById(R.id.drag_handle)

        fun bind(windowInfo: CastWindowInfo, position: Int, onNoteChangeListener: OnNoteChangeListener?) {
            // 设置视觉层级序号（position 0 = 最上层 = 序号1）
            val layerNumber = position + 1
            tvOrderNumber.text = "$layerNumber."

            // 设置设备名称
            tvDeviceName.text = windowInfo.deviceName

            // 设置连接ID（格式化显示）
            tvConnectionId.text = "（ID:${windowInfo.getShortConnectionId()}）"

            // 🏷️ 设置备注 - 显示和点击处理
            tvDeviceNote.text = windowInfo.getNoteDisplayText()
            tvDeviceNote.setOnClickListener {
                showNoteEditDialog(windowInfo, onNoteChangeListener)
            }

            AppLog.d("【层级绑定】位置${position} -> 层级序号${layerNumber}: ${windowInfo.getDisplayTextWithDevice()}")
        }

        /**
         * 只更新序号，用于拖拽结束后的丝滑更新
         */
        fun updateOrderNumber(position: Int) {
            val layerNumber = position + 1
            tvOrderNumber.text = "$layerNumber."
            AppLog.d("【层级序号】位置${position} -> 层级序号${layerNumber}")
        }

        /**
         * 🏷️ 显示备注编辑对话框
         */
        private fun showNoteEditDialog(windowInfo: CastWindowInfo, onNoteChangeListener: OnNoteChangeListener?) {
            try {
                val context = itemView.context
                val noteEditDialog = com.example.castapp.ui.dialog.NoteEditDialog(
                    context = context,
                    connectionId = windowInfo.connectionId,
                    deviceName = windowInfo.deviceName,
                    currentNote = windowInfo.note
                ) { newNote ->
                    // 备注更新回调
                    tvDeviceNote.text = if (newNote.isNotEmpty() && newNote != "无") {
                        newNote
                    } else {
                        "无"
                    }

                    // 🏷️ 通知监听器更新底层数据
                    onNoteChangeListener?.onNoteChanged(windowInfo.connectionId, newNote)

                    AppLog.d("🏷️ 层级管理-备注已更新: ${windowInfo.connectionId} -> $newNote")
                }
                noteEditDialog.show()
                AppLog.d("🏷️ 层级管理-备注编辑对话框已显示: ${windowInfo.connectionId}")
            } catch (e: Exception) {
                AppLog.e("🏷️ 层级管理-显示备注编辑对话框失败: ${windowInfo.connectionId}", e)
            }
        }
    }

    /**
     * ItemTouchHelper回调，处理拖拽排序
     */
    class ItemTouchHelperCallback(private val adapter: LayerManagerAdapter) : ItemTouchHelper.Callback() {

        override fun getMovementFlags(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder): Int {
            val dragFlags = ItemTouchHelper.UP or ItemTouchHelper.DOWN
            return makeMovementFlags(dragFlags, 0)
        }

        override fun onMove(
            recyclerView: RecyclerView,
            viewHolder: RecyclerView.ViewHolder,
            target: RecyclerView.ViewHolder
        ): Boolean {
            return adapter.onItemMove(viewHolder.bindingAdapterPosition, target.bindingAdapterPosition)
        }

        override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
            // 不支持滑动删除
        }

        override fun isLongPressDragEnabled(): Boolean {
            return false // 禁用长按拖拽，使用拖动手柄
        }

        // 拖拽结束后更新序号，确保丝滑体验
        override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
            super.clearView(recyclerView, viewHolder)

            AppLog.d("【层级拖拽】拖拽结束，开始更新序号")
            // 延迟更新序号，确保拖拽动画完成
            recyclerView.post {
                adapter.updateAllOrderNumbers()
            }
        }

        override fun isItemViewSwipeEnabled(): Boolean {
            return false // 禁用滑动
        }
    }

    /**
     * DiffUtil回调，用于高效更新列表
     */
    private class WindowDiffCallback : DiffUtil.ItemCallback<CastWindowInfo>() {
        override fun areItemsTheSame(oldItem: CastWindowInfo, newItem: CastWindowInfo): Boolean {
            return oldItem.connectionId == newItem.connectionId
        }

        override fun areContentsTheSame(oldItem: CastWindowInfo, newItem: CastWindowInfo): Boolean {
            return oldItem == newItem
        }
    }
}
