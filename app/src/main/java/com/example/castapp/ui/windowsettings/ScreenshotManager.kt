package com.example.castapp.ui.windowsettings

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.RectF
import android.view.TextureView
import android.view.View
import androidx.core.graphics.createBitmap
import com.example.castapp.utils.AppLog

/**
 * 截图管理器
 * 负责投屏窗口的截图捕获和图像处理
 */
class ScreenshotManager {
    
    /**
     * 捕获投屏窗口的最终显示画面（TextureView专用）
     * @param textureView TextureView实例
     * @param connectionId 连接ID
     * @param isCroppedWindow 是否为裁剪窗口
     * @param isMirrored 是否镜像
     * @param cropRectRatio 裁剪区域比例
     * @param applyTransforms 🎯 是否应用变换（裁剪、镜像），false时返回原始截图
     * @return 截图Bitmap，失败时返回null
     */
    fun captureScreenshot(
        textureView: TextureView?,
        connectionId: String,
        isCroppedWindow: Boolean,
        isMirrored: Boolean,
        cropRectRatio: RectF?,
        applyTransforms: Boolean = true
    ): Bitmap? {
        return try {
            AppLog.d("📸 开始捕获TextureView截图: $connectionId")

            if (textureView == null || !textureView.isAvailable) {
                AppLog.w("📸 TextureView不可用，无法截图: $connectionId, available=${textureView?.isAvailable}")
                return null
            }

            // 第一步：从TextureView获取原始视频内容
            val originalBitmap = textureView.bitmap
            if (originalBitmap == null) {
                AppLog.w("📸 TextureView截图返回null: $connectionId")
                return null
            }

            AppLog.d("📸 TextureView原始截图成功: $connectionId, 尺寸: ${originalBitmap.width}x${originalBitmap.height}")

            // 🎯 根据applyTransforms参数决定是否应用变换
            val finalBitmap = if (applyTransforms) {
                // 第二步：应用容器的最终变换状态
                val transformedBitmap = applyContainerTransformsToBitmap(
                    originalBitmap,
                    connectionId,
                    isCroppedWindow,
                    isMirrored,
                    cropRectRatio
                )

                // 释放原始截图（如果有变换）
                if (transformedBitmap != originalBitmap) {
                    originalBitmap.recycle()
                }

                if (transformedBitmap != null) {
                    AppLog.d("📸 TextureView截图成功（已变换）: $connectionId, 最终尺寸: ${transformedBitmap.width}x${transformedBitmap.height}")
                    AppLog.d("📸 应用的变换效果: 裁剪=$isCroppedWindow, 镜像=$isMirrored")
                } else {
                    AppLog.w("📸 TextureView变换失败，返回原始截图: $connectionId")
                }
                transformedBitmap ?: originalBitmap
            } else {
                // 🎯 返回原始截图，不应用任何变换（用于遥控端）
                AppLog.d("📸 TextureView截图成功（原始）: $connectionId, 尺寸: ${originalBitmap.width}x${originalBitmap.height}")
                AppLog.d("📸 跳过变换效果，返回原始截图（用于遥控端可视化）")
                originalBitmap
            }

            finalBitmap

        } catch (e: Exception) {
            AppLog.e("📸 捕获TextureView截图失败: $connectionId", e)
            null
        }
    }

    /**
     * 🎯 新增：捕获任意View的截图（支持ImageView、TextView等）
     * @param view 要截图的View
     * @param connectionId 连接ID
     * @param isCroppedWindow 是否为裁剪窗口
     * @param isMirrored 是否镜像
     * @param cropRectRatio 裁剪区域比例
     * @param applyTransforms 🎯 是否应用变换（裁剪、镜像），false时返回原始截图
     * @return 截图Bitmap，失败时返回null
     */
    fun captureViewScreenshot(
        view: View?,
        connectionId: String,
        isCroppedWindow: Boolean,
        isMirrored: Boolean,
        cropRectRatio: RectF?,
        applyTransforms: Boolean = true
    ): Bitmap? {
        return try {
            AppLog.d("📸 开始捕获View截图: $connectionId, 视图类型: ${view?.javaClass?.simpleName}")

            if (view == null) {
                AppLog.w("📸 View为null，无法截图: $connectionId")
                return null
            }

            // 确保View已经布局完成
            if (view.width <= 0 || view.height <= 0) {
                AppLog.w("📸 View尺寸无效，无法截图: $connectionId, 尺寸: ${view.width}x${view.height}")
                return null
            }

            // 第一步：从View获取原始截图
            val originalBitmap = captureViewToBitmap(view)
            if (originalBitmap == null) {
                AppLog.w("📸 View截图返回null: $connectionId")
                return null
            }

            AppLog.d("📸 View原始截图成功: $connectionId, 尺寸: ${originalBitmap.width}x${originalBitmap.height}")

            // 🎯 根据applyTransforms参数决定是否应用变换
            val finalBitmap = if (applyTransforms) {
                // 第二步：应用容器的最终变换状态
                applyContainerTransformsToBitmap(
                    originalBitmap,
                    connectionId,
                    isCroppedWindow,
                    isMirrored,
                    cropRectRatio
                )
            } else {
                // 🎯 返回原始截图，不应用任何变换（用于遥控端）
                AppLog.d("📸 View截图跳过变换，返回原始截图（用于遥控端可视化）: $connectionId")
                originalBitmap
            }

            // 🎯 修复：只有应用变换且结果不同时才释放原始截图
            if (applyTransforms && finalBitmap != originalBitmap) {
                originalBitmap.recycle()
            }

            if (finalBitmap != null) {
                if (applyTransforms) {
                    AppLog.d("📸 View截图成功（已变换）: $connectionId, 最终尺寸: ${finalBitmap.width}x${finalBitmap.height}")
                    AppLog.d("📸 应用的变换效果: 裁剪=$isCroppedWindow, 镜像=$isMirrored")
                } else {
                    AppLog.d("📸 View截图成功（原始）: $connectionId, 尺寸: ${finalBitmap.width}x${finalBitmap.height}")
                }
            }

            finalBitmap

        } catch (e: Exception) {
            AppLog.e("📸 捕获View截图失败: $connectionId", e)
            null
        }
    }

    /**
     * 🎯 将View转换为Bitmap的辅助方法
     */
    private fun captureViewToBitmap(view: View): Bitmap? {
        return try {
            // 创建与View尺寸相同的Bitmap
            val bitmap = createBitmap(
                view.width,
                view.height,
                Bitmap.Config.ARGB_8888
            )

            // 创建Canvas并绘制View
            val canvas = Canvas(bitmap)
            view.draw(canvas)

            AppLog.d("📸 View转Bitmap成功: ${view.javaClass.simpleName}, 尺寸: ${bitmap.width}x${bitmap.height}")
            bitmap

        } catch (e: Exception) {
            AppLog.e("📸 View转Bitmap失败: ${view.javaClass.simpleName}", e)
            null
        }
    }

    /**
     * 应用容器变换状态到Bitmap
     * 按照TextureView变换的相同顺序应用变换：裁剪 -> 镜像
     */
    private fun applyContainerTransformsToBitmap(
        sourceBitmap: Bitmap,
        connectionId: String,
        isCroppedWindow: Boolean,
        isMirrored: Boolean,

        cropRectRatio: RectF?
    ): Bitmap? {
        return try {
            AppLog.d("📸 开始应用容器变换: $connectionId")
            
            // 如果没有任何变换，直接返回原始截图
            if (!isCroppedWindow && !isMirrored) {
                AppLog.d("📸 无变换效果，返回原始截图: $connectionId")
                return sourceBitmap
            }
            
            var currentBitmap = sourceBitmap

            // 第二步：应用裁剪（如果启用）
            if (isCroppedWindow && cropRectRatio != null) {
                val croppedBitmap = cropBitmapWithRatio(currentBitmap, cropRectRatio)
                if (croppedBitmap != null) {
                    // 裁剪成功，更新当前bitmap（第一次不需要释放，因为currentBitmap就是sourceBitmap）
                    currentBitmap = croppedBitmap
                    AppLog.d("📸 裁剪完成: $connectionId")
                } else {
                    AppLog.w("📸 裁剪失败，保持当前状态: $connectionId")
                }
            }

            // 第三步：应用镜像变换（如果启用）
            if (isMirrored) {
                val mirroredBitmap = applyMirrorToBitmap(currentBitmap, connectionId)
                if (mirroredBitmap != null) {
                    // 如果之前进行了裁剪，需要释放裁剪后的bitmap
                    if (currentBitmap != sourceBitmap) {
                        currentBitmap.recycle()
                    }
                    currentBitmap = mirroredBitmap
                    AppLog.d("📸 镜像变换完成: $connectionId")
                } else {
                    AppLog.w("📸 镜像变换失败，保持当前状态: $connectionId")
                }
            }
            
            AppLog.d("📸 容器变换应用完成: $connectionId, 最终尺寸: ${currentBitmap.width}x${currentBitmap.height}")
            AppLog.d("📸 变换序列: 裁剪=$isCroppedWindow -> 镜像=$isMirrored")
            
            currentBitmap
            
        } catch (e: Exception) {
            AppLog.e("📸 应用容器变换失败: $connectionId", e)
            sourceBitmap // 失败时返回原始截图
        }
    }



    /**
     * 对Bitmap应用镜像变换
     * @param sourceBitmap 原始Bitmap
     * @param connectionId 连接ID
     * @return 镜像后的Bitmap，失败时返回null
     */
    private fun applyMirrorToBitmap(sourceBitmap: Bitmap, connectionId: String): Bitmap? {
        return try {
            AppLog.d("📸 开始对截图应用镜像变换: $connectionId, 原始尺寸: ${sourceBitmap.width}x${sourceBitmap.height}")

            // 创建与原始Bitmap相同尺寸的新Bitmap
            val mirroredBitmap = createBitmap(
                sourceBitmap.width,
                sourceBitmap.height,
                sourceBitmap.config ?: Bitmap.Config.ARGB_8888
            )

            // 创建Canvas用于绘制
            val canvas = Canvas(mirroredBitmap)

            // 创建镜像变换矩阵
            val mirrorMatrix = Matrix().apply {
                // 水平镜像：先移动到原点，水平翻转，再移回中心
                val centerX = sourceBitmap.width / 2f
                postTranslate(-centerX, 0f)  // 移动到原点
                postScale(-1f, 1f)           // 水平镜像
                postTranslate(centerX, 0f)   // 移回中心
            }

            // 使用变换矩阵绘制镜像后的图像
            val paint = Paint(Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG).apply {
                isFilterBitmap = true
                isDither = false  // 禁用抖动，保持原始质量
            }

            canvas.drawBitmap(sourceBitmap, mirrorMatrix, paint)

            AppLog.d("📸 截图镜像变换完成: $connectionId, 镜像后尺寸: ${mirroredBitmap.width}x${mirroredBitmap.height}")
            mirroredBitmap

        } catch (e: Exception) {
            AppLog.e("📸 对截图应用镜像变换失败: $connectionId", e)
            null
        }
    }

    /**
     * 根据裁剪比例从Bitmap中提取裁剪区域
     * @param sourceBitmap 原始Bitmap
     * @param cropRatio 裁剪区域比例 (left, top, right, bottom 都是0.0-1.0的相对值)
     * @return 裁剪后的Bitmap，失败时返回null
     */
    private fun cropBitmapWithRatio(sourceBitmap: Bitmap, cropRatio: RectF): Bitmap? {
        return try {
            val sourceWidth = sourceBitmap.width
            val sourceHeight = sourceBitmap.height

            // 计算裁剪区域的像素坐标
            val cropLeft = (cropRatio.left * sourceWidth).toInt().coerceIn(0, sourceWidth - 1)
            val cropTop = (cropRatio.top * sourceHeight).toInt().coerceIn(0, sourceHeight - 1)
            val cropRight = (cropRatio.right * sourceWidth).toInt().coerceIn(cropLeft + 1, sourceWidth)
            val cropBottom = (cropRatio.bottom * sourceHeight).toInt().coerceIn(cropTop + 1, sourceHeight)

            val cropWidth = cropRight - cropLeft
            val cropHeight = cropBottom - cropTop

            AppLog.d("📸 [裁剪计算] 原始尺寸: ${sourceWidth}x${sourceHeight}")
            AppLog.d("📸 [裁剪计算] 裁剪比例: left=${cropRatio.left}, top=${cropRatio.top}, right=${cropRatio.right}, bottom=${cropRatio.bottom}")
            AppLog.d("📸 [裁剪计算] 裁剪区域: ($cropLeft, $cropTop, $cropRight, $cropBottom)")
            AppLog.d("📸 [裁剪计算] 裁剪尺寸: ${cropWidth}x${cropHeight}")

            // 验证裁剪区域有效性
            if (cropWidth <= 0 || cropHeight <= 0) {
                AppLog.w("📸 [裁剪计算] 裁剪区域无效: ${cropWidth}x${cropHeight}")
                return null
            }

            // 创建裁剪后的Bitmap
            val croppedBitmap = Bitmap.createBitmap(
                sourceBitmap,
                cropLeft,
                cropTop,
                cropWidth,
                cropHeight
            )

            AppLog.d("📸 [裁剪计算] 裁剪成功: ${croppedBitmap.width}x${croppedBitmap.height}")
            croppedBitmap

        } catch (e: Exception) {
            AppLog.e("📸 [裁剪计算] 裁剪Bitmap失败", e)
            null
        }
    }
}
