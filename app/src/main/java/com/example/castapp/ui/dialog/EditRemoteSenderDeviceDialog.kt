package com.example.castapp.ui.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.example.castapp.R
import com.example.castapp.model.RemoteSenderConnection

/**
 * 编辑远程连接对话框
 * 发送端固定使用9999端口
 */
class EditRemoteSenderDeviceDialog(
    private val connection: RemoteSenderConnection,
    private val onConnectionEdited: (String, String, String) -> Unit
) : DialogFragment() {

    private lateinit var dialogTitle: TextView
    private lateinit var portInfoText: TextView
    private lateinit var deviceNameInput: EditText
    private lateinit var ipAddressInput: EditText
    private lateinit var confirmButton: Button
    private lateinit var cancelButton: Button

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.dialog_add_remote_device, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initViews(view)
        setupDialog()
        setupClickListeners()
    }

    private fun initViews(view: View) {
        dialogTitle = view.findViewById(R.id.dialog_title)
        portInfoText = view.findViewById(R.id.port_info_text)
        deviceNameInput = view.findViewById(R.id.device_name_input)
        ipAddressInput = view.findViewById(R.id.ip_address_input)
        confirmButton = view.findViewById(R.id.confirm_button)
        cancelButton = view.findViewById(R.id.cancel_button)

        // 设置发送端特定内容
        dialogTitle.text = "编辑发送端设备"
        portInfoText.text = "发送端固定使用端口 9999"

        // 预填充当前连接信息
        deviceNameInput.setText(connection.deviceName)
        ipAddressInput.setText(connection.ipAddress)

        // 修改确认按钮文字
        confirmButton.text = "保存"
    }

    private fun setupDialog() {
        dialog?.window?.let { window ->
            val displayMetrics = resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val dialogWidth = (screenWidth * 0.9).toInt().coerceAtLeast((320 * displayMetrics.density).toInt())

            window.setLayout(
                dialogWidth,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
        }
    }

    private fun setupClickListeners() {
        confirmButton.setOnClickListener {
            handleSaveConnection()
        }
        
        cancelButton.setOnClickListener {
            dismiss()
        }
    }

    private fun handleSaveConnection() {
        val deviceName = deviceNameInput.text.toString().trim()
        val ipAddress = ipAddressInput.text.toString().trim()

        // 验证输入
        if (deviceName.isEmpty()) {
            Toast.makeText(requireContext(), "请输入设备名称", Toast.LENGTH_SHORT).show()
            return
        }

        if (ipAddress.isEmpty()) {
            Toast.makeText(requireContext(), "请输入IP地址", Toast.LENGTH_SHORT).show()
            return
        }

        if (!RemoteSenderConnection.isValidIpAddress(ipAddress)) {
            Toast.makeText(requireContext(), "IP地址格式不正确", Toast.LENGTH_SHORT).show()
            return
        }

        if (!RemoteSenderConnection.isValidDeviceName(deviceName)) {
            Toast.makeText(requireContext(), "设备名称长度应在1-50字符之间", Toast.LENGTH_SHORT).show()
            return
        }

        // 回调编辑结果，固定使用9999端口
        onConnectionEdited(connection.id, ipAddress, deviceName)
        dismiss()
    }

    companion object {
        const val TAG = "EditRemoteConnectionDialog"
    }
}
