package com.example.castapp.ui.dialog

import android.app.AlertDialog
import android.content.Context
import android.view.LayoutInflater
import android.widget.EditText
import android.widget.TextView
import com.example.castapp.R
import com.example.castapp.manager.LayoutManager
import com.example.castapp.utils.ToastUtils
import com.example.castapp.model.CastWindowInfo
import com.example.castapp.utils.AppLog

/**
 * 保存布局对话框
 * 用于输入布局名称并保存当前窗口布局
 */
class SaveLayoutDialog(
    private val context: Context,
    private val windowInfoList: List<CastWindowInfo>,
    private val onSaveSuccess: (() -> Unit)? = null
) {
    
    private var dialog: AlertDialog? = null
    private val layoutManager = LayoutManager.getInstance()
    
    /**
     * 显示保存布局对话框
     */
    fun show() {
        try {
            val view = LayoutInflater.from(context).inflate(R.layout.dialog_save_director_layout, null)
            
            // 获取视图组件
            val layoutNameInput = view.findViewById<EditText>(R.id.layout_name_input)
            val windowCountText = view.findViewById<TextView>(R.id.window_count_text)
            
            // 设置窗口数量显示
            windowCountText.text = "当前有 ${windowInfoList.size} 个窗口将被保存"
            
            // 创建对话框
            val builder = AlertDialog.Builder(context)
                .setView(view)
                .setCancelable(true)
            
            dialog = builder.create()
            
            // 设置对话框按钮（通过自定义布局实现）
            setupDialogButtons(layoutNameInput)
            
            dialog?.show()
            
            AppLog.d("保存布局对话框已显示，当前窗口数: ${windowInfoList.size}")
            
        } catch (e: Exception) {
            AppLog.e("显示保存布局对话框失败", e)
            ToastUtils.showToast(context, "显示对话框失败")
        }
    }
    
    /**
     * 设置对话框按钮
     */
    private fun setupDialogButtons(layoutNameInput: EditText) {
        // 由于使用自定义布局，我们需要在布局中添加按钮
        // 这里我们通过编程方式添加按钮到对话框
        
        dialog?.setButton(AlertDialog.BUTTON_POSITIVE, "保存") { _, _ ->
            // 这个回调会被下面的代码覆盖
        }
        
        dialog?.setButton(AlertDialog.BUTTON_NEGATIVE, "取消") { _, _ ->
            AppLog.d("用户取消保存布局")
        }
        
        // 设置按钮点击监听器（防止自动关闭对话框）
        dialog?.setOnShowListener { dialogInterface ->
            val alertDialog = dialogInterface as AlertDialog
            
            val positiveButton = alertDialog.getButton(AlertDialog.BUTTON_POSITIVE)
            val negativeButton = alertDialog.getButton(AlertDialog.BUTTON_NEGATIVE)
            
            positiveButton.setOnClickListener {
                handleSaveLayout(layoutNameInput)
            }
            
            negativeButton.setOnClickListener {
                AppLog.d("用户取消保存布局")
                dialog?.dismiss()
            }
        }
    }
    
    /**
     * 处理保存布局逻辑
     */
    private fun handleSaveLayout(layoutNameInput: EditText) {
        val layoutName = layoutNameInput.text.toString().trim()
        
        // 验证输入
        if (layoutName.isEmpty()) {
            ToastUtils.showToast(context, "请输入布局名称")
            return
        }

        if (layoutName.length > 50) {
            ToastUtils.showToast(context, "布局名称不能超过50个字符")
            return
        }

        if (windowInfoList.isEmpty()) {
            ToastUtils.showToast(context, "当前没有窗口可以保存")
            dialog?.dismiss()
            return
        }
        
        // 禁用保存按钮，防止重复点击
        dialog?.getButton(AlertDialog.BUTTON_POSITIVE)?.isEnabled = false
        
        AppLog.d("开始保存布局: $layoutName, 窗口数: ${windowInfoList.size}")
        
        // 调用布局管理器保存布局
        layoutManager.saveLayout(layoutName, windowInfoList, context) { success, message ->
            // 重新启用保存按钮
            dialog?.getButton(AlertDialog.BUTTON_POSITIVE)?.isEnabled = true
            
            if (success) {
                AppLog.d("布局保存成功: $layoutName")
                ToastUtils.showToast(context, "布局保存成功")
                dialog?.dismiss()
                onSaveSuccess?.invoke()
            } else {
                AppLog.w("布局保存失败: $message")
                ToastUtils.showToast(context, message)
                // 保存失败时不关闭对话框，让用户可以修改布局名称
            }
        }
    }
    
    /**
     * 关闭对话框
     */
    fun dismiss() {
        dialog?.dismiss()
        dialog = null
    }
}
