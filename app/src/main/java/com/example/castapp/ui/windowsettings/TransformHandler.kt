package com.example.castapp.ui.windowsettings

import android.content.Context
import android.graphics.RectF
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.Surface
import android.view.View
import android.view.View.VISIBLE
import android.view.View.INVISIBLE
import android.view.View.GONE
import android.widget.FrameLayout
import kotlin.math.sqrt
import com.example.castapp.ui.windowsettings.interfaces.CropStateListener
import com.example.castapp.ui.windowsettings.interfaces.SurfaceStateListener
import com.example.castapp.ui.windowsettings.interfaces.TransformStateListener
import com.example.castapp.utils.AppLog

/**
 * 重构后的变换处理器（协调器模式）
 * 负责协调各个管理器组件，提供统一的对外接口
 */
class TransformHandler @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    initialWidth: Int = DEFAULT_WINDOW_WIDTH,
    initialHeight: Int = DEFAULT_WINDOW_HEIGHT
) : FrameLayout(context, attrs, defStyleAttr) {

    companion object {
        const val DEFAULT_WINDOW_WIDTH = 400
        const val DEFAULT_WINDOW_HEIGHT = 600
    }

    // 窗口尺寸（支持动态更新，特别是文本窗口）
    private var windowWidth: Int = initialWidth
    private var windowHeight: Int = initialHeight

    // 连接信息
    private var connectionId: String = ""

    // 管理器组件
    private val surfaceManager: SurfaceManager
    private val screenshotManager: ScreenshotManager
    private val transformRenderer: TransformRenderer
    private val transformManager: TransformManager
    private var cropManager: CropManager
    private var mediaSurfaceManager: MediaSurfaceManager? = null
    private var textWindowManager: TextWindowManager? = null

    // 🎯 移除：统一裁剪管理器已合并到CropManager中
    // private lateinit var unifiedCropManager: UnifiedCropManager

    // 手势检测器
    private val scaleGestureDetector: ScaleGestureDetector
    private val rotationGestureDetector: RotationGestureDetector


    // 手势状态
    private var isDragging = false
    private var isScaling = false
    private var isRotating = false
    private var lastTouchX = 0f
    private var lastTouchY = 0f

    // 边框调整大小状态
    private var isResizing = false
    private var resizeMode = ResizeMode.NONE
    private var borderTouchThreshold = 20f // 边框触摸区域阈值(dp)

    // 调整大小模式枚举
    private enum class ResizeMode {
        NONE, LEFT, RIGHT, TOP, BOTTOM,
        TOP_LEFT, TOP_RIGHT, BOTTOM_LEFT, BOTTOM_RIGHT
    }

    // 功能控制开关
    private var isDragEnabled = false
    private var isScaleEnabled = false
    private var isRotationEnabled = false
    private var isMirrored = false

    private var isBorderResizeEnabled = false // 边框调整大小功能开关

    // 外部回调
    private var surfaceAvailableCallback: ((Surface?, String) -> Unit)? = null
    private var transformChangeCallback: ((String, Float, Float, Float, Float) -> Unit)? = null

    init {
        // 固定窗口尺寸
        layoutParams = LayoutParams(windowWidth, windowHeight)

        // 初始化管理器组件
        surfaceManager = SurfaceManager(context, this)
        screenshotManager = ScreenshotManager()
        transformRenderer = TransformRenderer(context, this, windowWidth, windowHeight)
        transformManager = TransformManager(this, windowWidth, windowHeight)
        cropManager = CropManager(context, this, connectionId)

        // 🎯 设置CropManager的窗口尺寸（统一裁剪功能已合并）
        cropManager.setWindowSize(windowWidth, windowHeight)

        // 设置管理器间的监听器
        setupManagerListeners()

        // 初始化手势检测器
        scaleGestureDetector = ScaleGestureDetector(context, ScaleGestureListener())
        rotationGestureDetector = RotationGestureDetector(RotationGestureListener())

        AppLog.d("🎯 重构版TransformHandler初始化完成，固定尺寸: ${windowWidth}x${windowHeight}")
    }

    /**
     * 设置管理器间的监听器
     */
    private fun setupManagerListeners() {
        // Surface状态监听
        surfaceManager.setSurfaceStateListener(object : SurfaceStateListener {
            override fun onSurfaceAvailable(surface: Surface?, connectionId: String) {
                surfaceAvailableCallback?.invoke(surface, connectionId)
            }

            override fun onSurfaceDestroyed(connectionId: String) {
                surfaceAvailableCallback?.invoke(null, connectionId)
            }
        })

        // 变换状态监听
        transformManager.setTransformStateListener(object : TransformStateListener {
            override fun onTransformChanged(connectionId: String, x: Float, y: Float, scale: Float, rotation: Float) {
                transformChangeCallback?.invoke(connectionId, x, y, scale, rotation)
            }
        })

        // 🎯 集中化：设置WindowPositionManager引用，实现直接位置管理
        transformManager.positionManager = transformRenderer.getPositionManager()

        // 🎯 统一裁剪：设置裁剪状态提供者，让TransformManager能够获取当前裁剪状态
        transformManager.setCropStateProvider {
            Pair(cropManager.isCroppedWindow(), cropManager.getCropRectRatio())
        }

        // 🎯 统一裁剪：设置统一裁剪管理器的状态监听（现在使用CropManager）
        cropManager.setCropStateListener { cropRect ->
            // 裁剪区域变化时重新应用变换（如果需要）
            // 注意：统一裁剪管理器已经处理了实际的裁剪应用，这里只需要处理其他相关逻辑
            AppLog.d("🎯 [统一裁剪] 裁剪状态变化: $connectionId, 区域: $cropRect")
        }

        // 🎯 统一边框法：设置边框位置更新回调
        transformManager.setBorderPositionUpdateCallback {
            transformRenderer.updateUnifiedBorderPosition()
        }

        // 🎯 关键修复：设置圆角和边框更新回调（现在使用CropManager）
        cropManager.setCornerRadiusUpdateCallback {
            // 裁剪状态变化时，重新应用圆角和边框以适配新的显示区域
            transformRenderer.applyCornerRadiusForCrop()
            AppLog.d("🎯 [统一裁剪] 圆角和边框已更新适配裁剪区域: $connectionId")
        }

        // 保持原有CropManager的监听（用于UI交互）
        cropManager.setCropStateListener(object : CropStateListener {
            override fun onCropModeChanged(isCropping: Boolean) {
                // 裁剪模式变化时的处理
            }

            override fun onCropRectChanged(cropRect: RectF?) {
                // 🎯 统一裁剪：将裁剪变化委托给统一裁剪管理器（现在使用CropManager的统一方法）
                cropManager.applyCrop(cropRect)
            }
        })
    }

    /**
     * 为指定连接设置TextureView和Surface
     */
    fun setupForConnection(connectionId: String) {
        this.connectionId = connectionId

        // 🎯 关键修复：更新CropManager的连接ID并正确更新成员变量
        cropManager.cleanup()
        cropManager = CropManager(context, this, connectionId)
        cropManager.setWindowSize(windowWidth, windowHeight) // 设置窗口尺寸
        setupCropManagerListener(cropManager)

        // 🎯 统一裁剪：重新设置状态监听（现在使用CropManager）
        // 注意：这里不再重复设置监听器，避免覆盖setupCropManagerListener中设置的监听器

        // 设置Surface
        surfaceManager.setupTextureView(connectionId)

        // 🎯 根本性修复：初始化位置管理器（现在使用CropManager）
        transformRenderer.initializePositionManager(
            cropManager.isCroppedWindow(),
            cropManager.getCropRectRatio()
        )

        AppLog.d("为连接 $connectionId 设置完成")
    }

    /**
     * 为摄像头连接设置TextureView和Surface
     */
    fun setupForCameraConnection(cameraId: String, cameraName: String) {
        this.connectionId = cameraId

        // 更新CropManager的连接ID
        cropManager.cleanup()
        cropManager = CropManager(context, this, cameraId)
        cropManager.setWindowSize(windowWidth, windowHeight) // 🎯 修复：设置窗口尺寸
        setupCropManagerListener(cropManager)

        // 🎯 统一裁剪：设置状态监听（现在使用CropManager）
        // 注意：这里不再重复设置监听器，避免覆盖setupCropManagerListener中设置的监听器



        // 设置摄像头Surface
        surfaceManager.setupCameraTextureView(cameraId, cameraName)

        // 初始化位置管理器
        transformRenderer.initializePositionManager(
            cropManager.isCroppedWindow(),
            cropManager.getCropRectRatio()
        )

        AppLog.d("为摄像头 $cameraName (ID: $cameraId) 设置完成")
    }

    /**
     * 为媒体连接设置VideoView或ImageView
     */
    fun setupForMediaConnection(mediaId: String, fileName: String, uri: android.net.Uri, contentType: String) {
        this.connectionId = mediaId

        // 更新CropManager的连接ID
        cropManager.cleanup()
        cropManager = CropManager(context, this, mediaId)
        cropManager.setWindowSize(windowWidth, windowHeight) // 🎯 修复：设置窗口尺寸
        setupCropManagerListener(cropManager)

        // 🎯 统一裁剪：设置状态监听（现在使用CropManager）
        // 注意：这里不再重复设置监听器，避免覆盖setupCropManagerListener中设置的监听器



        // 创建MediaSurfaceManager
        mediaSurfaceManager = MediaSurfaceManager(context, this)

        // 根据内容类型设置相应的视图
        when (contentType) {
            "video" -> {
                mediaSurfaceManager?.setupVideoView(mediaId, fileName, uri)
            }
            "image" -> {
                mediaSurfaceManager?.setupImageView(mediaId, fileName, uri)
            }
            else -> {
                AppLog.w("不支持的媒体类型: $contentType")
            }
        }

        // 初始化位置管理器
        transformRenderer.initializePositionManager(
            cropManager.isCroppedWindow(),
            cropManager.getCropRectRatio()
        )

        AppLog.d("为媒体 $fileName (ID: $mediaId, 类型: $contentType) 设置完成")
    }

    /**
     * 为文本连接设置TextView
     */
    fun setupForTextConnection(textId: String, textContent: String) {
        this.connectionId = textId

        // 更新CropManager的连接ID
        cropManager.cleanup()
        cropManager = CropManager(context, this, textId)
        cropManager.setWindowSize(windowWidth, windowHeight)
        setupCropManagerListener(cropManager)



        // 创建TextWindowManager
        textWindowManager = TextWindowManager(context, this)

        // 设置文本内容
        textWindowManager?.setupTextView(textId, textContent)



        // 初始化位置管理器
        transformRenderer.initializePositionManager(
            cropManager.isCroppedWindow(),
            cropManager.getCropRectRatio()
        )

        AppLog.d("为文本 $textContent (ID: $textId) 设置完成")
    }

    /**
     * 设置CropManager监听器
     */
    private fun setupCropManagerListener(cropManager: CropManager) {
        cropManager.setCropStateListener(object : CropStateListener {
            override fun onCropModeChanged(isCropping: Boolean) {
                // 裁剪模式变化处理
            }

            override fun onCropRectChanged(cropRect: RectF?) {
                // 🎯 统一裁剪：将裁剪变化委托给统一裁剪管理器（现在使用CropManager）
                cropManager.applyCrop(cropRect)
                AppLog.d("🎯 [统一裁剪] 裁剪状态变化: $connectionId, 区域: $cropRect")
            }
        })

        // 🎯 关键修复：重新设置圆角和边框更新回调
        cropManager.setCornerRadiusUpdateCallback {
            // 裁剪状态变化时，重新应用圆角和边框以适配新的显示区域
            transformRenderer.applyCornerRadiusForCrop()
            AppLog.d("🎯 [统一裁剪] 圆角和边框已更新适配裁剪区域: $connectionId")
        }
    }

    /**
     * 设置设备信息
     */
    fun setDeviceInfo(deviceName: String?, ipAddress: String, port: Int) {
        cropManager.setDeviceInfo(deviceName, ipAddress, port)
        AppLog.d("🐾 设置设备信息: $connectionId, 设备名称=$deviceName, 地址=${ipAddress}:${port}")
    }

    /**
     * 应用所有变换
     */
    private fun applyTransforms() {
        val isCroppedWindow = cropManager.isCroppedWindow()
        val cropRectRatio = cropManager.getCropRectRatio()

        // 🎯 统一视图获取：优先获取TextureView（投屏窗口和视频媒体窗口都使用TextureView）
        val textureView = surfaceManager.getTextureView() ?: mediaSurfaceManager?.getTextureView()
        val targetView = mediaSurfaceManager?.getCurrentView() ?: textWindowManager?.let {
            // 用于图片媒体窗口的ImageView或文本窗口的TextView
            getChildAt(0)
        }

        transformRenderer.applyTransforms(
            textureView = textureView, // 统一的TextureView（投屏窗口或视频媒体窗口）
            targetView = targetView, // 图片媒体窗口的ImageView或文本窗口的TextView
            connectionId = connectionId,
            isCroppedWindow = isCroppedWindow,
            cropRectRatio = cropRectRatio,
            isMirrored = isMirrored,

            currentScaleFactor = transformManager.getCurrentScaleFactor(),
            currentRotation = transformManager.getCurrentRotation(),
            currentPivotX = transformManager.getCurrentPivotX(),
            currentPivotY = transformManager.getCurrentPivotY()
        )

        // 通知变换变化
        transformManager.notifyTransformChange(connectionId)

        // 🎯 统一边框法：变换完成后更新边框位置
        transformRenderer.updateUnifiedBorderPosition()
    }

    // ========== 手势处理 ==========

    override fun dispatchTouchEvent(event: MotionEvent): Boolean {
        // 🎯 优化：让所有事件都能正常分发，确保双击和拖动都能工作
        return super.dispatchTouchEvent(event)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        var handled = false



        // 优先让手势检测器处理事件
        if (isScaleEnabled) {
            handled = scaleGestureDetector.onTouchEvent(event) || handled
        }
        if (isRotationEnabled) {
            handled = rotationGestureDetector.onTouchEvent(event) || handled
        }

        // 如果正在缩放或旋转，不处理拖动
        if (isScaling || isRotating) {
            return true
        }

        // 处理拖动手势
        if (isDragEnabled) {
            when (event.action and MotionEvent.ACTION_MASK) {
                MotionEvent.ACTION_DOWN -> {
                    // 🎯 关键修复：检查触摸点是否在裁剪后的可见区域内
                    if (!isTouchInVisibleArea(event.x, event.y)) {
                        AppLog.d("🎯 触摸点在被裁剪区域内，拒绝处理拖动事件")
                        return false
                    }

                    // 🎯 修复：统一处理所有窗口类型的拖动开始
                    // 记录初始触摸位置，但不立即开始拖动（给双击检测留时间）
                    lastTouchX = event.rawX
                    lastTouchY = event.rawY

                    // 检测是否在边框区域（用于调整大小）
                    resizeMode = detectResizeMode(event.x, event.y)

                    if (textWindowManager != null) {
                        AppLog.d("🎯 文本窗口 - 记录触摸位置，准备拖动检测，调整模式: $resizeMode")
                    } else {
                        AppLog.d("🎯 记录触摸位置，准备拖动检测")
                    }

                    // 对于所有窗口类型，都先不开始拖动，在MOVE中根据距离判断
                    handled = true
                }

                MotionEvent.ACTION_POINTER_DOWN -> {
                    isDragging = false
                    isResizing = false
                    resizeMode = ResizeMode.NONE
                    AppLog.d("🎯 检测到多指触摸，停止拖动和调整大小")
                    handled = true
                }

                MotionEvent.ACTION_MOVE -> {
                    // 🎯 修复：统一的拖动阈值检测（适用于所有窗口类型）
                    if (!isDragging && !isResizing && event.pointerCount == 1) {
                        val deltaX = event.rawX - lastTouchX
                        val deltaY = event.rawY - lastTouchY
                        val distance = sqrt(deltaX * deltaX + deltaY * deltaY)

                        // 拖动阈值：文本窗口稍大一些，其他窗口使用标准阈值
                        val dragThreshold = if (textWindowManager != null) 25f else 15f

                        if (distance > dragThreshold) {
                            if (resizeMode != ResizeMode.NONE) {
                                isResizing = true
                                AppLog.d("🎯 检测到边框调整大小手势，开始调整大小 (模式: $resizeMode)")
                            } else {
                                isDragging = true
                                AppLog.d("🎯 检测到拖动手势，开始拖动 (距离: ${distance.toInt()}px)")
                            }
                        }
                    }

                    // 执行边框调整大小
                    if (isResizing && event.pointerCount == 1) {
                        val deltaX = event.rawX - lastTouchX
                        val deltaY = event.rawY - lastTouchY

                        performResize(deltaX, deltaY)

                        lastTouchX = event.rawX
                        lastTouchY = event.rawY

                        AppLog.v("🎯 调整大小中")
                        handled = true
                    }
                    // 执行拖动
                    else if (isDragging && event.pointerCount == 1) {
                        val deltaX = event.rawX - lastTouchX
                        val deltaY = event.rawY - lastTouchY

                        transformManager.updateTranslation(deltaX, deltaY)
                        applyTransforms()

                        lastTouchX = event.rawX
                        lastTouchY = event.rawY

                        AppLog.v("🎯 拖动中")
                        handled = true
                    }
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    if (isDragging) {
                        isDragging = false
                        transformManager.notifyTransformChangeImmediate(connectionId)
                        AppLog.d("🎯 拖动结束")
                    } else if (isResizing) {
                        isResizing = false
                        resizeMode = ResizeMode.NONE
                        AppLog.d("🎯 调整大小结束")
                    } else {
                        performClick()
                    }
                    handled = true
                }

                MotionEvent.ACTION_POINTER_UP -> {
                    if (event.pointerCount == 2) {
                        isDragging = true
                        lastTouchX = event.rawX
                        lastTouchY = event.rawY
                        AppLog.d("🎯 恢复单指拖动")
                    }
                    handled = true
                }
            }
        }

        return if (handled || isScaleEnabled || isRotationEnabled || isDragEnabled) {
            handled || super.onTouchEvent(event)
        } else {
            super.onTouchEvent(event)
        }
    }

    override fun performClick(): Boolean {
        super.performClick()
        return true
    }

    // ========== 手势检测器监听器 ==========

    private inner class ScaleGestureListener : ScaleGestureDetector.SimpleOnScaleGestureListener() {
        override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
            isScaling = true
            transformManager.setPivot(detector.focusX, detector.focusY)
            AppLog.d("🎯 缩放手势开始")
            return true
        }

        override fun onScale(detector: ScaleGestureDetector): Boolean {
            val scaleFactor = detector.scaleFactor
            if (transformManager.updateScale(scaleFactor)) {
                applyTransforms()
                transformManager.notifyTransformChange(connectionId)
            }
            AppLog.v("🎯 缩放中: 倍数=${transformManager.getCurrentScaleFactor()}")
            return true
        }

        override fun onScaleEnd(detector: ScaleGestureDetector) {
            isScaling = false
            transformManager.resetPivotToCenter()
            applyTransforms()
            transformManager.notifyTransformChangeImmediate(connectionId)
            AppLog.d("🎯 缩放手势结束")
        }
    }

    private inner class RotationGestureListener : RotationGestureDetector.OnRotationGestureListener {
        override fun onRotationBegin(detector: RotationGestureDetector): Boolean {
            isRotating = true
            transformManager.setPivot(detector.focusX, detector.focusY)
            AppLog.d("🎯 旋转手势开始")
            return true
        }

        override fun onRotate(detector: RotationGestureDetector, rotationDelta: Float): Boolean {
            transformManager.updateRotation(rotationDelta)
            applyTransforms()
            transformManager.notifyTransformChange(connectionId)
            AppLog.v("🎯 旋转中: 角度=${transformManager.getCurrentRotation()}°")
            return true
        }

        override fun onRotationEnd(detector: RotationGestureDetector) {
            isRotating = false
            transformManager.resetPivotToCenter()
            applyTransforms()
            transformManager.notifyTransformChangeImmediate(connectionId)
            AppLog.d("🎯 旋转手势结束")
        }
    }

    // ========== 自定义旋转手势检测器 ==========

    private class RotationGestureDetector(private val listener: OnRotationGestureListener) {
        private var prevAngle = 0f
        private var isInProgress = false
        var focusX = 0f
            private set
        var focusY = 0f
            private set

        interface OnRotationGestureListener {
            fun onRotationBegin(detector: RotationGestureDetector): Boolean
            fun onRotate(detector: RotationGestureDetector, rotationDelta: Float): Boolean
            fun onRotationEnd(detector: RotationGestureDetector)
        }

        fun onTouchEvent(event: MotionEvent): Boolean {
            var handled = false
            when (event.action and MotionEvent.ACTION_MASK) {
                MotionEvent.ACTION_POINTER_DOWN -> {
                    if (event.pointerCount == 2) {
                        prevAngle = getAngle(event)
                        focusX = (event.getX(0) + event.getX(1)) / 2f
                        focusY = (event.getY(0) + event.getY(1)) / 2f
                        isInProgress = listener.onRotationBegin(this)
                        handled = true
                    }
                }
                MotionEvent.ACTION_MOVE -> {
                    if (isInProgress && event.pointerCount == 2) {
                        val newAngle = getAngle(event)
                        var angleDelta = newAngle - prevAngle

                        if (angleDelta > 180) angleDelta -= 360
                        if (angleDelta < -180) angleDelta += 360

                        if (kotlin.math.abs(angleDelta) > 1f) {
                            listener.onRotate(this, angleDelta)
                            prevAngle = newAngle
                        }

                        focusX = (event.getX(0) + event.getX(1)) / 2f
                        focusY = (event.getY(0) + event.getY(1)) / 2f
                        handled = true
                    }
                }
                MotionEvent.ACTION_POINTER_UP -> {
                    if (isInProgress) {
                        listener.onRotationEnd(this)
                        isInProgress = false
                    }
                    handled = true
                }
            }
            return handled
        }

        private fun getAngle(event: MotionEvent): Float {
            val deltaX = event.getX(0) - event.getX(1)
            val deltaY = event.getY(0) - event.getY(1)
            return Math.toDegrees(kotlin.math.atan2(deltaY.toDouble(), deltaX.toDouble())).toFloat()
        }
    }

    // ========== 公共接口方法 ==========

    /**
     * 设置Surface可用回调
     */
    fun setSurfaceAvailableCallback(callback: (Surface?, String) -> Unit) {
        surfaceAvailableCallback = callback
    }

    /**
     * 设置变换变化回调
     */
    fun setTransformChangeCallback(callback: (String, Float, Float, Float, Float) -> Unit) {
        transformChangeCallback = callback
    }

    /**
     * 设置裁剪模式变化回调
     */
    fun setCropModeChangeCallback(callback: (Boolean) -> Unit) {
        cropManager.setCropModeChangeCallback(callback)
    }

    // ========== 拖动相关方法 ==========

    /**
     * 通过拖动手柄执行拖动（供TextWindowManager调用）
     */
    fun performDragByHandle(deltaX: Float, deltaY: Float) {
        transformManager.updateTranslation(deltaX, deltaY)
        applyTransforms()
        AppLog.v("🎯 拖动手柄执行拖动: deltaX=$deltaX, deltaY=$deltaY")
    }

    /**
     * 通知拖动结束（供TextWindowManager调用）
     */
    fun notifyDragEnd() {
        transformManager.notifyTransformChangeImmediate(connectionId)
        AppLog.d("🎯 拖动手柄结束，通知变换变化")
    }

    // ========== 功能开关方法 ==========

    fun setDragEnabled(enabled: Boolean) {
        isDragEnabled = enabled
        AppLog.d("🎯 拖动功能${if (enabled) "启用" else "禁用"}: $connectionId")
    }

    fun setScaleEnabled(enabled: Boolean) {
        isScaleEnabled = enabled
        AppLog.d("🎯 缩放功能${if (enabled) "启用" else "禁用"}: $connectionId")
    }

    fun setRotationEnabled(enabled: Boolean) {
        isRotationEnabled = enabled
        AppLog.d("🎯 旋转功能${if (enabled) "启用" else "禁用"}: $connectionId")
    }

    fun setMirrorEnabled(enabled: Boolean) {
        isMirrored = enabled
        applyTransforms()
        AppLog.d("🎯 镜像功能${if (enabled) "启用" else "禁用"}: $connectionId")
    }



    // ========== 功能状态查询 ==========

    fun isDragEnabled(): Boolean = isDragEnabled
    fun isScaleEnabled(): Boolean = isScaleEnabled
    fun isRotationEnabled(): Boolean = isRotationEnabled
    fun isMirrorEnabled(): Boolean = isMirrored

    /**
     * 📝 检查编辑功能是否启用（仅文本窗口）
     */
    fun isEditEnabled(): Boolean {
        // 只有文本窗口才支持编辑功能
        if (!connectionId.startsWith("text_")) {
            return false
        }

        // 通过WindowSettingsManager获取编辑状态
        return try {
            val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()
            windowSettingsManager.getEditState(connectionId)
        } catch (e: Exception) {
            AppLog.e("【TransformHandler】获取编辑状态失败: $connectionId", e)
            false
        }
    }


    /**
     * 获取连接ID
     */
    fun getConnectionId(): String = connectionId

    /**
     * 📝 获取文本窗口管理器
     */
    fun getTextWindowManager(): TextWindowManager? = textWindowManager

    /**
     * 🎬 获取媒体Surface管理器
     */
    fun getMediaSurfaceManager(): MediaSurfaceManager? = mediaSurfaceManager

    // ========== 变换状态查询 ==========

    fun getActualDisplayX(): Float = transformManager.getActualDisplayX()
    fun getActualDisplayY(): Float = transformManager.getActualDisplayY()

    // 🎯 新增：获取容器位置（用于遥控端可视化）
    fun getContainerDisplayX(): Float = transformManager.getContainerDisplayX()
    fun getContainerDisplayY(): Float = transformManager.getContainerDisplayY()

    fun getCurrentScaleFactor(): Float = transformManager.getCurrentScaleFactor()
    fun getCurrentRotation(): Float = transformManager.getCurrentRotation()

    // ========== 精准变换控制 ==========

    fun setPrecisionTransform(x: Float, y: Float, scale: Float, rotation: Float) {
        transformManager.setPrecisionTransform(x, y, scale, rotation)
        applyTransforms()
        AppLog.d("🎯 精准变换已设置: 连接=$connectionId")
    }

    fun resetTransform() {
        transformManager.resetTransform()
        applyTransforms()
        AppLog.d("🎯 变换已重置: 连接=$connectionId")
    }

    // ========== 裁剪功能 ==========

    fun startCropMode() {
        // 🎯 统一裁剪：使用统一裁剪管理器的状态（现在使用CropManager）
        transformManager.saveCurrentTransformState(cropManager.isCroppedWindow(), cropManager.getCropRectRatio())

        // 🎯 裁剪模式边框管理：进入裁剪模式时临时移除边框
        transformRenderer.onEnterCropMode()

        cropManager.startCropMode()
        AppLog.d("🎯 开始裁剪模式: $connectionId")
    }

    fun endCropMode(isCancel: Boolean = false) {
        if (isCancel) {
            // 🎯 修复：恢复变换状态（位置、缩放等），但不设置裁剪状态
            // 裁剪状态由CropManager自己处理，避免冲突
            transformManager.restoreSavedTransformState()
        }

        // 🎯 修复：先恢复边框状态，再结束裁剪模式，避免时序问题
        transformRenderer.onExitCropMode()

        cropManager.endCropMode(isCancel)

        AppLog.d("结束裁剪模式: $connectionId, 是否取消: $isCancel")
    }

    fun isCroppingMode(): Boolean = cropManager.isCroppingMode()

    // 🎯 统一裁剪：从统一裁剪管理器获取裁剪状态（现在使用CropManager）
    fun getCropRectRatio(): RectF? = cropManager.getCropRectRatio()

    fun setCropRectRatio(rectRatio: RectF?) {
        // 🎯 统一裁剪：现在只需要更新CropManager（已包含统一裁剪功能）
        cropManager.setCropRectRatio(rectRatio)
        AppLog.d("【布局恢复】设置裁剪区域: $connectionId, 区域: $rectRatio")
    }

    // ========== UI效果控制 ==========

    fun setCornerRadius(radius: Float) {
        transformRenderer.setCornerRadius(radius)
        AppLog.d("🎯 圆角半径设置为: ${radius}dp")
    }

    fun getCornerRadius(): Float = transformRenderer.getCornerRadius()

    fun setWindowAlpha(alphaValue: Float) {
        transformRenderer.setWindowAlpha(alphaValue)
        AppLog.d("🎯 透明度设置为: ${(alphaValue * 100).toInt()}%")
    }

    fun getWindowAlpha(): Float = transformRenderer.getWindowAlpha()

    fun setBorderEnabled(enabled: Boolean) {
        transformRenderer.setBorderEnabled(enabled)
        AppLog.d("🎯 边框显示状态设置为: $enabled")
    }

    fun isBorderEnabled(): Boolean = transformRenderer.isBorderEnabled()

    fun setBorderColor(color: Int) {
        transformRenderer.setBorderColor(color)
        AppLog.d("🎨 边框颜色设置为: ${String.format("#%06X", 0xFFFFFF and color)}")
    }

    fun getBorderColor(): Int = transformRenderer.getBorderColor()

    fun setBorderWidth(width: Float) {
        transformRenderer.setBorderWidth(width)
        AppLog.d("🎯 边框宽度设置为: ${width}dp")
    }

    fun getBorderWidth(): Float = transformRenderer.getBorderWidth()

    // ========== 层级管理 ==========

    /**
     * 🎯 统一边框法：重写bringToFront方法，确保边框视图同步调整层级
     */
    override fun bringToFront() {
        // 首先调用父类方法，将窗口容器移到前台
        super.bringToFront()

        // 然后将边框视图也移到前台，确保边框始终在窗口上方
        transformRenderer.bringBorderToFront()

        AppLog.d("🎯 [统一边框法] 窗口和边框已同步调整到前台: $connectionId")
    }

    /**
     * 🎯 统一边框法：重写setVisibility方法，确保边框视图同步显示/隐藏
     */
    override fun setVisibility(visibility: Int) {
        // 首先调用父类方法，设置窗口容器的可见性
        super.setVisibility(visibility)

        // 然后同步设置边框视图的可见性
        transformRenderer.syncBorderVisibility(visibility)

        AppLog.d("🎯 [统一边框法] 窗口和边框可见性已同步: $connectionId, visibility=${when(visibility) {
            VISIBLE -> "VISIBLE"
            INVISIBLE -> "INVISIBLE"
            GONE -> "GONE"
            else -> "UNKNOWN($visibility)"
        }}")
    }

    // ========== 窗口尺寸查询和更新 ==========

    fun getBaseWindowWidth(): Int = windowWidth
    fun getBaseWindowHeight(): Int = windowHeight

    /**
     * 更新基础窗口尺寸（主要用于文本窗口尺寸变化）
     * 🎯 统一边框法：移除对TransformRenderer原始尺寸的更新，因为不再需要容器扩展
     */
    fun updateBaseWindowSize(newWidth: Int, newHeight: Int) {
        windowWidth = newWidth
        windowHeight = newHeight

        // 同步更新CropManager的窗口尺寸
        cropManager.setWindowSize(windowWidth, windowHeight)

        AppLog.d("🎯 基础窗口尺寸已更新: ${windowWidth}x${windowHeight} (统一边框法)")
    }

    // ========== 截图功能 ==========

    fun captureScreenshot(forRemoteControl: Boolean = false): android.graphics.Bitmap? {
        return try {
            AppLog.d("📸 开始截图窗口: $connectionId")

            // 🎯 根据窗口类型选择正确的截图方式
            val screenshot = when {
                // 1. 投屏窗口或摄像头窗口：使用SurfaceManager的TextureView
                surfaceManager.getTextureView() != null -> {
                    AppLog.d("📸 使用投屏/摄像头TextureView截图: $connectionId, 遥控端请求: $forRemoteControl")
                    screenshotManager.captureScreenshot(
                        textureView = surfaceManager.getTextureView(),
                        connectionId = connectionId,
                        isCroppedWindow = cropManager.isCroppedWindow(),
                        isMirrored = isMirrored,
                        cropRectRatio = cropManager.getCropRectRatio(),
                        applyTransforms = !forRemoteControl  // 🎯 遥控端请求时不应用变换
                    )
                }

                // 2. 视频媒体窗口：使用MediaSurfaceManager的TextureView
                mediaSurfaceManager?.getTextureView() != null -> {
                    AppLog.d("📸 使用视频媒体TextureView截图: $connectionId, 遥控端请求: $forRemoteControl")
                    screenshotManager.captureScreenshot(
                        textureView = mediaSurfaceManager?.getTextureView(),
                        connectionId = connectionId,
                        isCroppedWindow = cropManager.isCroppedWindow(),
                        isMirrored = isMirrored,
                        cropRectRatio = cropManager.getCropRectRatio(),
                        applyTransforms = !forRemoteControl  // 🎯 遥控端请求时不应用变换
                    )
                }

                // 3. 图片媒体窗口：使用MediaSurfaceManager的ImageView
                mediaSurfaceManager?.getCurrentView() != null -> {
                    AppLog.d("📸 使用图片媒体ImageView截图: $connectionId, 遥控端请求: $forRemoteControl")
                    screenshotManager.captureViewScreenshot(
                        view = mediaSurfaceManager?.getCurrentView(),
                        connectionId = connectionId,
                        isCroppedWindow = cropManager.isCroppedWindow(),
                        isMirrored = isMirrored,
                        cropRectRatio = cropManager.getCropRectRatio(),
                        applyTransforms = !forRemoteControl  // 🎯 遥控端请求时不应用变换
                    )
                }

                // 4. 文本窗口：使用TextWindowManager的TextView
                textWindowManager != null && childCount > 0 -> {
                    val textView = getChildAt(0) // TextWindowView
                    AppLog.d("📸 使用文本窗口TextView截图: $connectionId, 遥控端请求: $forRemoteControl")
                    screenshotManager.captureViewScreenshot(
                        view = textView,
                        connectionId = connectionId,
                        isCroppedWindow = cropManager.isCroppedWindow(),
                        isMirrored = isMirrored,
                        cropRectRatio = cropManager.getCropRectRatio(),
                        applyTransforms = !forRemoteControl  // 🎯 遥控端请求时不应用变换
                    )
                }

                else -> {
                    AppLog.w("📸 无法确定窗口类型，截图失败: $connectionId")
                    null
                }
            }

            if (screenshot != null) {
                AppLog.d("📸 窗口截图成功: $connectionId, 尺寸: ${screenshot.width}x${screenshot.height}")
            } else {
                AppLog.w("📸 窗口截图失败: $connectionId")
            }

            screenshot

        } catch (e: Exception) {
            AppLog.e("📸 窗口截图异常: $connectionId", e)
            null
        }
    }

    // ========== 边框调整大小功能 ==========

    /**
     * 检查触摸点是否在裁剪后的可见区域内
     * @param x 触摸点X坐标（相对于TransformHandler容器）
     * @param y 触摸点Y坐标（相对于TransformHandler容器）
     * @return true表示在可见区域内，false表示在被裁剪区域内
     */
    private fun isTouchInVisibleArea(x: Float, y: Float): Boolean {
        // 🎯 获取当前裁剪状态
        val (isCroppedWindow, cropRectRatio) = transformManager.getCropStateProvider()?.invoke() ?: Pair(false, null)

        // 如果窗口未裁剪，所有区域都可触摸
        if (!isCroppedWindow || cropRectRatio == null) {
            return true
        }

        // 🎯 计算裁剪区域的实际像素坐标
        val containerWidth = this.width.toFloat()
        val containerHeight = this.height.toFloat()

        val cropLeft = cropRectRatio.left * containerWidth
        val cropTop = cropRectRatio.top * containerHeight
        val cropRight = cropRectRatio.right * containerWidth
        val cropBottom = cropRectRatio.bottom * containerHeight

        // 检查触摸点是否在裁剪区域内
        val isInCropArea = x >= cropLeft && x <= cropRight && y >= cropTop && y <= cropBottom

        AppLog.d("🎯 触摸点检测: (${x.toInt()}, ${y.toInt()}), 裁剪区域: (${cropLeft.toInt()}, ${cropTop.toInt()}, ${cropRight.toInt()}, ${cropBottom.toInt()}), 结果: $isInCropArea")

        return isInCropArea
    }

    /**
     * 检测触摸点是否在边框区域
     */
    private fun detectResizeMode(x: Float, y: Float): ResizeMode {
        if (!isBorderResizeEnabled) return ResizeMode.NONE

        val threshold = borderTouchThreshold * context.resources.displayMetrics.density
        val width = this.width.toFloat()
        val height = this.height.toFloat()

        val isLeft = x <= threshold
        val isRight = x >= width - threshold
        val isTop = y <= threshold
        val isBottom = y >= height - threshold

        return when {
            isLeft && isTop -> ResizeMode.TOP_LEFT
            isRight && isTop -> ResizeMode.TOP_RIGHT
            isLeft && isBottom -> ResizeMode.BOTTOM_LEFT
            isRight && isBottom -> ResizeMode.BOTTOM_RIGHT
            isLeft -> ResizeMode.LEFT
            isRight -> ResizeMode.RIGHT
            isTop -> ResizeMode.TOP
            isBottom -> ResizeMode.BOTTOM
            else -> ResizeMode.NONE
        }
    }

    /**
     * 执行边框调整大小
     */
    private fun performResize(deltaX: Float, deltaY: Float) {
        val layoutParams = this.layoutParams
        val minWidth = 100 * context.resources.displayMetrics.density // 最小宽度100dp
        val minHeight = 60 * context.resources.displayMetrics.density // 最小高度60dp

        var newWidth = layoutParams.width.toFloat()
        var newHeight = layoutParams.height.toFloat()
        var newX = this.x
        var newY = this.y

        when (resizeMode) {
            ResizeMode.LEFT -> {
                val proposedWidth = newWidth - deltaX
                if (proposedWidth >= minWidth) {
                    newWidth = proposedWidth
                    newX += deltaX
                }
            }
            ResizeMode.RIGHT -> {
                newWidth = (newWidth + deltaX).coerceAtLeast(minWidth)
            }
            ResizeMode.TOP -> {
                val proposedHeight = newHeight - deltaY
                if (proposedHeight >= minHeight) {
                    newHeight = proposedHeight
                    newY += deltaY
                }
            }
            ResizeMode.BOTTOM -> {
                newHeight = (newHeight + deltaY).coerceAtLeast(minHeight)
            }
            ResizeMode.TOP_LEFT -> {
                val proposedWidth = newWidth - deltaX
                val proposedHeight = newHeight - deltaY
                if (proposedWidth >= minWidth) {
                    newWidth = proposedWidth
                    newX += deltaX
                }
                if (proposedHeight >= minHeight) {
                    newHeight = proposedHeight
                    newY += deltaY
                }
            }
            ResizeMode.TOP_RIGHT -> {
                newWidth = (newWidth + deltaX).coerceAtLeast(minWidth)
                val proposedHeight = newHeight - deltaY
                if (proposedHeight >= minHeight) {
                    newHeight = proposedHeight
                    newY += deltaY
                }
            }
            ResizeMode.BOTTOM_LEFT -> {
                val proposedWidth = newWidth - deltaX
                if (proposedWidth >= minWidth) {
                    newWidth = proposedWidth
                    newX += deltaX
                }
                newHeight = (newHeight + deltaY).coerceAtLeast(minHeight)
            }
            ResizeMode.BOTTOM_RIGHT -> {
                newWidth = (newWidth + deltaX).coerceAtLeast(minWidth)
                newHeight = (newHeight + deltaY).coerceAtLeast(minHeight)
            }
            else -> return
        }

        // 应用新的尺寸和位置
        if (newWidth != layoutParams.width.toFloat() || newHeight != layoutParams.height.toFloat() ||
            newX != this.x || newY != this.y) {

            layoutParams.width = newWidth.toInt()
            layoutParams.height = newHeight.toInt()
            this.layoutParams = layoutParams
            this.x = newX
            this.y = newY

            // 通知文本窗口管理器尺寸变化
            textWindowManager?.let { manager ->
                AppLog.d("🎯 通知文本窗口尺寸变化: ${newWidth.toInt()}x${newHeight.toInt()}")
            }

            AppLog.d("🎯 边框调整大小完成: ${newWidth.toInt()}x${newHeight.toInt()}, 位置=(${newX}, ${newY})")
        }
    }

    // ========== 清理资源 ==========

    fun cleanup() {
        try {
            AppLog.d("开始清理重构版TransformHandler: connectionId=$connectionId")

            // 清理各个管理器
            surfaceManager.cleanup()
            mediaSurfaceManager?.cleanup()
            textWindowManager?.cleanup()

            transformRenderer.cleanup()
            transformManager.cleanup()
            cropManager.cleanup()

            // 清理回调
            surfaceAvailableCallback = null
            transformChangeCallback = null

            // 清理连接ID
            val oldConnectionId = connectionId
            connectionId = ""

            AppLog.d("重构版TransformHandler清理完成: oldConnectionId=$oldConnectionId")

        } catch (e: Exception) {
            AppLog.e("重构版TransformHandler清理时发生异常", e)
        }
    }

    // ========== 🎯 横竖屏适配功能 ==========

    /**
     * 设置视频方向并应用变换
     */
    fun setVideoOrientation(orientation: Int, videoWidth: Int = 0, videoHeight: Int = 0) {
        transformRenderer.setVideoOrientation(orientation, videoWidth, videoHeight)
        // 重新应用变换以生效方向适配
        applyTransforms()
        AppLog.d("🎯 视频方向已设置并应用变换: $connectionId, 方向: ${getOrientationName(orientation)}, 分辨率: ${videoWidth}×${videoHeight}")
    }



    /**
     * 获取方向名称（用于日志）
     */
    private fun getOrientationName(orientation: Int): String {
        return when (orientation) {
            android.content.res.Configuration.ORIENTATION_LANDSCAPE -> "横屏"
            android.content.res.Configuration.ORIENTATION_PORTRAIT -> "竖屏"
            else -> "未知($orientation)"
        }
    }
}
